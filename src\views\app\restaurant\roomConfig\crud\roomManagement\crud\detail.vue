<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="detail"
      :editRedirectConfig="editRedirectConfig"
      @onSubmit="onBasicSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    id: route.query.id // 从路由获取id参数
  },
  curl: {
    info: '/private_room/get_info', // 获取详情接口
    del: '/private_room/post_del' // 删除接口
  },
  groupForm: [
    [0, 1],
    [1, 4]
  ],
  groupForm: [
      [0, 2],
      [2, 3],
      [3, 4],
      [4, 5],
    ],
   form: [
    {
      label: "名称",
      key: "title",
      component: "yhc-input",
      required: true,
      // 不允许输入
      disabled: true,
      rules: [{ required: true, message: "请填写包间名称" }],
    },
    {
      label: "人数",
      key: "capacity",
      component: "yhc-stepper",
      disabled: true,
      // default: 2, // 默认值
      min: 1, // 最小值
      // max: 10, // 最大值
      step: 1, // 步长
      theme: "default" // 样式 round:圆角 ，default：方形
    },
    {
      label: "标签",
      key: "tags",
      component: "yhc-tag-selector",
      // required: true,
      disabled: true,
      opts: {
        url: "/private_room_tag/get_all",
        text_key: "title",
        contrast_key: "title",
        maxTagLength: 12,
        maxCustomTags: 15,
        defaultList: [
        ],
        // keyMap: "title",
      },
      
      defaultSelected: [],
    },
    {
      label: "地址",
      key: "address",
      component: "yhc-input",
      disabled: true,
    },
    {
      label: "图片",
      key: "image",
      component: "yhc-select-image",
      disabled: true,
    },
  ]
}

// 修改按钮跳转配置
const editRedirectConfig = {
  path: '/roomConfig/roomManagement/add', // 跳转到新增页面进行编辑
  query: {
    id: route.query.id, // 传递id参数
    from: 'detail' // 标识来源
  }
}
const { proxy } = getCurrentInstance();
const setRight = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title:'包间详情',
  });
};
setRight()
// 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
}
</style>
