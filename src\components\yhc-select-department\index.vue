<template>
  <div class="wrapper">
    <van-field v-model="text" is-link readonly :name="config.key" :label="config.label"
      :placeholder="config.placeholder" :border="config.border" :rules="config.rules" :disabled="config.disabled"
      :input-align="config.inputAlign" :required="config.required" @click="onClick" />
  </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance } from "vue";
import { deepAssign } from "@/untils";
import { showToast } from "vant";
import { useLoginStore } from "@/store/dingLogin";

const app = useLoginStore();
const emits = defineEmits(["change"]);
const { proxy } = getCurrentInstance();

// 默认配置
let config = {
  // 基础配置
  label: "选择部门",       // 字段标签 (字符串) - 显示在输入框左侧的标签文字
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"departments", "selected_depts"
  placeholder: "请选择",    // 占位符 (字符串) - 未选择部门时显示的提示文字
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用选择, false: 可正常选择
  rules: [],               // 验证规则 (数组) - 表单验证规则配置

  // 选择器配置
  type: "departmentPicker", // 选择器类型 (字符串) - "departmentPicker": 部门选择器
  multiple: true,          // 是否支持多选 (布尔值) - true: 支持选择多个部门, false: 单选模式
  maxDepartments: 100,     // 最大选择部门数量 (数字) - 限制最多可选择的部门数量
  showOrgEcological: true, // 是否显示组织生态 (布尔值) - true: 显示组织架构, false: 仅显示部门列表

  // 样式配置
  border: true,            // 是否显示边框 (布尔值) - true: 显示下边框, false: 无边框
  inputAlign: "left",      // 输入内容对齐方式 (字符串) - "left": 左对齐, "center": 居中, "right": 右对齐
};

let text = ref("");
let selectedDepartments = []; // 存储选中的部门信息

const props = defineProps({
  config: Object,
  form: Object,
});

// 合并配置
props.config && deepAssign(config, props.config);

// 设置显示文本的函数
const setText = (departments) => {
  if (departments.length === 0) {
    text.value = "";
    return;
  }

  if (departments.length === 1) {
    text.value = departments[0].name;
  } else if (departments.length <= 3 && app.browserEnv !== "wx") {
    text.value = departments.map((dept) => dept.name).join("、");
  } else {
    text.value = `已选择${departments.length}个部门`;
  }
};

// 初始化已选择的部门
if (props.form[config.key]) {
  if (typeof props.form[config.key] === 'object' && props.form[config.key].departments) {
    // 标准格式：{ departments: [...] }
    const { departments = [] } = props.form[config.key];
    selectedDepartments = departments;
    setText(departments);
    console.log("部门回显数据(标准格式):", departments);
  } else if (typeof props.form[config.key] === 'string') {
    // 兼容字符串格式
    try {
      const parsed = JSON.parse(props.form[config.key]);
      if (parsed.departments) {
        selectedDepartments = parsed.departments;
        setText(parsed.departments);
        console.log("部门回显数据(字符串格式):", parsed.departments);
      }
    } catch (e) {
      console.warn("部门数据解析失败:", props.form[config.key]);
    }
  }
}

// 点击选择部门
const onClick = () => {
  if (config.disabled) return;

  // 获取已选择的部门ID列表
  let selectedDepartmentIds = selectedDepartments
    ? selectedDepartments.map((item) => item.dept_id)
    : [];

  if (app.browserEnv === "wx") {
    // 企业微信环境
    proxy.$_dd.selectEnterpriseContact({
      fromDepartmentId: 0,
      mode: config.multiple ? "multi" : "single",
      type: ["department"], // 只选择部门
      selectedDepartmentIds: selectedDepartmentIds,
      success (res) {
        console.log("企业微信部门选择结果:", res);
        res = res.result;

        selectedDepartments = res.departmentList.map((dept) => {
          return {
            dept_id: dept.id,
            name: dept.name || "",
            order: dept.order || 0,
          };
        });

        setText(selectedDepartments);

        // 更新表单数据
        props.form[config.key] = {
          departments: selectedDepartments,
        };

        emits("change", props.form);
      },
      fail (err) {
        console.error("企业微信部门选择失败:", err);
        showToast("选择部门失败");
      }
    });
  } else {
    // 钉钉环境 - 使用complexChoose API，只选择部门
    proxy.$_dd.ready(function () {
      proxy.$_dd.biz.contact.departmentsPicker({
        title: config.label, // 标题
        corpId: app.corpId, // 企业的corpId
        multiple: config.multiple, // 是否多选
        maxUsers: 0, // 不选择用户
        maxDepartments: config.maxDepartments, // 最大选择部门数量
        pickedUsers: [], // 不选择用户
        pickedDepartments: selectedDepartmentIds, // 已选部门
        appId: app.appid, // 微应用的Id
        showOrgEcological: config.showOrgEcological, // 是否显示组织生态
        showLabelPick: true, // 显示标签选择
        rootPage: "CommonOrgContact", // 根页面
        onSuccess: function (result) {
          console.log("钉钉部门选择结果:", result);

          // 只处理部门数据，忽略用户数据
          selectedDepartments = result.departments.map((dept) => {
            return {
              dept_id: dept.id,
              name: dept.name,
              order: dept.order || 0,
              parentId: dept.parentId || null,
            };
          });

          setText(selectedDepartments);

          // 更新表单数据
          props.form[config.key] = {
            departments: selectedDepartments,
          };

          emits("change", props.form);
        },
        onFail: function (err) {
          console.error("钉钉部门选择失败:", err);
          // showToast("选择部门失败");
        },
      }).catch((err) => {
        console.error("钉钉API调用失败:", err);
        // showToast("选择部门失败");
      });
    });
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  /* 保持与其他组件一致的样式 */
}
</style>
