<template>
  <div class="edit-group-container">
    <!-- 页面标题 -->
    <!-- <van-nav-bar title="编辑设备组"  /> -->

    <!-- 设备组名称 -->
    <div class="group-name-section">
      <van-cell-group inset>
        <van-field
          v-model="groupForm.group_title"
          label="设备组名称"
          placeholder="请输入设备组名称"
          :rules="[{ required: true, message: '请输入设备组名称' }]"
        />
      </van-cell-group>
    </div>

    <!-- 添加设备按钮 -->
    <div class="add-device-section">
      <van-cell-group inset>
        <van-cell
          @click="showAddDeviceOptions"
        >
          <template #title>
            <div class="add-device-title">
              <div class="add-icon">
                <van-icon name="plus" />
              </div>
              <span>添加设备</span>
            </div>
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 设备列表 -->
    <div v-if="groupDevices.length > 0" class="devices-section">
      <van-cell-group inset>
        <van-cell title="设备" :value="`(${groupDevices.length})`">
          <template #right-icon>
            <van-icon name="arrow-down" @click="toggleDeviceList" />
          </template>
        </van-cell>
        
        <div v-show="showDeviceList" class="device-list">
          <div
            v-for="device in groupDevices"
            :key="device.id"
            class="device-item"
          >
            <div class="device-info">
              <span class="device-prefix">L</span>
              <span class="device-name">{{ device.title }}</span>
            </div>
            <van-button
              type="primary"
              size="mini"
              plain
              @click="removeDevice(device.id)"
            >
              移除
            </van-button>
          </div>
        </div>
      </van-cell-group>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-actions">
      <van-button
        type="default"
        size="large"
        @click="deleteGroup"
      >
        删除
      </van-button>
      <van-button
        type="primary"
        size="large"
        :loading="saving"
        @click="saveGroup"
      >
        保存
      </van-button>
    </div>

    <!-- 添加设备选项弹窗 -->
    <van-action-sheet
      v-model:show="showAddOptions"
      :actions="addActions"
      @select="onAddActionSelect"
      cancel-text="取消"
      title="添加设备"
    />

    <!-- 删除确认弹窗 -->
    <van-dialog
      v-model:show="showDeleteDialog"
      title="删除设备组"
      message="确定要删除这个设备组吗？删除后无法恢复。"
      show-cancel-button
      @confirm="confirmDelete"
    />
  </div>
</template>

<script setup>
import { ref,  onMounted,getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'
const { proxy } = getCurrentInstance();
// 路由实例
const route = useRoute()
const router = useRouter()
console.log('当前路由参数:', route.query)
// function getDetail(){
//   proxy.$get('/device/get_group_info', { id: route.query.id }).then((res) => {
//     console.log('设备组详情:', res)
//     if(res.code == 200){
//       groupForm.value = res.data
//       groupDevices.value = res.data.devices?JSON.parse(res.data.devices):[]
//       console.log('获取设备组详情:', groupForm.value)
//     } else {
//       showToast(res.msg || '获取设备组详情失败，请稍后重试')
//     }
//   })
// }
function getDetail(){
  proxy.$get('/device/get_nodes', {id:route.query.id,type:route.query.type}).then((res) => { 
    if(res.code === 200){
      console.log('获取设备详情:', res)
      groupForm.value = res.data
      console.log('获取设备详情:111', groupForm.value)
      if(res.data.result.length > 0){
        res.data.result.forEach((item) => {
          groupDevices.value.push({
            id: item.id,
            title: item.title,
          })
        })
      } else {
        groupDevices.value = []
      }
      // groupDevices.value = res.data.result
      // 初始化编辑表单
      // initEditForm()
    } else {
      showToast(res.msg || '获取设备详情失败，请稍后重试')
    }
  })
}
// 页面状态
const saving = ref(false)
const showDeviceList = ref(true)
const showAddOptions = ref(false)
const showDeleteDialog = ref(false)

// 表单数据
const groupForm = ref({

})

// 设备列表
const groupDevices = ref([
])

// 添加设备选项
const addActions = [
  {
    name: '从现有设备中选择',
    value: 'select'
  }
]

// 显示添加设备选项
const showAddDeviceOptions = () => {
  // 直接显示设备选择功能，不需要选项弹窗
  router.push({
    name:"deviceManagementCreateEquipmentSelection",
    query: {
      type: route.query.type,
      itemType:"edit",
      name:groupForm.value.group_title,
      id:groupForm.value.group_id,
      arr: JSON.stringify(groupDevices.value)
    }
  })
}

// 切换设备列表显示
const toggleDeviceList = () => {
  showDeviceList.value = !showDeviceList.value
}

// 添加设备选项选择
const onAddActionSelect = (action) => {
  if (action.value === 'select') {
    // 从现有设备中选择
  }
  showAddOptions.value = false
}



// 移除设备
const removeDevice = (deviceId) => {
  const index = groupDevices.value.findIndex(device => device.id === deviceId)
  if (index > -1) {
    groupDevices.value.splice(index, 1)
    showToast('设备已移除')
  }
}

// 保存设备组
const saveGroup = async () => {
  if (!groupForm.value.group_title.trim()) {
    showToast('请输入设备组名称')
    return
  }
  if (groupDevices.value.length === 0) {
    showToast('请至少添加一个设备')
    return
  }
  let arr=[]
  groupDevices.value.map((item) => {
    arr.push(item.id)
  })
  groupForm.value.devices= JSON.stringify(arr)
  groupForm.value.type =parseInt(route.query.type)
  console.log('保存设备组数据:', route.query)
  let obj = {
    title: groupForm.value.group_title,
    devices: groupForm.value.devices,
    type: groupForm.value.type,
    id: route.query.id
  }
  console.log('obj:', obj)
  saving.value = true
  proxy.$post('/device/post_modify_group', obj).then((res) => {
    if (res.code === 200) {
      showToast('设备组修改成功')
      router.push({
        name: 'deviceManagement',
      })
    } else {
      showToast(res.msg || '修改失败，请稍后重试')
    }
    saving.value = false
  })
}

// 删除设备组
const deleteGroup = () => {
  showDeleteDialog.value = true
}

// 确认删除
const confirmDelete = async () => {
  proxy.$postDel('/device/post_del_group',{id:groupForm.value.id}).then((res) => {
    console.log('删除设备组:', res)
    if(res.code == 200){
      showToast('设备组已删除')
      router.push({name: "deviceManagement"})
    } else {
      showToast(res.msg || '删除设备组失败，请稍后重试')
    }
  }).catch(error => {
    showToast(error.msg || '删除设备组失败，请稍后重试')
  })
}

// 页面初始化
onMounted(() => {
  if(route.query.devices){
    groupDevices.value= JSON.parse(route.query.devices)
    console.log('已选设备:', groupDevices.value)
  }
  if(route.query.name){
    groupForm.value.title = route.query.name
  }
  if(route.query.id){
    groupForm.value.id = route.query.id
  }
  if(route.query.type){
    groupForm.value.type = route.query.type
  }
  if(!route.query.isSelect){
    getDetail()
  }
})
</script>

<style lang="scss" scoped>
.edit-group-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 80px;
}

.group-name-section,
.add-device-section,
.devices-section {
  margin-top: 16px;
}

.device-list {
  .device-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-top: 1px solid #ebedf0;

    .device-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .device-prefix {
        width: 20px;
        height: 20px;
        background: #f7f8fa;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #969799;
      }

      .device-name {
        font-size: 14px;
        color: #323233;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 16px;
  padding: 16px;
  background: white;
  border-top: 1px solid #ebedf0;

  .van-button {
    flex: 1;
  }
}

.add-device-title {
  display: flex;
  align-items: center;
  gap: 12px;
  white-space: nowrap;

  .add-icon {
    width: 24px;
    height: 24px;
    background: #1989fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .van-icon {
      font-size: 14px;
      color: white;
      line-height: 1;
    }
  }

  span {
    font-size: 16px;
    color: #323233;
    line-height: 1;
    flex-shrink: 0;
  }
}

:deep(.van-cell-group) {
  margin: 0 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

// 统一字段标签宽度和字体大小
:deep(.van-field) {
  .van-field__label {
    flex: none;
    width: 80px;
    text-align: left;
    font-size: 16px;
    color: #323233;
  }

  .van-field__control {
    text-align: right;
  }
}

:deep(.van-cell) {
  // 统一标签宽度和字体大小
  .van-cell__title {
    flex: none;
    width: 80px;
    text-align: left;
    font-size: 16px;
    color: #323233;
  }
}
</style>
