<template>
  <div class="wraper-order-confirm">
    <div class="top-bg"></div>
    <div class="address-info" v-if="true">
      <div class="address-title">
        <van-cell
          v-if="data.type === 0"
          :title="app.loginData.dininghall_title"
          is-link
          :border="false"
        />
        <van-cell
          v-if="data.type === 1"
          @click="data.show = true"
          :title="
            data.addressList.length
              ? data.addressList[data.selectedAddressId].region +
                data.addressList[data.selectedAddressId].doorplate
              : '请选择地址'
          "
          is-link
          :border="false"
        />
        <div
          class="type"
          v-if="app.item.deliver * 1 && route.query.advanced * 1"
        >
          <div :class="`${data.type === 0 && 'active'}`" @click="data.type = 0">
            自提
          </div>
          <div :class="`${data.type === 1 && 'active'}`" @click="data.type = 1">
            外卖
          </div>
        </div>
      </div>
      <div
        v-if="data.type === 0"
        style="color: rgba(23, 26, 29, 0.4); font-size: 10px; margin: 0 16px"
      >
        {{ route.query.repast_title }} {{ route.query.window_title }}
      </div>
      <div
        v-if="data.type === 1"
        style="color: rgba(23, 26, 29, 0.4); font-size: 10px; margin: 0 16px"
      >
        {{
          data.addressList[data.selectedAddressId] &&
          data.addressList[data.selectedAddressId].name
        }}
        {{
          data.addressList[data.selectedAddressId] &&
          data.addressList[data.selectedAddressId].mobile
        }}
      </div>
      <div class="time-bottom address-title">
        <div class="title" style="width: 100px">
          {{ data.type * 1 ? "送达时间" : "就餐时间" }}
        </div>
        <van-cell :title="data.date" is-link :border="false" />
      </div>
      <div
        class="meal-type-block"
        v-if="data.type === 0 && route.query.advanced * 1"
      >
        <div
          :class="{
            'meal-type': true,
            'meal-type3': typeListLength < 3,
            active: mealType === i,
          }"
          v-for="(item, i) in typeList"
          :key="item.en"
          @click="onMealTypeClick(i)"
          v-show="item.isShow * 1"
        >
          <van-image
            style="background-color: #fff; padding: 1px"
            round
            :width="typeListLength < 3 ? '38px' : '28px'"
            :height="typeListLength < 3 ? '38px' : '28px'"
            :src="item.icon"
          />
          <div class="meal-type-info">
            <div style="font-size: 12px; font-weight: 500; margin-bottom: 2px">
              {{ item.title }}
            </div>
            <div style="transform: scale(0.9)">{{ item.en }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="address-info dish-list-block">
      <div class="dish-title">
        <div class="tip">订单信息</div>
        <van-button plain type="primary" size="small" round @click="goOrder"
          >继续点单</van-button
        >
      </div>
      <div :class="`dish-list ${data.isShowAll && 'show-all'}`">
        <div class="dish-item" v-for="(item, i) in data.selectList" :key="i">
          <div class="left">
            <van-image
              v-if="item.image"
              width="56"
              height="56"
              radius="4"
              :src="
                item.image ||
                'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
              "
            />
            <div
              v-else
              style="
                width: 56px;
                height: 56px;
                border-radius: 4px;
                background: #007fff;
                color: #fff;
                font-size: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              {{ item.title[0] }}
            </div>
          </div>
          <div class="right">
            <div>{{ item.title }}</div>
            <div class="dish-info">
              <div>{{ item.desc }}</div>
              <div>X{{ item.count }}</div>
              <div>￥{{ (item.price * 1 * item.count).toFixed(2) }}</div>
            </div>
          </div>
        </div>
      </div>
      <div style="text-align: center">
        <div class="show-all-button" @click="data.isShowAll = !data.isShowAll">
          <van-cell
            :title="data.isShowAll ? '收起' : '展示全部'"
            is-link
            :arrow-direction="data.isShowAll ? 'up' : 'down'"
          />
        </div>
      </div>
      <div class="detail-money">
        <span class="title">优惠金额</span>
        <span class="money">-￥{{ data.coupon_money }}</span>
      </div>
      <div class="detail-money">
        <span class="title">餐补金额</span>
        <span class="money">-￥{{ data.subsidy_money }}</span>
      </div>
    </div>
    <div class="address-info" style="margin-top: 16px">
      <van-field
        v-model="data.desc"
        rows="2"
        autosize
        label="备注"
        type="textarea"
        maxlength="200"
        placeholder="请输入地址、口味、偏好等"
        show-word-limit
        :disabled="!!data.orderId"
      />
    </div>
    <div class="goods-bar" v-if="!data.orderId">
      <div class="total">
        <van-icon
          size="34"
          :name="getImageUrl('/images/common/goodscar.png')"
          color="#1989fa"
          :badge="data.selectList.size"
          @click="data.showBottom = true"
        />
        <span style="margin-left: 16px; font-size: 14px">总价</span>
        <!-- <span style="font-size: 17px">￥{{ total }}</span> -->
        <span style="font-size: 17px">￥{{ (data.money * 1).toFixed(2) }}</span>
      </div>
      <van-button round type="primary" @click="onSettlementClick"
        >去结算</van-button
      >
    </div>
    <!-- <div v-else style="margin: 30px 16px" @click="onBackClick">
      <van-button style="dishplay: block; width: 100%" type="primary"
        >返回订单列表</van-button
      >
    </div> -->
    <van-action-sheet v-model:show="data.show" title="选择收货地址">
      <div class="content">
        <van-radio-group v-model="data.selectedAddressId" class="custom-radio">
          <div
            v-for="(item, i) in data.addressList"
            :key="item.id"
            style="
              padding-bottom: 12px;
              border-bottom: 1px solid rgba(0, 0, 0, 0.08);
              display: flex;
              align-items: center;
              margin-bottom: 12px;
            "
          >
            <van-radio :name="i" />
            <div style="flex: 1; margin-left: 8px">
              <div>{{ item.region + item.doorplate }}</div>
              <div style="font-size: 12px; color: #888; margin-top: 4px">
                {{ item.name }} {{ item.mobile }}
              </div>
            </div>
            <van-icon name="edit" size="24" @click.stop="onEditAddress(item)" />
          </div>
        </van-radio-group>
        <van-button
          type="primary"
          block
          plain
          icon="add-o"
          style="margin-top: 16px"
          @click="onAddAddress"
        >
          新增收货地址
        </van-button>
      </div>
    </van-action-sheet>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, computed } from "vue";
import { showToast, closeToast, showLoadingToast } from "vant";
import dayjs from "dayjs";
import isToday from 'dayjs/plugin/isToday';
// 注册插件
dayjs.extend(isToday);

import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
let data = reactive({
  orderId: "",
  desc: "",
  type: app.isTakeout ? app.isTakeout : 0,
  isShowAll: false,
  date:
    dayjs(route.query.date).add(20, "minute").format(`MM月DD日${dayjs(route.query.date).isToday()?'(今日)':''}`) +
    route.query.repast_start_time +
    "--" +
    route.query.repast_end_time,
  selectList: app.orderDishSelectList || new Set(),
  coupon_money: "0",
  money: "0",
  subsidy_money: 0,
  show: false,

  addressList: [],
  selectedAddressId: 0,
});

let typeList = [
  {
    title: "堂食",
    en: "Dine in",
    icon: "/images/common/dine.png",
    isShow: 1,
  },
  {
    title: "留餐",
    en: "Eat later",
    icon: "/images/common/eat.png",
    isShow: app.item.keep * 1,
  },
  {
    title: "打包",
    en: "Take away",
    icon: "/images/common/take.png",
    isShow: app.item.bale * 1,
  },
];
let typeListLength = typeList.filter((el) => el.isShow == 1).length;
let mealType = ref(0);

let total = computed(() => {
  let total = 0;
  for (let el of data.selectList) {
    total += el.price * 1 * (el.count * 1);
  }
  return total.toFixed(2);
});
const onMealTypeClick = (i) => {
  mealType.value = i;
};
function onEditAddress(item) {
  // 跳转到编辑地址页面，传递item.id
  router.push({
    path: "/editAddress",
    query: item,
  });
}
function onAddAddress() {
  // 跳转到新增地址页面
  router.push({ path: "/editAddress" });
}
watch(
  () => data.type,
  (val) => {
    app.isTakeout = val;
  }
);
const goOrder = () => {
  // router.push({
  //   path: "/orderMeal",
  //   query: route.query,
  // });
  router.back();
};
function getImageUrl(name) {
  return new URL(name, import.meta.url).href;
}
function onBackClick() {
  router.push({
    path: "/home",
  });
  // router.back(-3);
}
const getAddress = () => {
  proxy
    .$get("address/get_all")
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        data.addressList = res;
        data.addressList.findIndex((el, i) => {
          if (el.default == 1) {
            data.selectedAddressId = i;
            return true;
          }
        });
        console.log(res);
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(JSON.stringify(err));
      router.back();
    });
};
getAddress();
const preOrder = () => {
  showLoadingToast({
    message: "下单中...",
    forbidClick: true,
  });
  console.log("route.query", route.query);
  let postData = {
    dishess: JSON.stringify(
      Array.from(data.selectList).map((el) => ({
        id: el.id,
        title: el.title,
        count: el.count,
      }))
    ),
    repast_id: route.query.repast_id,
    window_id: route.query.window_id,
    date: route.query.date,
  };
  proxy
    .$post("order/post_order_per", postData)
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        console.log(res);
        Object.assign(data, res);
        // showToast("下单成功~");
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(JSON.stringify(err));
      router.back();
    })
    .finally(() => {
      setTimeout(() => {
        closeToast();
      }, 3000);
    });
};
const onSettlementClick = () => {
  showLoadingToast({
    message: "结算中...",
    forbidClick: true,
  });

  if (data.type == 1) {
    if (!data.addressList[data.selectedAddressId]) {
      showToast("请选择地址");
      return;
    }
    data.addressList[data.selectedAddressId].delivery_time =
      dayjs().add(20, "minute").format("MM月DD日 ") +
      route.query.repast_start_time +
      "--" +
      route.query.repast_end_time;
  }

  let postData = {
    dishess: JSON.stringify(
      Array.from(data.selectList).map((el) => ({
        id: el.id,
        title: el.title,
        count: el.count,
      }))
    ),
    repast_id: route.query.repast_id,
    window_id: route.query.window_id,
    date: route.query.date,
    desc: data.desc,
    dininghall_type: 1,
    window_order: 0,
    dine_type: data.type ? 3 : mealType.value,
    delivery: JSON.stringify(data.addressList[data.selectedAddressId]),
  };
  proxy
    .$post("order/post_order_add", postData)
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        showToast("结算成功~");
        data.orderId = res.id;
        setTimeout(() => {
          onBackClick();
          closeToast();
        }, 3000);
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
      setTimeout(() => {
        data.orderId = 6666;
        closeToast();
      }, 3000);
    })
    .finally(() => {
      // setTimeout(() => {
      //   data.orderId = 6666;
      //   closeToast();
      // }, 3000);
    });
};
preOrder();
</script>

<style lang="scss">
.wraper-order-confirm {
  padding-bottom: 120px;
  .top-bg {
    background: linear-gradient(#007fff, #e4edf6);
    height: 250px;
  }
  .address-info {
    margin: 0 16px;
    margin-top: -228px;
    border-radius: 8px;
    background: #ffffff;
    overflow: hidden;
    .address-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .van-cell__title {
        font-size: 15px;
      }
      .type {
        width: 150px;
        font-size: 14px;
        background: rgba(0, 0, 0, 0.16);
        display: flex;
        justify-content: space-between;
        padding: 1px;
        margin-right: 16px;
        border-radius: 4px;
        div {
          padding: 5px 8px;
          border-radius: 4px;
        }
      }
      .active {
        background: #fff;
      }
    }
    .time-bottom {
      .title {
        font-size: 14px;
        margin-left: 16px;
      }
      .van-cell__title {
        text-align: right;
        font-size: 14px;
        color: #007fff !important;
      }
    }
  }
  .dish-list-block {
    margin-top: 16px;

    .dish-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
    }
    .dish-list {
      max-height: 300px;
      overflow: scroll;
      .dish-item {
        padding: 12px;
        display: flex;
        .right {
          flex: 1;
          font-size: 14px;
          margin-left: 10px;
          .dish-info {
            margin-top: 4px;
            display: flex;
            justify-content: space-between;
            color: rgba(23, 26, 29, 0.4);
            font-size: 10px;
            div:nth-of-type(1) {
              width: 100px;
            }
            div:nth-of-type(3) {
              font-size: 14px;
              color: #171a1d;
            }
          }
        }
      }
    }
    .show-all {
      height: auto;
      max-height: none;
    }
    .show-all-button {
      background: #f2f2f6;
      border-radius: 50px;
      width: 90px;
      overflow: hidden;
      display: inline-block;
      .van-cell {
        background: #f2f2f6;
        padding: 2px 8px;
        color: rgba(23, 26, 29, 0.4);
        font-size: 10px;
      }
    }
    .detail-money {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      margin: 12px;
      .title {
      }
      .money {
        color: #ff5219;
      }
    }
  }
}
.goods-bar {
  padding: 0 12px;
  position: fixed;
  bottom: 16px;
  left: 0;
  right: 0;
  height: 58px;
  border-radius: 29px;
  opacity: 1;
  background: #ffffff;
  box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 16px;
  .total {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.content {
  padding: 32px 16px;
}
.custom-radio {
  .van-radio__icon {
    width: 20px;
    height: 20px;
    .van-icon {
      background: #fff;
    }
  }
  .van-radio__icon--checked,
  .van-checkbox__icon--checked {
    border-color: #007fff !important;
    border: 1px solid #007fff !important; // 蓝色边框
    border-radius: 50%;
    width: 20px;
    height: 20px;
    &::before {
      content: "";
      display: block;
      width: 12px;
      height: 12px;
      background: #007fff !important; // 选中时蓝色背景
      border-radius: 50%;
      margin: 3px auto;
    }
  }
}
.meal-type-block {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  .meal-type {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #007fff;
    padding: 8px 0;
    width: 30%;
    border-radius: 8px;
    color: #007fff;
    font-size: 12px;
    .meal-type-info {
      margin-left: 8px;
      text-align: center;
    }
  }
  .meal-type3 {
    width: 48%;
    height: 66px;
  }
  .active {
    background: #007fff;
    color: #ffffff;
  }
}
</style>
