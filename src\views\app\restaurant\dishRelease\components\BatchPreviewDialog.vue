<template>
  <van-dialog
    v-model:show="dialogVisible"
    :title="dialogConfig.title"
    :show-cancel-button="true"
    :confirm-button-text="dialogConfig.confirmText"
    :confirm-button-color="dialogConfig.confirmColor"
    cancel-button-text="取消"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    class="batch-preview-dialog"
  >
    <div class="preview-content">
      <div class="description">{{ dialogConfig.description }}</div>
      
      <!-- 日历预览区域 -->
      <div class="calendar-preview">
        <div class="weekdays">
          <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
        </div>
        
        <div class="dates-grid">
          <div
            v-for="date in previewDates"
            :key="date.key"
            class="date-item"
            :class="{
              'highlight': date.highlight,
              'selected': date.selected,
              'disabled': date.disabled
            }"
          >
            {{ date.day }}
          </div>
        </div>
      </div>
    </div>
  </van-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  operationType: {
    type: String,
    default: ''
  },
  selectedDate: {
    type: Date,
    default: () => new Date()
  }
})

const emit = defineEmits(['update:show', 'confirm', 'cancel'])

const dialogVisible = ref(false)
const weekdays = ['日', '一', '二', '三', '四', '五', '六']

// 监听显示状态
watch(() => props.show, (newVal) => {
  dialogVisible.value = newVal
})

watch(dialogVisible, (newVal) => {
  emit('update:show', newVal)
})

// 弹窗配置
const dialogConfig = computed(() => {
  const configs = {
    'sync_day_to_week': {
      title: '同步日菜单至本周',
      description: '将今日菜单同步到本周工作日',
      confirmText: '确认执行',
      confirmColor: '#1989FA'
    },
    'sync_week_to_month': {
      title: '同步周菜单至本月',
      description: '将本周菜单模式同步到整月',
      confirmText: '确认执行',
      confirmColor: '#1989FA'
    },
    'delete_sunday': {
      title: '删除周日菜单',
      description: '删除本月所有周日的菜单',
      confirmText: '确认执行',
      confirmColor: '#1989FA'
    },
    'delete_weekend': {
      title: '删除周六日菜单',
      description: '删除本月所有周末的菜单',
      confirmText: '确认执行',
      confirmColor: '#1989FA'
    }
  }
  return configs[props.operationType] || configs['sync_day_to_week']
})

// 生成预览日期数据
const previewDates = computed(() => {
  if (!props.selectedDate) return []
  
  const currentDate = dayjs(props.selectedDate)
  const year = currentDate.year()
  const month = currentDate.month()
  const daysInMonth = currentDate.daysInMonth()
  const firstDayOfMonth = dayjs(`${year}-${month + 1}-01`).day()
  
  const dates = []
  
  // 添加上个月的日期填充
  const prevMonth = currentDate.subtract(1, 'month')
  const daysInPrevMonth = prevMonth.daysInMonth()
  for (let i = firstDayOfMonth - 1; i >= 0; i--) {
    const day = daysInPrevMonth - i
    dates.push({
      key: `prev-${day}`,
      day,
      highlight: false,
      selected: false,
      disabled: true
    })
  }
  
  // 添加当月日期
  for (let day = 1; day <= daysInMonth; day++) {
    const date = dayjs(`${year}-${month + 1}-${day}`)
    const dayOfWeek = date.day()
    
    let highlight = false
    let selected = false
    
    // 根据操作类型设置高亮
    switch (props.operationType) {
      case 'sync_day_to_week':
        // 高亮本周工作日
        if (date.isSame(currentDate, 'week') && dayOfWeek >= 1 && dayOfWeek <= 5) {
          highlight = true
        }
        if (date.isSame(currentDate, 'day')) {
          selected = true
        }
        break
      case 'sync_week_to_month':
        // 高亮整月
        highlight = true
        if (date.isSame(currentDate, 'week')) {
          selected = true
        }
        break
      case 'delete_sunday':
        // 高亮周日
        if (dayOfWeek === 0) {
          highlight = true
        }
        break
      case 'delete_weekend':
        // 高亮周六日
        if (dayOfWeek === 0 || dayOfWeek === 6) {
          highlight = true
        }
        break
    }
    
    dates.push({
      key: `current-${day}`,
      day,
      highlight,
      selected,
      disabled: false
    })
  }
  
  // 添加下个月的日期填充
  const totalCells = 42 // 6行 × 7列
  const remainingCells = totalCells - dates.length
  for (let day = 1; day <= remainingCells; day++) {
    dates.push({
      key: `next-${day}`,
      day,
      highlight: false,
      selected: false,
      disabled: true
    })
  }
  
  return dates
})

const handleConfirm = () => {
  emit('confirm', props.operationType)
  dialogVisible.value = false
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.batch-preview-dialog {
  .van-dialog {
    border-radius: 12px;
    overflow: hidden;
 

  :deep(.van-dialog__header) {
    padding: 20px 20px 0;
    font-size: 18px;
    font-weight: 500;
    color: #323233;
  }

  :deep(.van-dialog__content) {
    padding: 20px;
  }

  :deep(.van-dialog__footer) {
    padding: 0 20px 20px;

    .van-button {
      border-radius: 8px;
      font-weight: 500;

      &--default {
        background-color: #f7f8fa;
        color: #646566;
        border: none;
      }

      &--primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
      }
    }
  }
   }
}

.preview-content {
  .description {
    font-size: 13px;
    color: #969799;
    // margin-bottom: 16px;
    text-align: center;
    line-height: 19px;
  }
}

.calendar-preview {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;

  .weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-bottom: 16px;

    .weekday {
      text-align: center;
      font-size: 12px;
      color: #969799;
      font-weight: 500;
      padding: 8px 0;
    }
  }

  .dates-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;

    .date-item {
      aspect-ratio: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 500;
      border-radius: 6px;
      background-color: #fff;
      color: #323233;
      transition: all 0.2s ease;

      &.highlight {
        background: linear-gradient(135deg, #e1f0ff 0%, #cce7ff 100%);
        color: #1989fa;
        box-shadow: 0 1px 4px rgba(25, 137, 250, 0.2);
      }

      &.selected {
        background: linear-gradient(135deg, #e1f0ff 0%, #cce7ff 100%);
        color: #1989fa;
        // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        // color: white;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
        // transform: scale(1.05);
      }

      &.disabled {
        color: #c8c9cc;
        background-color: #f7f8fa;
      }
    }
  }
}
</style>
