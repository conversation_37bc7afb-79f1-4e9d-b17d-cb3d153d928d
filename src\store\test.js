export let data = {
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjb3JwX3R5cGUiOiJkdGFsayIsImNvcnBfcHJvZHVjdCI6ImNhbnlpbiIsInR5cGVzIjoiaXN2IiwiY29ycGlkIjoiZGluZ2I5NjE0ZGY5NDM0MmY1NzBhMTMyMGRjYjI1ZTkxMzUxIiwiY29ycF9uYW1lIjoiXHU0ZTAwXHU0ZTAwXHU3OWQxXHU2MjgwXHU1MTg1XHU5MGU4XHU1ZjAwXHU1M2QxXHU1ZTczXHU1M2YwIiwidXNlcmlkIjoiMDMzNjE2MjQ2MzI3MTk2ODExIiwidW5pb25pZCI6IllCTzFiaWlVUGgzcXRMZUZpUG1UVlVCUWlFaUUiLCJuYW1lIjoiXHU2YmI1XHU1NDJmXHU3OTY1Iiwic3RhZmZfbmFtZSI6Ilx1NmJiNVx1NTQyZlx1Nzk2NSIsInN0YWZmaWQiOiIwMzM2MTYyNDYzMjcxOTY4MTEiLCJqb2JfbnVtYmVyIjoiIiwiZGVwdF9uYW1lIjoiXHU5MGU4XHU5NWU4MS4xLjEiLCJkZXBhcnRtZW50X25hbWUiOiJcdTkwZThcdTk1ZTgxLjEuMSIsImRlcHRfaWRfbGlzdCI6Ils0ODUzODk1MzAsIDQ4NjM3MDAxOCwgNjY0OTQ2Mjk5LCAyOTk5MzkwMTcsIDI3ODU0MjA1MCwgNTY2MTQ1NDA4XSIsImRlcHRfbGlzdCI6WzEsNTY2MTQ1NDA4LDI5OTkzOTAxNywyNzg1NDIwNTBdLCJwYXJlbnRfaWRfbGlzdCI6W10sInVzZXJfdGl0bGUiOiIiLCJkaW5pbmdoYWxsX2lkIjo5NiwiZGluaW5naGFsbF90aXRsZSI6Ilx1NWU3M1x1NzE2NFx1NjczYVx1OTkxMFx1NTM4NSIsImFkbWluIjoxfQ.syeqrZ4Zqzr0kl5aeeYcyXpyFYEVzd-OcJWRtDsMHXA", 
  "user_info": {
    "id": 2, 
    "corpid": "dingb9614df94342f570a1320dcb25e91351", 
    "userid": "033616246327196811", 
    "unionid": "YBO1biiUPh3qtLeFiPmTVUBQiEiE", 
    "name": "段启祥", 
    "avatar": "https://static-legacy.dingtalk.com/media/lQLPDhvB0biBhjnNAyDNAyCwWJngXP0Y3K8DDjoDHoBaAA_800_800.png", 
    "state_code": "", 
    "manager_userid": "", 
    "mobile": null, 
    "hide_mobile": 0, 
    "telephone": "", 
    "job_number": "", 
    "title": "", 
    "email": "", 
    "org_email": "", 
    "work_place": "", 
    "remark": "", 
    "department_name": "部门1.1.1", 
    "dept_id_list": "[*********]", 
    "dept_order_list": null, 
    "extension": null, 
    "hired_date": null, 
    "active": 1, 
    "admin": 1, 
    "boss": 0, 
    "leader": 0, 
    "exclusive_account": 0, 
    "login_id": "", 
    "exclusive_account_type": "", 
    "leader_in_dept": null, 
    "role_list": "[{'id': *********, 'name': '子管理员', 'group_name': '默认'}]", 
    "union_emp_ext": "[]", 
    "isleave": 0, 
    "isleave_time": null, 
    "created_at": "2022-03-09 00:07:54", 
    "updated_at": "2023-08-14 16:56:47", 
    "dept_id": null, 
    "id_card": null, 
    "area": "[]", 
    "dept_list": [
      1, 
      *********, 
      *********, 
      *********
    ], 
    "parent_id_list": [
      1, 
      *********, 
      *********, 
      *********
    ]
  }, 
  "dininghall_id": 96, 
  "dininghall_title": "平煤机餐厅", 
  "is_expire": 0, 
  "end_date": null, 
  "permission": {
    "all_action": 0, 
    "actions": [
      {
        "id": 1, 
        "app": "dininghall", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "餐厅管理"
      }, 
      {
        "id": 2, 
        "app": "window", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "窗口管理"
      }, 
      {
        "id": 3, 
        "app": "device", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }
        ], 
        "title": "设备管理"
      }, 
      {
        "id": 4, 
        "app": "repast", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "餐时管理"
      }, 
      {
        "id": 5, 
        "app": "repast_group", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "餐时组"
      }, 
      {
        "id": 6, 
        "app": "subsidy", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "餐补方案"
      }, 
      {
        "id": 7, 
        "app": "attendance", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "考勤方案"
      }, 
      {
        "id": 8, 
        "app": "apply", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "报餐方案"
      }, 
      {
        "id": 9, 
        "app": "order", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "点餐方案"
      }, 
      {
        "id": 10, 
        "app": "plan", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }, 
          {
            "act": "post_upload_user", 
            "title": "上传人员"
          }, 
          {
            "act": "get_file_head", 
            "title": "导出表头"
          }
        ], 
        "title": "就餐方案"
      }, 
      {
        "id": 11, 
        "app": "dishes_category", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "菜品分类"
      }, 
      {
        "id": 12, 
        "app": "dishes", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "菜品管理"
      }, 
      {
        "id": 13, 
        "app": "dishes_release", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "post_release", 
            "title": "发布"
          }, 
          {
            "act": "post_syn_day_to_week", 
            "title": "同步到周"
          }, 
          {
            "act": "post_syn_week_to_month", 
            "title": "同步到月"
          }, 
          {
            "act": "post_sunday_del", 
            "title": "删除周日"
          }, 
          {
            "act": "post_sundays_del", 
            "title": "删除周六日"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "菜品发布"
      }, 
      {
        "id": 14, 
        "app": "repast_cost", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "餐费设置"
      }, 
      {
        "id": 15, 
        "app": "permission_group", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "权限管理"
      }, 
      {
        "id": 16, 
        "app": "recharge", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "post_admin_recharge", 
            "title": "充值"
          }
        ], 
        "title": "充值"
      }, 
      {
        "id": 17, 
        "app": "recharge_cron", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "充值计划"
      }, 
      {
        "id": 18, 
        "app": "balance", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }
        ], 
        "title": "余额管理"
      }, 
      {
        "id": 19, 
        "app": "account_deduction", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "post_admin_deduction", 
            "title": "扣款"
          }
        ], 
        "title": "账户扣款"
      }, 
      {
        "id": 20, 
        "app": "config", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }
        ], 
        "title": "系统配置"
      }, 
      {
        "id": 21, 
        "app": "message_config", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }
        ], 
        "title": "消息推送"
      }, 
      {
        "id": 22, 
        "app": "datav", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }
        ], 
        "title": "统计查询"
      }, 
      {
        "id": 23, 
        "app": "voice", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }
        ], 
        "title": "语音设置"
      }, 
      {
        "id": 24, 
        "app": "pay_config", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }
        ], 
        "title": "支付设置"
      }, 
      {
        "id": 26, 
        "app": "feedback_manage", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "post_reply", 
            "title": "回复"
          }
        ], 
        "title": "反馈管理"
      }, 
      {
        "id": 27, 
        "app": "recharge_log", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }
        ], 
        "title": "充值记录"
      }, 
      {
        "id": 29, 
        "app": "report", 
        "acts": [
          {
            "act": "get_bill_ls", 
            "title": "账单列表"
          }, 
          {
            "act": "get_day_sum", 
            "title": "每日汇总"
          }, 
          {
            "act": "get_dininghall_sum", 
            "title": "餐厅汇总"
          }, 
          {
            "act": "get_user_sum", 
            "title": "人员汇总"
          }, 
          {
            "act": "get_bill_sum", 
            "title": "账单汇总"
          }, 
          {
            "act": "get_account_log_ls", 
            "title": "资金记录"
          }, 
          {
            "act": "get_rechage_ls", 
            "title": "充值记录"
          }, 
          {
            "act": "get_account_ls", 
            "title": "账户列表"
          }, 
          {
            "act": "get_coupon_bill_ls", 
            "title": "优惠券账单"
          }, 
          {
            "act": "get_supermarket_sell_sum", 
            "title": "超市销售汇总"
          }, 
          {
            "act": "get_visitor_bill_ls", 
            "title": "访客账单"
          }, 
          {
            "act": "get_coupon_sum", 
            "title": "优惠券汇总"
          }, 
          {
            "act": "get_alipay_log_ls", 
            "title": "支付宝流水列表"
          }, 
          {
            "act": "get_dept_sum", 
            "title": "部门汇总"
          }, 
          {
            "act": "get_bill_evaluate_ls", 
            "title": "账单评价列表"
          }, 
          {
            "act": "get_notice_ls", 
            "title": "公告列表"
          }, 
          {
            "act": "get_entertain_apply_ls", 
            "title": "包间预定列表"
          }
        ], 
        "title": "报表"
      }, 
      {
        "id": 31, 
        "app": "erp_category", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "商品分类"
      }, 
      {
        "id": 32, 
        "app": "erp_unit", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "商品单位"
      }, 
      {
        "id": 33, 
        "app": "erp_goods", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "商品管理"
      }, 
      {
        "id": 34, 
        "app": "erp_stock", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "post_enter", 
            "title": "入库"
          }, 
          {
            "act": "post_enter_batch", 
            "title": "批量入库"
          }
        ], 
        "title": "库存管理"
      }, 
      {
        "id": 35, 
        "app": "erp_stock_enter", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }
        ], 
        "title": "商品入库"
      }, 
      {
        "id": 38, 
        "app": "balance_empty", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "post_admin_empty", 
            "title": "清空"
          }
        ], 
        "title": "清空余额"
      }, 
      {
        "id": 39, 
        "app": "visitor_config", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }
        ], 
        "title": "访客设置"
      }, 
      {
        "id": 40, 
        "app": "operate_log", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }
        ], 
        "title": "审计日志"
      }, 
      {
        "id": 43, 
        "app": "cost", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "成本中心"
      }, 
      {
        "id": 44, 
        "app": "budget", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }
        ], 
        "title": "部门预算"
      }, 
      {
        "id": 41, 
        "app": "meeting", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "get_config_info", 
            "title": "查看配置"
          }, 
          {
            "act": "post_config_modify", 
            "title": "修改配置"
          }
        ], 
        "title": "会议餐"
      }, 
      {
        "id": 36, 
        "acts": [
          {
            "act": "check", 
            "title": "模板查看"
          }, 
          {
            "act": "add", 
            "title": "模板添加"
          }, 
          {
            "act": "modify", 
            "title": "模板修改"
          }, 
          {
            "act": "del", 
            "title": "模板删除"
          }, 
          {
            "act": "get_issue_ls", 
            "title": "发放记录查看"
          }, 
          {
            "act": "post_issue_add", 
            "title": "优惠券发放"
          }, 
          {
            "act": "post_issue_del", 
            "title": "发放记录删除"
          }, 
          {
            "act": "post_issue_cron_ls", 
            "title": "发放计划查看"
          }, 
          {
            "act": "post_issue_cron_add", 
            "title": "发放计划添加"
          }, 
          {
            "act": "post_issue_cron_modify", 
            "title": "发放计划修改"
          }, 
          {
            "act": "post_issue_cron_del", 
            "title": "发放计划删除"
          }
        ], 
        "class": "coupon", 
        "title": "优惠券"
      }, 
      {
        "id": 25, 
        "app": "feedback_dininghall", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }
        ], 
        "title": "餐厅反馈"
      }, 
      {
        "id": 28, 
        "app": "pay_auth", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }
        ], 
        "title": "支付授权"
      }, 
      {
        "id": 37, 
        "app": "diancan_online", 
        "acts": [
        ], 
        "title": "点餐"
      }, 
      {
        "id": 42, 
        "app": "apply_auto", 
        "acts": [
        ], 
        "title": "自动报餐"
      }, 
      {
        "id": 45, 
        "app": "erp_stock_out", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }
        ], 
        "title": "商品出库"
      }, 
      {
        "id": 46, 
        "app": "bill_ls", 
        "acts": [
          {
            "act": "get_bill_ls", 
            "title": "账单列表"
          }
        ], 
        "title": "账单列表"
      }, 
      {
        "id": 47, 
        "app": "notice", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "公告管理"
      }, 
      {
        "id": 48, 
        "app": "entertain_room", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }, 
          {
            "act": "modify", 
            "title": "修改"
          }, 
          {
            "act": "del", 
            "title": "删除"
          }
        ], 
        "title": "包间管理"
      }, 
      {
        "id": 49, 
        "app": "entertain_apply", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }, 
          {
            "act": "add", 
            "title": "添加"
          }
        ], 
        "title": "包间预订"
      }, 
      {
        "id": 50, 
        "app": "payment_code", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }
        ], 
        "title": "付款码"
      },
      {
        "id": 28, 
        "app": "visitor_recharge", 
        "acts": [
          {
            "act": "check", 
            "title": "查看"
          }
        ], 
        "title": "访客充值"
      }, 
    ]
  }
}