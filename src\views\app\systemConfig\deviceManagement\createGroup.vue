<template>
  <div class="create-group-container">
    <!-- 页面标题 -->
    <!-- <van-nav-bar title="创建设备组"  /> -->

    <!-- 设备组名称 -->
    <div class="group-name-section">
      <van-cell-group inset>
        <van-field
          v-model="groupForm.title"
          label="设备组名称"
          placeholder="请输入设备组名称"
          :rules="[{ required: true, message: '请输入设备组名称' }]"
        />
      </van-cell-group>
    </div>

    <!-- 添加设备按钮 -->
    <div class="add-device-section">
      <van-cell-group inset>
        <van-cell
          @click="showAddDeviceOptions"
        >
          <template #title>
            <div class="add-device-title">
              <div class="add-icon">
                <van-icon name="plus" />
              </div>
              <span>添加设备</span>
            </div>
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 设备列表 -->
    <div v-if="groupDevices.length > 0" class="devices-section">
      <van-cell-group inset>
        <van-cell title="设备" :value="`(${groupDevices.length})`">
          <template #right-icon>
            <van-icon name="arrow-down" @click="toggleDeviceList" />
          </template>
        </van-cell>
        
        <div v-show="showDeviceList" class="device-list">
          <div
            v-for="device in groupDevices"
            :key="device.id"
            class="device-item"
          >
            <div class="device-info">
              <span class="device-prefix">L</span>
              <span class="device-name">{{ device.title }}</span>
            </div>
            <van-button
              type="primary"
              size="mini"
              plain
              @click="removeDevice(device.id)"
            >
              移除
            </van-button>
          </div>
        </div>
      </van-cell-group>
    </div>

    <!-- 底部保存按钮 -->
    <div class="bottom-actions">
      <van-button
        type="primary"
        size="large"
        block
        :loading="saving"
        @click="saveGroup"
      >
        保存
      </van-button>
    </div>

    <!-- 添加设备选项弹窗 -->
    <van-action-sheet
      v-model:show="showAddOptions"
      :actions="addActions"
      @select="onAddActionSelect"
      cancel-text="取消"
      title="添加设备"
    />

    <!-- 设备选择器弹窗 -->
    <!-- <van-popup
      v-model:show="showDeviceSelector"
      position="bottom"
      :style="{ height: '70%' }"
      round
    >
      <div class="device-selector">
        <div class="selector-header">
          <h3>选择设备</h3>
          <van-button
            type="primary"
            size="small"
            :disabled="selectedDevices.length === 0"
            @click="confirmAddDevices"
          >
            确定({{ selectedDevices.length }})
          </van-button>
        </div>

        <div class="device-selector-list">
          <div
            v-for="device in availableDevices"
            :key="device.id"
            class="selector-device-item"
            @click="toggleDeviceSelection(device)"
          >
            <div class="device-info">
              <div class="device-name">{{ device.title }}</div>
              <div class="device-sn">{{ device.deviceSn }}</div>
              <div class="device-status" :class="{ online: device.status === 1 }">
                {{ device.network_status === 1 ? '在线' : '离线' }}
              </div>
            </div>
            <van-checkbox
              :model-value="selectedDevices.some(d => d.id === device.id)"
              @click.stop="toggleDeviceSelection(device)"
            />
          </div>
        </div>
      </div>
    </van-popup> -->
  </div>
</template>

<script setup>
import { ref,getCurrentInstance, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'
const { proxy } = getCurrentInstance();
// 路由实例
const route = useRoute()
const router = useRouter()
onMounted(() => {
  console.log('当前路由:', route)
  if(route.query.devices){
    groupDevices.value= JSON.parse(route.query.devices)
    console.log('已选设备:', groupDevices.value)
  }
  if(route.query.name){
    groupForm.value.title = route.query.name
  }
})


// 页面状态
const saving = ref(false)
const showDeviceList = ref(true)
const showAddOptions = ref(false)
const showDeviceSelector = ref(false)

// 表单数据
const groupForm = ref({
  title: '',
  devices: [],
  type:route.query.type
})

// 设备列表
const groupDevices = ref([])


// 选中的设备
const selectedDevices = ref([])

// 添加设备选项
const addActions = [
  {
    name: '从现有设备中选择',
    value: 'select'
  }
]
// function getDeviceList(){
//   proxy.$get('/device/get_device_all', { type: route.query.type }).then((res) => {
//     if (res.code === 200) {
//       availableDevices.value = res.data
//      console.log('获取设备列表成功:', res.data)
//     } else {
//       showToast(res.msg || '获取设备列表失败，请稍后重试')
//     }
//   }).catch(error => {
//     showToast(error.msg || '获取设备列表失败，请稍后重试')
//   })
// }
// 显示添加设备选项
const showAddDeviceOptions = () => {
  // 直接显示设备选择器，不需要选项弹窗
  // getDeviceList()  
  // showDeviceSelectorDialog()
  console.log(333)
  router.push({
    name:"deviceManagementCreateEquipmentSelection",
    query: {
      type: route.query.type,
      itemType:"add",
      name:groupForm.value.title,
      arr: JSON.stringify(groupDevices.value)
    }
  })
}

// 切换设备列表显示
const toggleDeviceList = () => {
  showDeviceList.value = !showDeviceList.value
}

// 添加设备选项选择
const onAddActionSelect = (action) => {
  if (action.value === 'select') {
    // 从现有设备中选择
    showDeviceSelectorDialog()
  }
  showAddOptions.value = false
}

// 显示设备选择器
const showDeviceSelectorDialog = () => {
  // 过滤出当前设备类型且未在组中的设备
  const currentType = route.query.type
  selectedDevices.value = []
  showDeviceSelector.value = true
}

// 切换设备选择
const toggleDeviceSelection = (device) => {
  const index = selectedDevices.value.findIndex(d => d.id === device.id)
  if (index > -1) {
    selectedDevices.value.splice(index, 1)
  } else {
    selectedDevices.value.push(device)
  }
}

// 确认添加选中的设备
const confirmAddDevices = () => {
  selectedDevices.value.forEach(device => {
    groupDevices.value.push({
      id: device.id,
      name: device.name,
      sn: device.sn
    })
  })

  showToast(`已添加 ${selectedDevices.value.length} 个设备`)
  showDeviceSelector.value = false
  selectedDevices.value = []
}



// 跳转到手动添加设备页面
const goToAddDevice = () => {
  router.push({
    path: `/app/systemConfig/deviceManagement/add`,
    query: {
      type: route.query.type,
      isCreateGroup: true
    }
  })
}

// 移除设备
const removeDevice = (deviceId) => {
  const index = groupDevices.value.findIndex(device => device.id === deviceId)
  if (index > -1) {
    groupDevices.value.splice(index, 1)
    showToast('设备已移除')
  }
}

// 保存设备组
const saveGroup = async () => {
  if (!groupForm.value.title.trim()) {
    showToast('请输入设备组名称')
    return
  }
  if (groupDevices.value.length === 0) {
    showToast('请至少添加一个设备')
    return
  }
  let arr=[]
  groupDevices.value.map((item) => {
    arr.push(item.id)
  })
  groupForm.value.devices=JSON.stringify(arr)
  // groupForm.value.devices=JSON.stringify(groupDevices.value)
  groupForm.value.type =parseInt(groupForm.value.type)
  saving.value = true
  proxy.$post('/device/post_add_group', groupForm.value).then((res) => {
    if (res.code === 200) {
      showToast('设备组创建成功')
      router.push({
        name: 'deviceManagement',
      })
    } else {
      showToast(res.msg || '创建设备组失败，请稍后重试')
    }
    saving.value = false
  }).catch(error => {
    showToast(error.msg || '创建设备组失败，请稍后重试')
    saving.value = false
  })
 console.log('保存设备组:', groupForm.value)

}
</script>

<style lang="scss" scoped>
.create-group-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 80px;
}

.group-name-section,
.add-device-section,
.devices-section {
  margin-top: 16px;
}

.device-list {
  .device-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-top: 1px solid #ebedf0;

    .device-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .device-prefix {
        width: 20px;
        height: 20px;
        background: #f7f8fa;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #969799;
      }

      .device-name {
        font-size: 14px;
        color: #323233;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #ebedf0;
}

.add-device-title {
  display: flex;
  align-items: center;
  gap: 12px;
  white-space: nowrap;

  .add-icon {
    width: 24px;
    height: 24px;
    background: #1989fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .van-icon {
      font-size: 14px;
      color: white;
      line-height: 1;
    }
  }

  span {
    font-size: 16px;
    color: #323233;
    line-height: 1;
    flex-shrink: 0;
  }
}

:deep(.van-cell-group) {
  margin: 0 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

// 统一字段标签宽度和字体大小
:deep(.van-field) {
  .van-field__label {
    flex: none;
    width: 80px;
    text-align: left;
    font-size: 16px;
    color: #323233;
  }

  .van-field__control {
    text-align: right;
  }
}

:deep(.van-cell) {
  // 统一标签宽度和字体大小
  .van-cell__title {
    flex: none;
    width: 80px;
    text-align: left;
    font-size: 16px;
    color: #323233;
  }
}

// 设备选择器样式
.device-selector {
  height: 100%;
  display: flex;
  flex-direction: column;

  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #ebedf0;
    background: white;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #323233;
    }
  }

  .device-selector-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .selector-device-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      background: white;
      border-radius: 12px;
      margin-bottom: 12px;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

      &:active {
        background: #f7f8fa;
      }

      .device-info {
        flex: 1;

        .device-name {
          font-size: 16px;
          color: #323233;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .device-sn {
          font-size: 14px;
          color: #969799;
          margin-bottom: 4px;
        }

        .device-status {
          font-size: 12px;
          color: #ee0a24;

          &.online {
            color: #07c160;
          }
        }
      }
    }
  }
}
</style>
