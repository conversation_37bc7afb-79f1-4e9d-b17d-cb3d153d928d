<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="detail"
      :editRedirectConfig="editRedirectConfig"
      @onSubmit="onBasicSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'
const { proxy } = getCurrentInstance();

const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    id: route.query.id // 从路由获取id参数
  },
  curl: {
    info: '/api/stall/info', // 获取详情接口
    del: '/api/stall/delete' // 删除接口
  },
    groupForm: [
        [0, 1],
        [1, 12]
    ],
    form: [
        {
            label: "预定包间",
            key: "select-picker",
            component: "yhc-picker",
            placeholder: "请选择",
            // 禁止输入
            disabled: true,
            required: true,
            rules: [{ required: true, message: "请选择包间" }],
            popup: {
                round: true,
                position: "bottom",
                style: { height: "50vh", overflow: "hidden" },
                closeable: false
            },
            card: {
                title: "title",
                desc: "desc"
            },
            opts: {
                url: "",
                postData: {},
                merge: false,
                multiple: true,
                maxlength: 32,
                text_key: "title",
                contrast_key: "id",
                keyMap: {},
                defaultList: [
                    { id: 1, title: "技术部", desc: "111" },
                    { id: 2, title: "市场部", desc: "111" },
                    { id: 3, title: "人事部", desc: "111" },
                    { id: 4, title: "财务部", desc: "111" },
                    { id: 5, title: "技术部", desc: "111" }
                ]
            },
        },
        {
            label: "预定日期",
            key: "speed_duration2",
            component: "yhc-picker-date",
            type: "time",
            disabled: true,
            required: true,
            rules: [{ required: true, message: "请选择预定日期" }],
        },
        {
            label: "预定餐时",
            key: "select-picker",
            component: "yhc-picker",
            placeholder: "请选择",
            required: true,
            disabled: true,
            rules: [{ required: true, message: "请选择餐时" }],
            popup: {
                round: true,
                position: "bottom",
                style: { height: "50vh", overflow: "hidden" },
                closeable: false
            },
            card: {
                title: "title",
                desc: "desc"
            },
            opts: {
                url: "",
                postData: {},
                merge: false,
                multiple: true,
                maxlength: 32,
                text_key: "title",
                contrast_key: "id",
                keyMap: {},
                defaultList: [
                    { id: 1, title: "技术部", desc: "111" },
                    { id: 2, title: "市场部", desc: "111" },
                    { id: 3, title: "人事部", desc: "111" },
                    { id: 4, title: "财务部", desc: "111" },
                    { id: 5, title: "技术部", desc: "111" }
                ]
            },
        },
        {
            label: "用餐人数",
            key: "basic-number",
            component: "yhc-input",
            type: "number",
            placeholder: "请输入",
            disabled: true,
        },
        {
            label: "用餐部门",
            key: "department",
            component: "yhc-select-department",
            required: true,
            disabled: true,
            rules: [{ required: true, message: "请选择用餐" }],
        },
        {
            label: "自定义表单",
            key: "basic-number",
            component: "yhc-input",
            placeholder: "请输入",
            disabled: true,
        },
        {
            label: "预定套餐",
            key: "select-picker",
            component: "yhc-picker",
            placeholder: "请选择",
            disabled: true,
            popup: {
                round: true,
                position: "bottom",
                style: { height: "50vh", overflow: "hidden" },
                closeable: false
            },
            card: {
                title: "title",
                desc: "desc"
            },
            opts: {
                url: "",
                postData: {},
                merge: false,
                multiple: true,
                maxlength: 32,
                text_key: "title",
                contrast_key: "id",
                keyMap: {},
                defaultList: [
                    { id: 1, title: "技术部", desc: "111" },
                    { id: 2, title: "市场部", desc: "111" },
                    { id: 3, title: "人事部", desc: "111" },
                    { id: 4, title: "财务部", desc: "111" },
                    { id: 5, title: "技术部", desc: "111" }
                ]
            },
        },
        {
            label: "备注",
            disabled: true,
            key: "basic-textarea",
            component: "yhc-input",
            type: "textarea",
            placeholder: "请输入",
            autosize: true,
            "show-word-limit": true,
            maxlength: 200,
        },
        // {
        //     label:'查看审批进度请前往钉钉工作台->OA审批->我发起的->云一消费-包间预定,查看审批进度',
        //     component:'yhc-desc'
        // }
    ]
}

// 修改按钮跳转配置
const editRedirectConfig = {
  path: '/reserve/roomReserveAdd', // 跳转到新增页面进行编辑
  query: {
    id: route.query.id, // 传递id参数
    from: 'detail' // 标识来源
  }
}

// 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
const setRight = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title:'预定详情',
  });
};
setRight()
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
