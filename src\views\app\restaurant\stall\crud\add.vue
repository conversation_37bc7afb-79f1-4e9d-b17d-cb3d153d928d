<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="add"
      @onSubmit="onBasicSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const { proxy } = getCurrentInstance();
const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    // 如果有id参数，则为编辑模式
    ...(route.query.id ? { id: route.query.id } : {}),
    dininghall_id:localStorage.getItem('dininghall')
  },
  curl: {
    add: '/stall/post_add', // 新增接口
    edit: '/stall/post_modify', // 编辑接口
    info: '/stall/get_info' // 获取详情接口（编辑时需要）
  },
  groupForm: [
    [0, 1],
    [1, 3]
  ],
  form: [
    {
      label: "档口名称",
      key: "title",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请填写用户名" }],
    },
    {
      label: "消费机",
      key: "device_consumptions",
      component: "yhc-picker",
      placeholder: "请选择",
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false
      },
      card: {
        title: "title",
        desc: "desc"
      },
      opts: {
        url: "",
        postData: {},
        merge: false,
        multiple: true,
        maxlength:32,
        text_key: "title",
        contrast_key: "id",
        keyMap: {},
        defaultList: [
          { id: 1, title: "技术部",desc:"111" },
          { id: 2, title: "市场部",desc:"111" },
          { id: 3, title: "人事部",desc:"111" },
          { id: 4, title: "财务部",desc:"111" },
          { id: 5, title: "技术部",desc:"111" }
        ]
      },
    },
    {
      label: "收银机",
      key: "device_cashiers",
      component: "yhc-picker",
      placeholder: "请选择",
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false
      },
      card: {
        title: "title",
      },
      opts: {
        url: "",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: {},
        defaultList: [
          { id: 1, title: "技术部" },
          { id: 2, title: "市场部" },
          { id: 3, title: "人事部" },
          { id: 4, title: "财务部" }
        ]
      },
    },
  ]
}


// // 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: route.query.id ? '修改档口' : '新增档口',
  });
};
setRightA()
setRight()
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
