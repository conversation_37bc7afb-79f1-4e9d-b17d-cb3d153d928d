<template>
    <van-form @submit="submit">
        <div class="content">
            <van-cell-group inset class="item">
                <!-- 清空人员 -->
                <div class="distribute-section">
                    <div class="distribute-content">
                        <div class="section-title">清空人员</div>
                        <div class="radio-group">
                            <van-radio-group v-model="form.personnel_type" direction="horizontal">
                                <van-radio :name="1" class="radio-item">全部人员</van-radio>
                                <van-radio :name="2" class="radio-item">指定人员</van-radio>
                            </van-radio-group>
                        </div>
                    </div>
                </div>

                <!-- 人员/角色选择 -->
                <template v-if="form.personnel_type == 2">
                    <div class="type-section">
                        <div class="type-content">
                            <div class="section-title">人员/角色</div>
                            <div class="type-buttons">
                                <div :class="['type-btn', { 'active': data.type === 0 }]" @click="switchType(0)">
                                    人员
                                </div>
                                <!-- <div :class="['type-btn', { 'active': data.type === 1 }]" @click="switchType(1)">
                                    角色
                                </div> -->
                            </div>
                        </div>
                    </div>

                    <!-- 内部员工 -->
                    <van-cell title="内部员工" :value="getInternalStaffText()" is-link @click="handleInternalStaff"
                        class="staff-cell" />

                    <!-- 外部人员 -->
                    <van-cell title="外部人员" :value="getExternalStaffText()" is-link @click="handleExternalStaff"
                        class="staff-cell" />
                </template>
            </van-cell-group>

            <!-- 备注 -->
            <van-cell-group inset :style="{ marginTop: '16px' }">
                <div class="remark-section">
                    <div class="section-title">备注</div>
                    <van-field v-model="form.remark" placeholder="请输入内容" type="textarea" rows="3" maxlength="50"
                        show-word-limit class="remark-field" />
                </div>
            </van-cell-group>

            <!-- 提交按钮 -->
            <div class="submit-container">
                <van-button type="primary" native-type="submit" size="large" class="submit-btn">
                    提交
                </van-button>
            </div>
        </div>

    </van-form>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useLoginStore } from "@/store/dingLogin";
import {
    showToast,
    showFailToast,
    showDialog,
} from "vant";
import { useRouter } from "vue-router";

const { proxy } = getCurrentInstance();
const app = useLoginStore();
const router = useRouter();

// 表格数据
let form = ref({
    personnel_type: 2, // 改为字符串，默认指定人员
    remark: app.clearDebt?.remark || "",
    user_ids: [],
    dept_ids: [],
});

// 选择人组件
let data = ref({
    type: app.clearDebt?.type || 0,
});

let userlst = ref({
    users: [],
    departments: [],
});

// 初始化清空欠款的独立状态
app.clearDebt = app.clearDebt || {
    visiterList: [],
    externalUserList: [],
    type: null,
    remark: ''
};

let rolelst = app.clearDebt.visiterList;

// 页面初始化
onMounted(() => {
    // 确保状态正确初始化
    if (!app.clearDebt) {
        app.clearDebt = {
            visiterList: [],
            externalUserList: [],
            type: null,
            remark: ''
        };
    }
    if (app.provideSubsidies.userlst) {
        userlst.value = JSON.parse(JSON.stringify(app.provideSubsidies.userlst));
    }
});

const headbutton = () => {
    // 保存当前状态
    app.clearDebt.type = data.value.type;
    app.clearDebt.remark = form.value.remark;

    // 人员类型选择逻辑（与扣除补贴页面保持一致）
    if (app.browserEnv == "wx") {
        proxy.$_dd.selectEnterpriseContact({
            fromDepartmentId: 0,
            mode: "multi",
            type: ["user"],
            selectedDepartmentIds: userlst.value.departments.map(
                (el) => el.dept_id
            ),
            selectedUserIds: userlst.value.users.map((el) => el.userid),
            success(res) {
                res = res.result;
                userlst.value.users = res.userList.map((it) => {
                    return {
                        userid: it.id,
                        name: "",
                        avatar: "",
                    };
                });
                userlst.value.departments = res.departmentList.map((it) => {
                    return {
                        dept_id: it.id,
                        name: "",
                    };
                });
            },
        });
    } else {
        proxy.$_dd.complexChoose({
            showOrgEcological: true,
            showLabelPick: true,
            rootPage: "CommonOrgContact",
            title: "选择负责人",
            corpId: app.corpId,
            multiple: true,
            maxUsers: 10000,
            disabledUsers: [], //不可选用户
            disabledDepartments: [], //不可选部门
            requiredUsers: [], //必选用户（不可取消选中状态）
            requiredDepartments: [], //必选部门（不可取消选中状态）
            pickedUsers: userlst.value.users.map((el) => el.userid), //已选用户
            pickedDepartments: userlst.value.departments.map((el) => el.dept_id), //已选部门
            appId: app.appid, //微应用id
            onSuccess: function (res) {
                // 选择联系人或部门成功后的回调函数
                console.log(res);
                userlst.value.users = res.users.map((it) => {
                    return {
                        userid: it.emplId,
                        name: it.name,
                        avatar: it.avatar,
                    };
                });
                userlst.value.departments = res.departments.map((it) => {
                    return {
                        dept_id: it.id,
                        name: it.name,
                    };
                });
            },
            onFail: function (err) {
                // 选择联系人或部门失败后的回调函数
            },
        });
    }
};

// 切换人员/角色类型
const switchType = (type) => {
    data.value.type = type;
};

// 获取内部员工显示文本
const getInternalStaffText = () => {
    if (data.value.type === 1) {
        // 角色类型
        if (rolelst && rolelst.length > 0) {
            return `已选择${rolelst.length}个角色`;
        }
    } else {
        // 人员类型
        const departmentCount = userlst.value.departments ? userlst.value.departments.length : 0;
        const userCount = userlst.value.users ? userlst.value.users.length : 0;

        if (departmentCount > 0 && userCount > 0) {
            return `已选择${departmentCount}个部门，${userCount}个成员`;
        } else if (departmentCount > 0) {
            return `已选择${departmentCount}个部门`;
        } else if (userCount > 0) {
            return `已选择${userCount}个成员`;
        }
    }
    return '';
};

// 获取外部人员显示文本
const getExternalStaffText = () => {
    if (app.clearDebt.externalUserList && app.clearDebt.externalUserList.length > 0) {
        return `已选择${app.clearDebt.externalUserList.length}个人员`;
    }
    return '已选择0个人员';
};

// 处理内部员工点击
const handleInternalStaff = () => {
    if (data.value.type === 1) {
        // 角色类型，跳转到清空欠款的选择角色页面
        app.clearDebt.type = data.value.type;
        app.clearDebt.remark = form.value.remark;
        router.push("/clearDebt/crud/selectRole");
    } else {
        // 人员类型，调用选择人员逻辑
        headbutton();
    }
};

// 处理外部人员点击
const handleExternalStaff = () => {
    // 保存当前表单数据
    app.clearDebt.type = data.value.type;
    app.clearDebt.remark = form.value.remark;
    app.provideSubsidies.userlst = JSON.parse(JSON.stringify(userlst.value));
    // 跳转到外部人员选择页面
    router.push('/clearDebt/crud/selectExternalUser');
};

// 提交任务
const submit = () => {
    // 验证人员选择
    let hasInternalStaff = false;
    let hasExternalStaff = false;
    let internalCount = 0;
    let externalCount = 0;

    if (form.value.personnel_type === 1) {
        // 全部人员，无需验证
        hasInternalStaff = true;
    } else {
        // 指定人员，需要验证是否选择了人员
        if (data.value.type === 1) {
            // 角色类型
            internalCount = rolelst.length;
            hasInternalStaff = internalCount > 0;
        } else {
            // 人员类型
            internalCount = userlst.value.users ? userlst.value.users.length : 0;
            hasInternalStaff = internalCount > 0;
        }

        // 检查外部人员数据
        externalCount = app.clearDebt.externalUserList ? app.clearDebt.externalUserList.length : 0;
        hasExternalStaff = externalCount > 0;

        // 如果内部员工和外部人员都没有数据，提示用户
        // if (!hasInternalStaff && !hasExternalStaff) {
        //     showToast({
        //         message: '请选择要清空欠款的人员',
        //         type: 'text',
        //         duration: 2000
        //     });
        //     return;
        // }
    }
    const selectedUsers = userlst.value.users;         // 选中的人员集合
    const selectedDepartments = userlst.value.departments; // 选中的部门集合
    const externalUserList = app.clearDebt.externalUserList; // 选中的部门集合
    form.value.user_ids = []
    if (selectedUsers.length > 0) {
        selectedUsers.forEach(user => {
            form.value.user_ids.push(user.userid.toString());
        });
    }
    if (externalUserList.length > 0) {
        externalUserList.forEach(user => {
            form.value.user_ids.push(user.id.toString());
        });
    }
    form.value.dept_ids = []
    if (selectedDepartments.length > 0) {
        selectedDepartments.forEach(dept => {
            form.value.dept_ids.push(dept.dept_id.toString());
        });
    }
    console.log('提交数据:', form.value);
    proxy.$post("/transaction_flow/clear_debt", form.value).then((res) => {
        if (res.code == 200) {
            // 直接跳转到结果页面（与其他页面保持一致）
            router.push({
                path:'/clearDebt/result',
                query: {
                    results: JSON.stringify(res.data.results)
                }
            });
            console.log(res);
        } else {
            showFailToast({
                message: res.msg || '提交失败，请稍后再试',
                duration: 2000
            });
            return;
        }
    }).catch((err) => {
        showFailToast({
            message: err.msg || '提交失败，请稍后再试',
            duration: 2000
        });
    });

};
</script>

<style lang="scss" scoped>
.content {
    background: #f2f3f4;
    min-height: 100vh;
    padding-bottom: 100px;
}

.item {
    :deep(.van-cell-group) {
        margin: 16px;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }

    :deep(.van-cell) {
        padding-left: 16px;
        padding-right: 16px;
    }
}

// 清空人员样式
.distribute-section {
    height: 54px;
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;

    // 添加与van-cell一致的分割线
    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 16px;
        right: 16px;
        height: 1px;
        background: #ebedf0;
    }

    .distribute-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 16px;
        width: 100%;
        height: 100%;

        .section-title {
            font-size: 16px;
            color: #323233;
            font-weight: 500;
        }

        .radio-group {
            :deep(.van-radio-group) {
                display: flex;
                gap: 20px;
            }

            :deep(.van-radio) {
                .van-radio__icon {
                    .van-icon {
                        border: 1px solid #c8c9cc;
                        border-radius: 50%;
                        width: 16px;
                        height: 16px;
                        background: #fff;
                    }

                    &--checked .van-icon {
                        background: #fff;
                        border-color: #1989fa;
                        color: #1989fa;
                        position: relative;

                        &::before {
                            content: '';
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            width: 8px;
                            height: 8px;
                            background: #1989fa;
                            border-radius: 50%;
                        }
                    }
                }

                .van-radio__label {
                    font-size: 14px;
                    color: #323233;
                    margin-left: 6px;
                }
            }
        }
    }
}

// 人员/角色选择样式
.type-section {
    height: 54px;
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;

    // 添加与van-cell一致的分割线
    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 16px;
        right: 16px;
        height: 1px;
        background: #ebedf0;
    }

    .type-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 16px;
        width: 100%;
        height: 100%;

        .section-title {
            font-size: 16px;
            color: #323233;
            font-weight: 500;
        }

        .type-buttons {
            display: flex;
            background: #f5f5f5;
            border-radius: 6px;
            padding: 2px;

            .type-btn {
                padding: 8px 20px;
                font-size: 14px;
                color: #646566;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.3s ease;

                &.active {
                    background: #1989fa;
                    color: #fff;
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                }
            }
        }
    }
}

// 员工选择样式
.staff-cell {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
    height: 54px;
    align-items: center;

    :deep(.van-cell) {
        padding: 16px;
        min-height: 54px;
        height: 54px;
    }

    :deep(.van-cell__title) {
        font-size: 16px;
        color: #323233;
    }

    :deep(.van-cell__value) {
        font-size: 15px;
        color: #646566;
        word-wrap: break-word;
        word-break: break-all;
    }
}

// 备注样式
.remark-section {
    padding: 16px;

    .section-title {
        font-size: 16px;
        color: #323233;
        font-weight: 500;
        margin-bottom: 12px;
    }

    .remark-field {
        :deep(.van-field__control) {
            font-size: 14px;
            line-height: 1.5;
        }
    }
}

// 提交按钮样式
.submit-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 16px;
    border-top: 1px solid #ebedf0;
    z-index: 100;

    .submit-btn {
        width: 100%;
        height: 50px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 500;
    }
}
</style>