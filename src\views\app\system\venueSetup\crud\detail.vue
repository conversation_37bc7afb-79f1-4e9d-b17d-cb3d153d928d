<template>
    <div class="home-container">
        <!-- yhc-form 组件展示 -->
        <yhc-form :config="basicFormConfig" pageType="detail" @onSubmit="onBasicSubmit"
            :editRedirectConfig="editRedirectConfig" />
    </div>
</template>
<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const route = useRoute()
console.log('route:', route)
const basicFormConfig = {
    button: {
        isShow: true,
    },
    postData: {

        id: route.query.id // 从路由获取id参数
        // 如果有id参数，则为编辑模式
        // ...(route.query.id ? { id: route.query.id } : {})
    },
    curl: {
        del:'/dininghall/post_del',
        add: '/dininghall/post_add', // 新增接口
        edit: '/dininghall/post_modify', // 编辑接口
        info: '/dininghall/get_info' // 获取详情接口（编辑时需要）
    },
    groupForm: [
        [0, 1],
        [1, 2],
        [2, 3],
        [3, 4],
        [4, 5],
        [5, 6],
        [6, 7],
    ],
    form: [
        {
            label: "场所名称",
            key: "title",
            component: "yhc-input",
            type: "text",
            placeholder: "请输入",
            required: false,
            rules: [{ required: true, message: "请填写场所名称" }],
            disabled:true
        },
        {
            label: "档口合并",
            key: "merge_stall",
            component: "yhc-switch",
            required: false,
            disabled:true
        },
        {
            label: "说明：开启后，所有档口合并展示，支持统一发布菜品、统一预定入口",
            component: "yhc-desc"
        },
        {
            label: "菜品免发布",
            key: "exempt_dishes_release",
            component: "yhc-switch",
            required: false,
            disabled:true
        },
        {
            label: "说明：开启后，菜品无需手动发布，即可支持固定金额与自定义金额两种消费方式",
            component: "yhc-desc"
        },
        {
          label: "地址",
          key: "address",
          component: "yhc-input",
          type: "textarea",
          placeholder: "请输入",
          autosize: true,
          "show-word-limit": true,
          maxlength: 30,
          disabled:true
        //   desc: "支持多行文本输入，带字数限制"
        },
        {
            label: "支持多行文本输入，带字数限制",
            component: "yhc-desc"
        },
    ]
}
const editRedirectConfig = {
    path: '/venueSetupAdd', // 跳转到新增页面进行编辑
    query: {
        id: route.query.id, // 传递id参数
        from: 'detail' // 标识来源
    }
}
// // 表单提交处理函数
const onBasicSubmit = (data) => {
    console.log('基础表单提交:', data)
    showToast('基础表单提交成功')
}
</script>

<style lang="scss" scoped>
.home-container {
    padding: 0;
    min-height: 100vh;
    // background: #f7f8fa;
}
</style>
