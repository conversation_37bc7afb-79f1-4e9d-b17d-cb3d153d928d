<template>
  <div class="yp_qr_auth">
    <!-- 页面模式切换：fixed为固定金额，custom为自定义金额 -->
    <div>
      <div>
        <div
          style="
            background: #fff;
            padding: 16px 0;
            padding-top: 40px;
            border-radius: 8px;
            margin-bottom: 24px;
          "
        >
          <van-image
            width="56"
            height="56"
            radius="4"
            src="/index/codeMealErr.png"
          />

          <div style="font-size: 16px; color: #ff5219">付款失败</div>
          <div
            v-if="info.err"
            style="
              font-size: 15px;
              margin: 16px 0;
              color: rgba(23, 26, 29, 0.6);
            "
          >
            {{ info.err }}
          </div>
        </div>
      </div>
    </div>

    <!-- 页面模式切换按钮，仅用于demo演示 -->
    <div
      style="
        text-align: right;
        background: #fff;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 8px;
      "
    >
      <van-button type="primary" @click="goHome">返回首页</van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import { showToast } from "vant";
import { useLoginStore } from "@/store/dingLogin";
import { useRouter, useRoute } from "vue-router";
import dayjs from "dayjs";

const app = useLoginStore();
const router = useRouter();
const route = useRoute();

let info = ref({
  money: 0,
  balance: 0,
});
let { proxy } = getCurrentInstance();
info.value = route.query;
document.addEventListener("backbutton", function (e) {
  proxy.$_dd.biz.navigation.close({
    message: "quit message", //退出信息，传递给openModal或者openSlidePanel的onSuccess函数的result参数
    onSuccess: function (result) {
      /**/
    },
    onFail: function () {},
  });
  // 在这里处理你的业务逻辑
  e.preventDefault(); //backbutton事件的默认行为是回退历史记录，如果你想阻止默认的回退行为，那么可以通过preventDefault()实现
});
const goHome = () => {
  router.push({
    path: "/home",
  });
};
</script>

<style scoped lang="scss">
.yp_qr_auth {
  text-align: center;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0 0;
  .custom-pay-page {
    margin-top: 40px;
    padding: 0 16px;
  }
  .bank-info {
    display: flex;
    align-items: center;
    // justify-content: center;
    margin-bottom: 16px;
  }
  .bank-color-dot {
    width: 12px;
    height: 12px;
    background: #ff9800;
    border-radius: 2px;
    margin-right: 8px;
  }
  .bank-name {
    font-size: 16px;
    color: #333;
    margin-left: 8px;
  }

  // 输入金额区域样式
  .amount-input-area {
    background: #fff;
    border-radius: 8px;
    padding: 24px 20px 16px 20px;
    margin-bottom: 24px;
    text-align: left;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  }
  .amount-input-label {
    font-size: 15px;
    color: #999;
    margin-bottom: 8px;
  }
  .amount-input-box {
    display: flex;
    align-items: center;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    user-select: none;
    min-height: 40px;
  }
  .amount-input-cny {
    color: #333;
    font-size: 28px;
    margin-right: 4px;
  }
  .amount-input-value {
    color: #222;
    font-size: 28px;
    font-weight: bold;
  }
  .amount-input-placeholder {
    color: #ccc;
    font-size: 28px;
    font-weight: normal;
  }

  // 弹出式数字键盘样式
  .keypad-overlay {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    z-index: 1000;
  }
  .keypad-mask {
    flex: 1;
    background: rgba(0, 0, 0, 0.3);
  }
  .popup-keypad {
    width: 100%;
    max-width: 430px; /* 适配移动端最大宽度 */
    background: #f6f6f6; /* Grid gaps color */
    border-top: 1px solid #dcdcdc;
    border-radius: 0; /* 移除顶部圆角 */
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.08);
    display: grid; /* Changed to Grid layout */
    grid-template-columns: repeat(4, 1fr); /* 4 columns */
    grid-template-rows: repeat(4, 54px); /* 4 rows, each 54px height */
    gap: 1px; /* Gap for borders */
    overflow: hidden; /* Ensure rounded corners clip content */
  }
  .key-btn {
    font-size: 22px;
    border: none;
    background: #fff;
    border-radius: 0; /* 移除按钮圆角 */
    box-shadow: none;
    cursor: pointer;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    &:active {
      background: #f2f2f2;
    }
  }
  .key-btn.empty-background-btn {
    background: #f5f5f5; /* Match page background color */
    box-shadow: none;
    cursor: default;
    border-radius: 0; /* 移除按钮圆角 */
  }
  .key-btn.zero-btn {
    grid-column: 1 / span 2; /* Spans 2 columns */
    border-radius: 0; /* 移除按钮圆角 */
  }
  .key-btn.delete-key-btn {
    background: #f5f5f5; /* Match empty background */
    border-radius: 0; /* 移除按钮圆角 */
  }
  .confirm-btn {
    grid-column: 4 / 5;
    grid-row: 2 / span 3;
    background: #1989fa;
    color: #fff;
    font-weight: bold;
    border-radius: 0;
    font-size: 20px;
    box-shadow: none;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    transition: background 0.2s;
  }
  .confirm-btn:disabled {
    background: #b3d8fd;
    color: #fff;
  }
}
</style>
