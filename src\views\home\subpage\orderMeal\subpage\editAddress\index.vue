<template>
  <div class="edit-address-page custom-radio">
    <!-- 地图区域 -->
    <div class="map-wrapper">
      <el-amap ref="map" :center="center" :zoom="zoom" class="amap-box" @moveend="onMapMoveEnd">
      </el-amap>
      <div class="center-marker">
        <img src="https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png" alt="marker" />
      </div>
    </div>
    <!-- 地址信息卡片 -->

    <div class="address-card" v-if="!isEdit">
      <van-button type="default" plain block class="select-address-btn" @click="chooseAddress">选择收货地址</van-button>
    </div>
    <div class="address-card" v-else>
      <div class="address-card-main">
        <div class="address-main">
          {{ form.region }}
        </div>
        <div class="address-sub">
          {{ form.detail }}
        </div>
      </div>
      <van-button size="small" class="edit-address-btn" @click="chooseAddress">修改地址</van-button>
    </div>
    <!-- 表单区域 -->
    <div class="form-container">
      <van-form @submit="onSubmit">
        <div class="form-item">
          <van-field v-model="form.doorplate" label="详细地址" placeholder="请输入详细地址" label-width="80" class="custom-field"
            clearable clear-trigger="focus" />
        </div>

        <div class="form-item" style="display: flex; padding-top: 10px; padding-bottom: 10px;">
          <div style="width: 80%;">
            <van-field v-model="form.name" label="联系人" placeholder="请输入" class="contact-field" clearable
              clear-trigger="focus" />
          </div>
          <div class="custom-radio-wrapper">
            <div class="custom-radio-item" :class="{ active: form.customOption === 'option1' }"
              @click="selectCustomOption('option1')">
              <div class="radio-circle">
                <div class="radio-dot" v-if="form.customOption === 'option1'"></div>
              </div>
              <span class="radio-text">先生</span>
            </div>
            <div class="custom-radio-item" :class="{ active: form.customOption === 'option2' }"
              @click="selectCustomOption('option2')">
              <div class="radio-circle">
                <div class="radio-dot" v-if="form.customOption === 'option2'"></div>
              </div>
              <span class="radio-text">女士</span>
            </div>
          </div>
        </div>

        <div class="form-item">
          <van-field v-model="form.mobile" label="手机号" placeholder="请输入手机号" type="tel" maxlength="11" label-width="80"
            class="custom-field" clearable clear-trigger="focus" />
        </div>

        <div class="form-item default-item">
          <van-checkbox v-model="form.default" class="custom-checkbox">设为默认</van-checkbox>
        </div>
      </van-form>
    </div>
    <!-- 底部按钮 -->
    <div class="footer-btns">
      <van-button v-if="route.query.id" type="default" block class="delete-btn" @click="del">删除地址</van-button>
      <van-button v-if="isEdit" type="primary" block class="save-btn" @click="onSubmit">保存地址</van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import { showToast, showSuccessToast, showFailToast, closeToast } from "vant";
import { useRoute, useRouter } from "vue-router";
// wgs84转高德
import { gps84_To_gcj02 } from "@vuemap/vue-amap";

let { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const center = ref([116.397428, 39.90923]); // 默认北京
const zoom = ref(16);
const map = ref(null);
let isEdit = ref(false);

const form = ref({
  doorplate: "",
  name: "",
  sex: "先生",
  mobile: "",
  detail: "",
  province: "", // 省份
  city: "", // 城市
  region: "", // 区县
  longitude: "", // 经度
  latitude: "", // 纬度
  default: false, // 是否默认 0否 1是
  customOption: "option1", // 自定义单选选项
});
// 获取当前位置
const getCurrentPosition = () => {
  return new Promise((resolve, reject) => {
    // 先使用钉钉定位获取经纬度
    const isAndroid = /android/i.test(navigator.userAgent);
    proxy.$_dd.device.geolocation.get({
      type: 1,
      useCache: true,
      coordinate: isAndroid ? 1 : 0,
      cacheTimeout: 20,
      withReGeocode: true,
      targetAccuracy: "200",
      success: (res) => {
        let longitude, latitude;
        if (!isAndroid) {
          [longitude, latitude] = gps84_To_gcj02(res.latitude, res.longitude);
        } else {
          longitude = parseFloat(res.longitude);
          latitude = parseFloat(res.latitude);
        }
        resolve([longitude, latitude]);
      },
      fail: (e) => {
        reject(e);
      },
      complete: () => { },
    });
  });
};

// 初始化地图位置
const initMapPosition = async () => {
  try {
    // 如果有地址参数，使用传入的地址
    const query = route.query;
    console.log("路由---》", query);
    if (query.id) {
      isEdit.value = true;
      form.value.id = query.id;
      center.value = [Number(query.longitude), Number(query.latitude)];
    } else {
      // 如果没有地址参数，获取当前位置
      const position = await getCurrentPosition();
      center.value = position;
    }
  } catch (error) {
    console.error("初始化地图位置失败:", error);
  }
};

async function onMapMoveEnd(e) {
  try {
    // 获取地图中心点经纬度
    const amap = e.target;
    const center = amap.getCenter();
    form.value.longitude = center.lng.toString();
    form.value.latitude = center.lat.toString();

    // 使用高德地图的 Geocoder 服务进行地址解析
    window.AMap.plugin("AMap.Geocoder", () => {
      const geocoder = new window.AMap.Geocoder();
      geocoder.getAddress(center, (status, result) => {
        if (status === "complete" && result.info === "OK") {
          const regeocode = result.regeocode;
          // 更新地址信息
          form.value.detail = regeocode.formattedAddress;
          // 更新省市区信息
          const addressComponent = regeocode.addressComponent;
          form.value.province = addressComponent.province;
          form.value.city = addressComponent.city;
          form.value.region = addressComponent.region

          form.value.township = addressComponent.township;
          form.value.street = addressComponent.street;
          form.value.streetNumber = addressComponent.streetNumber;

          // 按优先级依次尝试截取
          let detailInfo = '';
          if (regeocode.formattedAddress) {
            // 1. 先尝试从streetNumber后面截取
            if (addressComponent.streetNumber) {
              const numberIndex = regeocode.formattedAddress.indexOf(addressComponent.streetNumber);
              if (numberIndex !== -1) {
                detailInfo = regeocode.formattedAddress.substring(numberIndex + addressComponent.streetNumber.length);
                detailInfo = detailInfo.replace(/^[\s,，。.]+/, '');
              }
            }

            // 2. 如果detailInfo为空，尝试从street后面截取
            if (!detailInfo && addressComponent.street) {
              const streetIndex = regeocode.formattedAddress.indexOf(addressComponent.street);
              if (streetIndex !== -1) {
                detailInfo = regeocode.formattedAddress.substring(streetIndex + addressComponent.street.length);
                detailInfo = detailInfo.replace(/^[\s,，。.]+/, '');
              }
            }

            // 3. 如果detailInfo仍然为空，尝试从region后面截取
            if (!detailInfo && form.value.region) {
              const regionIndex = regeocode.formattedAddress.indexOf(form.value.region);
              if (regionIndex !== -1) {
                detailInfo = regeocode.formattedAddress.substring(regionIndex + form.value.region.length);
                detailInfo = detailInfo.replace(/^[\s,，。.]+/, '');
              }
            }

            // 4. 如果detailInfo仍然为空，尝试从township后面截取
            if (!detailInfo && addressComponent.township) {
              const townshipIndex = regeocode.formattedAddress.indexOf(addressComponent.township);
              if (townshipIndex !== -1) {
                detailInfo = regeocode.formattedAddress.substring(townshipIndex + addressComponent.township.length);
                detailInfo = detailInfo.replace(/^[\s,，。.]+/, '');
              }
            }

            // 5. 如果detailInfo仍然为空，尝试从district后面截取
            if (!detailInfo && addressComponent.district) {
              const districtIndex = regeocode.formattedAddress.indexOf(addressComponent.district);
              if (districtIndex !== -1) {
                detailInfo = regeocode.formattedAddress.substring(districtIndex + addressComponent.district.length);
                detailInfo = detailInfo.replace(/^[\s,，。.]+/, '');
              }
            }
          }

          console.log("地址解析结果:", {
            addressComponent: addressComponent,
            formattedAddress: regeocode.formattedAddress,
            detailInfo: detailInfo,
            province: form.value.province,
            city: form.value.city,
            region: form.value.region,
            detail: form.value.detail,
            longitude: form.value.longitude,
            latitude: form.value.latitude,
          });
          form.value.region = detailInfo
        } else {
          console.error("地址解析失败:", result);
          showToast("获取地址信息失败");
        }
      });
    });
  } catch (error) {
    console.error("获取地址信息失败:", error);
    showToast("获取地址信息失败");
  }
}

const chooseAddress = async () => {
  isEdit.value = true;
  const position = await getCurrentPosition();
  center.value = position;
};

// 选择自定义单选选项
const selectCustomOption = (option) => {
  form.value.customOption = option;
};

function onSubmit() {
  if (!form.value.name || !form.value.mobile) {
    showToast("请填写完整信息");
    return;
  }
  if (!/^1[3-9]\d{9}$/.test(form.value.mobile)) {
    showToast("手机号格式不正确");
    return;
  }

  // 转换default值为字符串
  form.value.default = form.value.default ? 1 : 0;

  // 提交任务
  let api = "";
  if (route.query.id) {
    api = "address/post_modify";
    form.value.id = route.query.id;
  } else {
    api = "address/post_add";
  }

  // 创建FormData对象
  const formData = new FormData();
  Object.keys(form.value).forEach((key) => {
    formData.append(key, form.value[key]);
  });

  // 打印
  proxy
    .$post(api, formData)
    .then((res) => {
      if (res.errcode == 0) {
        showSuccessToast("提交成功");
        setTimeout(() => {
          closeToast();
          router.go(-1);
        }, 1000);
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
}
function del() {
  proxy
    .$post("address/post_del", { id: route.query.id })
    .then((res) => {
      if (res.errcode == 0) {
        showSuccessToast("删除成功");
        setTimeout(() => {
          closeToast();
          router.go(-1);
        }, 1000);
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
}
function getInfo(id) {
  proxy
    .$post("address/get_info", { id: id })
    .then((res) => {
      if (res.errcode == 0) {
        const data = res.result;
        // 更新表单数据
        form.value = {
          id: data.id,
          corpid: data.corpid,
          userid: data.userid,
          name: data.name,
          mobile: data.mobile,
          sex: data.sex,
          province: data.province,
          city: data.city,
          region: data.region,
          detail: data.detail,
          doorplate: data.doorplate,
          longitude: data.longitude.toString(),
          latitude: data.latitude.toString(),
          default: data.default * 1 ? true : false,
          created_at: data.created_at,
          updated_at: data.updated_at,
        };

        // 更新地图中心点
        center.value = [Number(data.longitude), Number(data.latitude)];

        // 更新编辑状态
        isEdit.value = true;
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
      showFailToast("获取地址信息失败");
    });
}

onMounted(() => {
  initMapPosition();
  // 如果有id参数，获取地址详情
  if (route.query.id) {
    getInfo(route.query.id);
  }
});
</script>

<style scoped>
.edit-address-page {
  background: #f7f8fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.map-wrapper {
  position: relative;
  width: 100%;
  height: 160px;
}

.amap-box {
  width: 100%;
  height: 100%;
}

.center-marker {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -100%);
  z-index: 10;
  pointer-events: none;
}

.address-card {
  background: #fff;
  border-radius: 10px;
  margin: -20px 16px 12px 16px;
  padding: 16px 12px 12px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
  z-index: 99999999;
}

.address-card-main {
  flex: 1;
  min-width: 0;
}

.address-main {
  font-size: 16px;
  font-weight: bold;
  color: #222;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.address-sub {
  font-size: 13px;
  color: #888;
  margin-bottom: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.edit-address-btn {
  color: #1989fa;
  border: 1.2px solid #1989fa;
  background: #fff;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 400;
  height: 28px;
  line-height: 28px;
  min-width: 64px;
  padding: 0 12px;
  box-sizing: border-box;
  text-align: center;
  margin-left: 12px;
}

/* 表单容器样式 */
.form-container {
  background: #fff;
  margin: 12px 16px 0 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 表单项样式 */
.form-item {
  position: relative;
  border-bottom: 1px solid #f5f5f5;
  padding: 5px 5px 5px 5px; 
}

.form-item:last-child {
  border-bottom: none;
}

/* 自定义字段样式 */
.custom-field {
  --van-field-label-color: #333;
  --van-field-input-text-color: #333;
  --van-field-placeholder-text-color: #999;
  --van-field-label-width: 80px;
  padding: 16px 16px;
}

.custom-field .van-field__label {
  font-size: 16px;
  font-weight: 400;
  color: #333;
}

.custom-field .van-field__control input {
  font-size: 16px;
  color: #333;
}

.custom-field .van-field__control input::placeholder {
  color: #999;
  font-size: 16px;
}

/* 联系人行样式 */
.contact-row {
  display: flex;
}

.contact-field {}

.contact-field .van-field__label {
  font-size: 16px;
  font-weight: 400;
  color: #333;
  width: 60px;
  /* 固定标签宽度 */
  flex-shrink: 0;
}

.contact-field .van-field__control {
  flex: 1;
  min-width: 0;
}

.contact-field .van-field__control input {
  font-size: 16px;
  color: #333;
  width: 100%;
}

.contact-field .van-field__control input::placeholder {
  color: #999;
  font-size: 16px;
}

/* 性别选择组样式 */
.gender-group {
  width: 150px;
  /* flex: 0 0 auto;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px; */
}

.custom-radio-group {
  display: flex;
  gap: 16px;
}

.custom-radio {
  --van-radio-size: 18px;
  --van-radio-border-color: #ddd;
  --van-radio-checked-icon-color: #1989fa;
  font-size: 16px;
  color: #333;
}

.custom-radio .van-radio__label {
  margin-left: 6px;
  font-size: 16px;
  color: #333;
}

/* 手写单选组件样式 */
.custom-radio-wrapper {
  display: flex;

  /* display: flex;
  gap: 16px;
  padding: 16px 16px 16px 12px;
  flex-shrink: 0;
  align-items: center;
  border-left: 1px solid #f5f5f5; */
  /* 添加左边框分隔 */
}

.custom-radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 2px 4px;
  border-radius: 4px;
  white-space: nowrap;
  /* 防止文字换行 */
}

/* .custom-radio-item:hover {
  background-color: #f5f7fa;
} */

.custom-radio-item.active .radio-text {
  color: #1989fa;
  font-weight: 500;
}

.radio-circle {
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.custom-radio-item.active .radio-circle {
  border-color: #1989fa;
}

.radio-dot {
  width: 6px;
  height: 6px;
  background-color: #1989fa;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.radio-text {
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease;
  user-select: none;
}

/* 默认地址选项样式 */
.default-item {
  padding: 16px;
}

.custom-checkbox {
  --van-checkbox-size: 18px;
  /* --van-checkbox-border-color: #ddd; */
  /* --van-checkbox-checked-icon-color: #1989fa; */
  font-size: 16px;
  color: #333;
}

.custom-checkbox .van-checkbox__label {
  margin-left: 8px;
  font-size: 16px;
  color: #333;
}

/* 底部按钮样式 */
.footer-btns {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #fff;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
}

.delete-btn {
  flex: 1;
  margin-right: 8px;
  color: #666;
  background: #f7f8fa;
  border: 1px solid #eee;
  border-radius: 8px;
  font-size: 16px;
  height: 44px;
}

.save-btn {
  flex: 1;
  margin-left: 8px;
  border-radius: 8px;
  font-size: 16px;
  height: 44px;
}

/* 选择地址按钮样式 */
.select-address-btn {
  width: 100%;
  color: #1989fa;
  border: 1.5px solid #1989fa;
  background: #fff;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 400;
  margin: 0 auto 12px auto;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;
  text-align: center;
}

/* 页面整体样式优化 */
.edit-address-page {
  padding-bottom: 80px;
  /* 为底部按钮留出空间 */
}

/* 表单项悬停效果 */
.form-item:active {
  background-color: #f8f9fa;
}

/* 输入框聚焦样式 */
.custom-field .van-field__control input:focus,
.contact-field .van-field__control input:focus {
  outline: none;
}

/* 单选按钮和复选框的激活状态 */
.custom-radio:active,
.custom-checkbox:active {
  opacity: 0.7;
}
</style>
