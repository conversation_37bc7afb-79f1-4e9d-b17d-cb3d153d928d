# yhc-list

## 介绍

yhc-list 是一个基于 Vant 的高级列表组件，支持搜索、筛选、分页加载、骨架屏等功能。

## 引入

```js
import yhcList from '@/components/yhc-list'
```

## 代码演示

### 基础用法

```vue
<template>
  <yhc-list :config="listConfig">
    <template #default="{ item, index }">
      <div class="list-item">
        <div class="title">{{ item.title }}</div>
        <div class="desc">{{ item.description }}</div>
      </div>
    </template>
  </yhc-list>
</template>

<script setup>
const listConfig = {
  curl: {
    ls: 'api/list'
  }
}
</script>
```

### 启用骨架屏

```vue
<template>
  <yhc-list :config="listConfig">
    <template #default="{ item, index }">
      <div class="list-item">{{ item.title }}</div>
    </template>
  </yhc-list>
</template>

<script setup>
const listConfig = {
  curl: {
    ls: 'api/list'
  },
  skeleton: {
    isShow: true, // 启用骨架屏
    count: 5, // 骨架屏数量
    duration: 2000, // 显示时长
    avatar: true, // 显示头像占位
    title: true // 显示标题占位
  }
}
</script>
```

### 带标签页的列表

```vue
<template>
  <yhc-list :config="listConfig" @onClickTab="onTabChange">
    <template #default="{ item, index, tab }">
      <div class="list-item">{{ item.title }}</div>
    </template>
  </yhc-list>
</template>

<script setup>
const listConfig = {
  curl: {
    ls: 'api/list'
  },
  tabs: {
    isShow: true,
    list: [
      { text: '全部', id: 0 },
      { text: '待处理', id: 1 },
      { text: '已完成', id: 2 }
    ]
  }
}

const onTabChange = (tabData) => {
  console.log('切换标签页:', tabData)
}
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| config | 列表配置 | object | - |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| onButClick | 按钮点击事件 | event, activeTab |
| onClickTab | 标签页点击事件 | tabData |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| refresh | 刷新列表 | - |
| del | 删除项目 | deleteData |

### Slots

| 名称 | 说明 | 参数 |
|------|------|------|
| default | 列表项内容 | { item, index, tab, list } |

### Config 配置

```js
{
  curl: {
    ls: '', // 列表接口
    del: '' // 删除接口
  },
  postData: {}, // 请求参数
  mockData: false, // 是否使用模拟数据
  search: {
    isShow: true, // 是否显示搜索
    isShowPopup: true // 是否显示筛选弹窗
  },
  tabs: {
    isShow: false, // 是否显示标签页
    sticky: true, // 是否吸顶
    swipeable: false, // 是否可滑动
    list: [] // 标签页列表
  },
  button: {
    isShow: true, // 是否显示底部按钮
    text: '新增' // 按钮文字
  },
  skeleton: {
    isShow: false, // 是否启用骨架屏
    count: 5, // 骨架屏数量
    row: 3, // 每个骨架屏的行数
    rowWidth: ['100%', '60%', '80%'], // 每行的宽度
    avatar: true, // 是否显示头像占位
    avatarSize: '40px', // 头像大小
    avatarShape: 'round', // 头像形状 round/square
    title: true, // 是否显示标题占位
    titleWidth: '50%', // 标题宽度
    duration: 2000 // 骨架屏显示时长(毫秒)
  },
  popup: {
    round: false, // 是否圆角
    position: 'right', // 弹出位置
    style: {}, // 弹窗样式
    closeable: false // 是否显示关闭按钮
  },
  filter: {
    // 筛选表单配置
  }
}
```

## 骨架屏功能

骨架屏是一种在内容加载前显示的占位符，可以提升用户体验。

### 特性

- 🎨 **可定制样式** - 支持自定义行数、宽度、头像等
- ⏱️ **灵活时长** - 可设置显示时长
- 🔄 **智能显示** - 仅在首次加载时显示
- 📱 **移动端优化** - 适配移动端交互

### 配置说明

- `isShow`: 控制是否启用骨架屏功能
- `count`: 设置显示的骨架屏数量
- `duration`: 设置骨架屏显示时长（毫秒）
- `avatar`: 是否显示头像占位符
- `title`: 是否显示标题占位符
- `row`: 每个骨架屏的文本行数
- `rowWidth`: 每行文本的宽度数组

### 最佳实践

1. 建议在网络较慢的环境下启用骨架屏
2. 骨架屏的结构应该与实际内容结构相似
3. 显示时长建议设置为 1-3 秒
4. 可以根据实际数据加载时间动态调整显示时长
