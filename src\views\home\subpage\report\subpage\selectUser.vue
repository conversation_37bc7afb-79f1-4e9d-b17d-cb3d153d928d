<template>
  <div class="wrapper-select">
    <van-search
      v-show="config.curDept"
      v-model="config.searchValue"
      placeholder="请输入搜索关键词"
      :clearable="false"
      @search="onSearch"
      @update:model-value="onSearch"
    >
    </van-search>
    <div class="cur-dept" @click="onDeptClick(null)">
      {{ config.curDept ? config.curDept.name : "通讯录" }}
    </div>
    <div
      v-if="config.curDept"
      class="cur-dept"
      style="margin-top: 10px; color: #000; display: flex; align-items: center"
    >
      <van-image
        :show-loading="false"
        width="20px"
        height="20px"
        fit="cover"
        :src="
          config.selectAll
            ? '/images/common/select.png'
            : '/images/common/not-select.png'
        "
        style="margin-right: 4px"
        @click="onSelectAll"
      />
      全选
    </div>
    <van-list
      v-show="config.curDept"
      style="margin-top: 10px"
      v-model:loading="config.loading"
      :finished="config.finished"
      finished-text="没有更多了"
      :immediate-check="false"
      @load="getUser"
    >
      <van-cell
        v-for="item in userList"
        :key="item.dept_id"
        :title="item.name"
        :icon="item.avatar"
        @click="onUserClick(item)"
      >
        <template #icon>
          <van-image
            :show-loading="false"
            width="20px"
            height="20px"
            fit="cover"
            :src="
              item.select
                ? '/images/common/select.png'
                : '/images/common/not-select.png'
            "
            style="margin-right: 4px"
          />
        </template>
        <template #title>
          <div style="display: flex; align-items: center">
            <van-image
              width="34px"
              radius="5px"
              height="34px"
              fit="cover"
              :src="item.avatar"
              style="margin-right: 4px"
            />
            {{ item.name }}
          </div>
        </template>
      </van-cell>
    </van-list>

    <van-list
      v-show="!config.curDept"
      style="margin-top: 10px"
      v-model:loading="config.loading"
      :finished="config.finished"
      finished-text="没有更多了"
      @load="getDept"
    >
      <van-cell v-for="item in deptList" :key="item.dept_id" :title="item.name">
        <template #value>
          <span style="color: #007fff" @click="onDeptClick(item)">
            <div style="display: inline-flex; align-items: center">
              <van-image
                width="16px"
                height="16px"
                fit="cover"
                :src="'/images/common/dept.png'"
                style="margin-right: 4px"
              />
              下级
            </div>
          </span>
        </template>
      </van-cell>
    </van-list>
    <div class="bottom" v-if="config.curDept">
      <span>已选择({{ appInfo.deptList.length }})：</span>
      <div class="select">
        <van-image
          @click="config.showBottom = true"
          v-for="item in appInfo.deptList"
          :key="item.id"
          width="26px"
          height="26px"
          fit="cover"
          radius="5px"
          :src="item.avatar"
          style="margin-right: 10px"
        />
      </div>
      <van-button type="primary" size="small" @click="onConfirmClick"
        >确定</van-button
      >
    </div>
    <!-- 底部弹出 -->
    <van-popup
      v-model:show="config.showBottom"
      position="bottom"
      :style="{ height: '50%' }"
    >
      <div
        style="
          position: relative;
          height: 100%;
          overflow: auto;
          background: #f6f6f6;
        "
      >
        <div class="popup-cust-title">
          <span></span><span>已选择({{ appInfo.deptList.length }})</span
          ><span style="color: #007fff" @click="config.showBottom = false"
            >确定</span
          >
        </div>
        <div style="margin: 50px 0 80px; background: #fff">
          <van-cell
            v-for="item in appInfo.deptList"
            :key="item.dept_id"
            :title="item.name"
          >
            <template #title>
              <div style="display: flex; align-items: center">
                <van-image
                  width="34px"
                  radius="5px"
                  height="34px"
                  fit="cover"
                  :src="item.avatar"
                  style="margin-right: 4px"
                />
                {{ item.name }}
              </div>
            </template>
            <template #value>
              <van-tag
                size="medium"
                plain
                color="rgba(0,0,0,0.4)"
                text-color="rgba(0,0,0,0.6)"
                type="primary"
                @click="onUserClick(item)"
                >移除</van-tag
              >
            </template>
          </van-cell>
        </div>
        <div class="popup-cust-bottom" @click="config.showBottom = false">
          取消
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance } from "vue";
import {
  showToast,
  closeToast,
  showLoadingToast,
  showConfirmDialog,
} from "vant";
import { useLoginStore } from "@/store/dingLogin";
const { proxy } = getCurrentInstance();
const appInfo = useLoginStore();
const router = useRouter();
let config = reactive({
  searchValue: "",
  selectAll: false,
  curDept: null,
  loading: false,
  finished: false,
  showBottom: false,
});
appInfo.deptList = appInfo.deptList || [];
let deptList = [];
let userList = ref([]);
const onConfirmClick = () => {
  router.back();
};
const onSelectAll = () => {
  appInfo.deptList = [];
  config.selectAll = !config.selectAll;
  userList.value.forEach((element) => {
    element.select = config.selectAll;
  });
  if (config.selectAll) {
    appInfo.deptList.push(...userList.value);
  } else {
    appInfo.deptList = [];
  }
};
const onUserClick = (item) => {
  item.select = !item.select;
  let i = appInfo.deptList.findIndex((el) => el.id == item.id);
  if (i != -1) {
    if (item.select) {
    } else {
      appInfo.deptList.splice(i, 1);
    }
  } else {
    if (item.select) {
      appInfo.deptList.push(item);
    } else {
    }
  }
};
const onSearch = (e) => {
  page.page = 1;
  config.finished = false;
  userList.value = [];
  getUser();
};
const onDeptClick = (item) => {
  config.curDept = item;
  appInfo.dept = item;
  config.finished = false;
  page.page = 1;
  userList.value=[]
  getUser();
};
let page = {
  page: 1,
  per_page: 20,
};
const getUser = (data = {}) => {
  Object.assign(data, page);
  if (config.curDept) {
    data.dept_id = config.curDept.dept_id;
  }
  if (config.searchValue) {
    Object.assign(data, { keyword: config.searchValue });
  }
  page.page++;
  proxy
    .$post("user/get_ls", data)
    .then((res) => {
      if (!res.errcode) {
        res = res.result.data;
        appInfo.deptList.forEach((el) => {
          res.forEach((sel, i) => {
            if (el.userid == sel.userid) {
              res[i] = el;
            }
          });
        });
        userList.value.push(...res);
        config.loading = false;
        if (res.length < page.per_page) {
          config.finished = true;
        }
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
      setTimeout(() => {
        closeToast();
      }, 3000);
    });
};
const getDept = (e) => {
  proxy
    .$post("dept/get_user_all", {})
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        deptList = res;
        config.loading = false;
        config.finished = true;
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
      setTimeout(() => {
        closeToast();
      }, 3000);
    });
};
getDept();
</script>
<style lang="scss" scoped>
.wrapper-select {
  margin-bottom: 50px;
}
.cur-dept {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.5);
  padding: 16px;
  background: #fff;
}
.van-cell {
  align-items: center;
}
.bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  height: 40px;
  padding: 0 16px;
  .select {
    flex: 1;
    overflow: auto;
    white-space: nowrap;
    margin-right: 16px;
  }
}
.popup-cust-title {
  display: flex;
  justify-content: space-between;
  padding: 8px 16px;
  position: fixed;
  border: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: #fff;
}
.popup-cust-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: #fff;
  padding: 8px 16px;
  text-align: center;
}
</style>
