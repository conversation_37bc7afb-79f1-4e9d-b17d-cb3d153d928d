<template>
  <div class="wrapper" :class="{ 'required-field': config.required }">
    <van-field v-model="value" is-link :name="config.key" :label="config.label"
      :placeholder="config.placeholder + config.label" :required="config.required" :rules="config.rules"
      :disabled="config.disabled" readonly input-align="left" @click="onClick">
      <template #input v-if="value">
        <span :class="config.disabled == true ? 'valueClass' : ''">{{ value }}</span>
        <van-tag v-if="showCrossDayTag" type="warning" size="medium" round class="cross-day-tag">
          跨天
        </van-tag>
      </template>
    </van-field>
    <yhc-picker-datetime :values="value" :type="config.type" :showPicker="showPicker" @changeValue=" showPicker = false"
      @confirm="onConfirm" />
  </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance, computed, watch } from "vue";
import { deepAssign } from "@/untils";
import { showToast } from "vant";
import { useLoginStore } from "@/store/dingLogin";
import { isCrossDay, supportsCrossDay, formatCrossDayTimeDisplay } from "@/untils/timeValidation";
const app = useLoginStore();
const { proxy } = getCurrentInstance();
const showPicker = ref(false); //弹框显隐
let config = {
  // 基础配置
  label: "日期选择",       // 字段标签 (字符串) - 显示在输入框左侧的标签文字
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"start_time", "end_time"
  placeholder: "请选择",    // 占位符 (字符串) - 未选择日期时显示的提示文字
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用选择, false: 可正常选择
  rules: [],               // 验证规则 (数组) - 表单验证规则配置

  // 日期类型配置
  type: "datetime",        // 日期选择类型 (字符串) - "datetime": 日期时间, "date": 日期, "time": 时间, "time-full": 完整时间, "time-short": 短时间
  format: "yyyy-MM-dd HH:mm:ss", // 日期格式 (字符串) - 日期显示和存储格式，如"yyyy-MM-dd", "HH:mm:ss"

  // 默认值配置
  default: false,          // 是否使用当前时间作为默认值 (布尔值) - true: 默认当前时间, false: 无默认值

  // 高级配置
  typeLabel: "",           // 类型标识 (字符串) - 用于标识开始时间或结束时间，如"start", "end"
  crossDayConfig: null     // 跨天配置对象 (对象) - 包含关联字段信息，用于跨天时间验证和处理
};

// 获取当前时间的默认值函数
function getCurrentTimeDefault (type) {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  switch (type) {
    case 'datetime':
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    case 'date':
      return `${year}-${month}-${day}`;
    case 'time':
    case 'time-full':
      return `${hours}:${minutes}:${seconds}`;
    case 'time-short':
      return `${hours}:${minutes}`;
    default:
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
}

const props = defineProps({
  config: Object,
  form: Object,
});
props.config && deepAssign(config, props.config);

// 只有在配置了 default 为 true 且表单中没有值时，才设置默认值为当前时间
if (config.default && !props.form[config.key]) {
  props.form[config.key] = getCurrentTimeDefault(config.type);
}
let value = ref(props.form[config.key]);

// 跨天标签显示逻辑
const showCrossDayTag = computed(() => {
  // 只有时间类型且配置了跨天相关信息才显示跨天标签
  if (!supportsCrossDay(config.type) || !config.crossDayConfig) {
    return false;
  }

  const { startTimeKey, endTimeKey } = config.crossDayConfig;
  const startTime = props.form[startTimeKey];
  const endTime = props.form[endTimeKey];

  // 只有结束时间字段才显示跨天标签
  if (config.typeLabel !== 'end' || !startTime || !endTime) {
    return false;
  }

  return isCrossDay(startTime, endTime);
});

watch(
  () => props.form[config.key],
  (v) => {
    value.value = v;
  }
);
const onConfirm = (selectedValues) => {
  props.form[config.key] = selectedValues;
  value.value = selectedValues;
  showPicker.value = false;
};
const onClick = (e) => {
  if (config.disabled === true) {
    return showPicker.value = false
  }
  // if (app.browserEnv == "wx") {
  showPicker.value = true;
  // } else {
  //   let obj = {
  //     date: "datepicker",
  //     time: "timepicker",
  //     datetime: "datetimepicker",
  //   };
  //   proxy.$_dd.biz.util[obj[config.type]]({
  //     format: config.format,
  //     value: value.value, //默认显示
  //     onSuccess: function (result) {
  //       //onSuccess将在点击完成之后回调
  //       /*{
  //           value: "2015-06-10 09:50"
  //       }
  //       */
  //       value.value = result.value;
  //       props.form[config.key] = result.value;
  //     },
  //     onFail: function (err) {},
  //   });
  // }
};
</script>
<style lang="scss" scoped>
.wrapper {
  .valueClass {
    color: #dcdcdc
  }

  .cross-day-tag {
    position: absolute;
    right: 10px;
    top: 3px;
    z-index: 1;
  }

  .van-field {
    font-size: 16px;
  }

  &.required-field {
    .van-field {
      --van-field-label-margin-right: 20px;

      :deep(.van-field__label) {
        margin-left: -8px !important;
      }
    }

  }

}
</style>
