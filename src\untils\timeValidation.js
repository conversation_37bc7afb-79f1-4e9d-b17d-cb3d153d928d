/**
 * 时间验证工具函数
 * 支持跨天时间选择功能的验证逻辑
 */

/**
 * 检查时间类型是否支持跨天功能
 * @param {string} timeType - 时间类型
 * @returns {boolean} 是否支持跨天
 */
export function supportsCrossDay(timeType) {
  return ['time', 'time-short', 'time-full'].includes(timeType);
}

/**
 * 验证时间格式是否正确
 * @param {string} timeStr - 时间字符串
 * @param {string} timeType - 时间类型
 * @returns {boolean} 格式是否正确
 */
export function validateTimeFormat(timeStr, timeType = 'time-short') {
  if (!timeStr) return false;
  
  const timeRegexMap = {
    'time': /^([01]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/,
    'time-full': /^([01]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/,
    'time-short': /^([01]?[0-9]|2[0-3]):([0-5][0-9])$/
  };
  
  const regex = timeRegexMap[timeType];
  return regex ? regex.test(timeStr) : false;
}

/**
 * 判断是否为跨天时间
 * @param {string} startTime - 开始时间 (HH:mm 或 HH:mm:ss)
 * @param {string} endTime - 结束时间 (HH:mm 或 HH:mm:ss)
 * @returns {boolean} 是否跨天
 */
export function isCrossDay(startTime, endTime) {
  if (!startTime || !endTime) return false;
  
  // 转换为分钟数进行比较
  const startMinutes = timeToMinutes(startTime);
  const endMinutes = timeToMinutes(endTime);
  
  return endMinutes < startMinutes;
}

/**
 * 将时间字符串转换为分钟数
 * @param {string} timeStr - 时间字符串 (HH:mm 或 HH:mm:ss)
 * @returns {number} 分钟数
 */
function timeToMinutes(timeStr) {
  const parts = timeStr.split(':');
  const hours = parseInt(parts[0], 10);
  const minutes = parseInt(parts[1], 10);
  return hours * 60 + minutes;
}

/**
 * 计算跨天时间差（分钟）
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @returns {number} 时间差（分钟）
 */
export function getCrossDayTimeDiff(startTime, endTime) {
  if (!startTime || !endTime) return 0;
  
  const startMinutes = timeToMinutes(startTime);
  const endMinutes = timeToMinutes(endTime);
  
  if (endMinutes >= startMinutes) {
    // 同一天
    return endMinutes - startMinutes;
  } else {
    // 跨天：到午夜的时间 + 从午夜到结束时间
    return (24 * 60 - startMinutes) + endMinutes;
  }
}

/**
 * 格式化跨天时间显示
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @returns {object} 格式化结果
 */
export function formatCrossDayTimeDisplay(startTime, endTime) {
  const crossDay = isCrossDay(startTime, endTime);
  const duration = getCrossDayTimeDiff(startTime, endTime);
  
  const hours = Math.floor(duration / 60);
  const minutes = duration % 60;
  
  let durationText = '';
  if (hours > 0) {
    durationText += `${hours}小时`;
  }
  if (minutes > 0) {
    durationText += `${minutes}分钟`;
  }
  if (duration === 0) {
    durationText = '0分钟';
  }
  
  const displayText = crossDay 
    ? `${startTime} - ${endTime} (跨天)`
    : `${startTime} - ${endTime}`;
  
  return {
    isCrossDay: crossDay,
    duration,
    durationText,
    displayText
  };
}

/**
 * 创建跨天时间验证规则
 * @param {string} startTimeKey - 开始时间字段名
 * @param {string} endTimeKey - 结束时间字段名
 * @param {object} options - 验证选项
 * @returns {function} 验证函数
 */
export function createCrossDayValidator(startTimeKey, endTimeKey, options = {}) {
  const {
    timeType = 'time-short',
    allowCrossDay = true,
    minDuration = 0, // 最小持续时间（分钟）
    maxDuration = 24 * 60, // 最大持续时间（分钟）
    messages = {}
  } = options;
  
  const defaultMessages = {
    required: '请选择时间',
    format: '时间格式不正确',
    minDuration: `持续时间不能少于${Math.floor(minDuration / 60)}小时${minDuration % 60}分钟`,
    maxDuration: `持续时间不能超过${Math.floor(maxDuration / 60)}小时${maxDuration % 60}分钟`,
    crossDayNotAllowed: '不允许跨天时间选择',
    invalidTimeRange: '结束时间必须晚于开始时间'
  };
  
  const finalMessages = { ...defaultMessages, ...messages };
  
  return (rule, value, callback, source, options) => {
    const form = options.form || source;
    const startTime = form[startTimeKey];
    const endTime = form[endTimeKey];
    
    // 基础验证
    if (!value) {
      return callback(new Error(finalMessages.required));
    }
    
    if (!validateTimeFormat(value, timeType)) {
      return callback(new Error(finalMessages.format));
    }
    
    // 如果开始时间和结束时间都存在，进行跨天验证
    if (startTime && endTime) {
      const crossDay = isCrossDay(startTime, endTime);
      const duration = getCrossDayTimeDiff(startTime, endTime);
      
      // 检查是否允许跨天
      if (crossDay && !allowCrossDay) {
        return callback(new Error(finalMessages.crossDayNotAllowed));
      }
      
      // 检查持续时间
      if (duration < minDuration) {
        return callback(new Error(finalMessages.minDuration));
      }
      
      if (duration > maxDuration) {
        return callback(new Error(finalMessages.maxDuration));
      }
      
      // 如果不允许跨天且结束时间不晚于开始时间
      if (!allowCrossDay && !crossDay && duration === 0) {
        return callback(new Error(finalMessages.invalidTimeRange));
      }
    }
    
    callback();
  };
}
