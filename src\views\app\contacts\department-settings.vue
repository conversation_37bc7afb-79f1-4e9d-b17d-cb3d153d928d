<template>
  <div class="department-settings-page">
    <!-- 表单内容 -->
    <div class="form-container">
      <div class="group-wrapper">
        <van-field v-model="departmentName" label="部门名称" placeholder="请输入名称" class="form-field" />
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="bottom-action">
      <div class="button-group">
        <van-button type="default" size="large" class="delete-btn" @click="handleDelete">
          删除
        </van-button>
        <van-button type="primary" size="large" class="modify-btn" @click="handleModify"
          :disabled="!departmentName.trim()">
          修改
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'

// 路由相关
const route = useRoute()
const router = useRouter()

// 页面状态
const departmentName = ref('')

// 删除部门
const handleDelete = async () => {
  try {
    await showConfirmDialog({
      title: '删除部门',
      message: '是否确定删除部门？',
      confirmButtonText: '删除部门',
      cancelButtonText: '取消',
      confirmButtonColor: '#ee0a24'
    })

    console.log('删除部门:', {
      name: departmentName.value,
      id: route.query.id
    })

    showSuccessToast('部门删除成功')

    // 延迟返回上一页
    setTimeout(() => {
      router.back()
    }, 1500)
  } catch (error) {
    // 用户取消删除
    console.log('取消删除')
  }
}

// 修改部门
const handleModify = () => {
  if (!departmentName.value.trim()) {
    showToast({
      message: '请输入部门名称',
      type: 'fail'
    })
    return
  }

  console.log('修改部门:', {
    name: departmentName.value.trim(),
    id: route.query.id,
    originalName: route.query.name
  })

  showSuccessToast('部门修改成功')

  // 延迟返回上一页
  setTimeout(() => {
    router.back()
  }, 1500)
}

// 加载部门数据
const loadDepartmentData = () => {
  // 从路由参数获取部门信息
  const deptName = route.query.name || '外部部门'
  departmentName.value = deptName
}

// 页面挂载时加载数据
onMounted(() => {
  loadDepartmentData()
})
</script>

<style lang="scss" scoped>
.department-settings-page {
  min-height: 100vh;
  background: #f2f3f4;
  display: flex;
  flex-direction: column;
  padding-bottom: 100px; // 为底部固定按钮留出空间
}

.form-container {
  flex: 1;
  background: #f2f3f4;
  box-sizing: border-box;
  border-top: 0.5px solid #f2f3f4;
  padding-bottom: 0px;

  .group-wrapper {
    margin: 16px;
    border-radius: 8px;
    overflow: hidden;
    background: white;

    .form-field {
      font-size: 16px;
      padding: 16px;

      :deep(.van-field__label) {
        color: #323233;
        font-weight: 500;
      }

      :deep(.van-field__control) {
        color: #323233;

        &::placeholder {
          color: #c8c9cc;
        }
      }
    }
  }
}

.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;

  .button-group {
    display: flex;
    gap: 12px;

    .delete-btn {
      flex: 1;
      height: 48px;
      border-radius: 24px;
      font-size: 16px;
      font-weight: 500;
      background: #f7f8fa;
      border-color: #f7f8fa;
      color: #646566;
      border: 1px solid #e0e2e4;

      &:hover {
        background: #ebedf0;
        border-color: #ebedf0;
      }
    }

    .modify-btn {
      flex: 1;
      height: 48px;
      border-radius: 24px;
      font-size: 16px;
      font-weight: 500;

      &:disabled {
        background: #c8c9cc;
        border-color: #c8c9cc;
      }
    }
  }
}
</style>
