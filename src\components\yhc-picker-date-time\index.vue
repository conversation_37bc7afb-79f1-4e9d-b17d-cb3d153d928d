<template>
  <div class="wrapper">
    <van-field v-model="value" is-link :name="config.key" :label="config.label"
      :placeholder="config.placeholder + config.label" :required="config.required" :rules="config.rules"
      :disabled="config.disabled" readonly input-align="right" @click="onClick">
      <template #input v-if="value">
        {{ value }}
      </template>
    </van-field>
    <yhc-picker-datesecond :values="value" :type="config.type" :showPicker="showPicker" @changeValue="showPicker = false"
      @confirm="onConfirm" />
  </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance } from "vue";
import { deepAssign } from "@/untils";
import { showToast } from "vant";
import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();
const { proxy } = getCurrentInstance();
const showPicker = ref(false); // 弹框显隐状态 (布尔值) - 控制日期时间选择器弹窗的显示和隐藏

// 日期时间选择器配置
let config = {
  // 基础配置
  label: "日期选择",       // 字段标签 (字符串) - 显示在输入框左侧的标签文字
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"start_time", "end_time"
  placeholder: "请选择",    // 占位符 (字符串) - 未选择日期时显示的提示文字
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用选择, false: 可正常选择
  rules: [],               // 验证规则 (数组) - 表单验证规则配置

  // 日期时间配置
  type: "datetime",        // 选择器类型 (字符串) - "datetime": 日期时间, "date": 日期, "time": 时间
  format: "yyyy-MM-dd HH:mm:ss", // 日期格式 (字符串) - 日期时间的显示和存储格式

  // 兼容性配置
  // 支持钉钉原生选择器和Web选择器两种模式
  // 当前版本优先使用Web选择器以保证跨平台兼容性
};
const props = defineProps({
  config: Object,
  form: Object,
});
props.config && deepAssign(config, props.config);
let value = ref(props.form[config.key]);
watch(
  () => props.form[config.key],
  (v, o) => {
    value.value = v;
  }
);
const onConfirm = (selectedValues) => {
  props.form[config.key] = selectedValues;
  value.value = selectedValues;
  showPicker.value = false;
};
const onClick = (e) => {
  // if (app.browserEnv == "wx") {
  showPicker.value = true;
  // } else {
  //   let obj = {
  //     date: "datepicker",
  //     time: "timepicker",
  //     datetime: "datetimepicker",
  //   };
  //   proxy.$_dd.biz.util[obj[config.type]]({
  //     format: config.format,
  //     value: value.value, //默认显示
  //     onSuccess: function (result) {
  //       //onSuccess将在点击完成之后回调
  //       /*{
  //           value: "2015-06-10 09:50"
  //       }
  //       */
  //       value.value = result.value;
  //       props.form[config.key] = result.value;
  //     },
  //     onFail: function (err) {},
  //   });
  // }
};
</script>
<style lang="scss" scoped>
.wrapper {}
</style>
