<template>
    <div class="skeleton-demo-container">
        <!-- 列表组件演示 -->
        <yhc-list :key="listConfig.key || 'default'" :config="listConfig" @onButClick="onAddClick" ref="listRef">
            <!-- 新增按钮插槽 -->
            <template #header>
                <div class="add-button-container">
                    <div class="add-button" @click="onAddClick">
                        <img src="/img/add.svg" alt="新增" class="add-icon" />
                        <span>新增菜品</span>
                    </div>
                </div>
            </template>
<!-- 
            <template #default="{ item, index }">
                <div class="dish-card" @click.stop="onCardClick(item)">
                    <div class="card-image">
                        <van-image
                            width="72"
                            height="72"
                            radius="6"
                            :src="item.image"
                            fit="cover"
                            :show-loading="false"
                        />
                    </div>
                    <div class="card-content">
                        <div class="card-header">
                            <h3 class="dish-title">{{ item.title }}</h3>
                            <van-tag
                                plain 
                                type="primary"
                                class="dish-tag"
                                >
                                {{ item.tag }}
                            </van-tag>
                        </div>
                        <div class="dish-desc">{{ item.description }}</div>
                        <div class="dish-price">{{ item.price }}</div>
                    </div>
                </div>
            </template> -->
        </yhc-list>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter();

// 骨架屏配置
const skeletonConfig = reactive({
    isShow: false,
    count: 3,
    row: 2,
    rowWidth: ['100%', '60%', '80%'],
    avatar: true,
    avatarSize: '40px',
    avatarShape: 'round',
    title: true,
    titleWidth: '50%',
    duration: 500
})
// 列表组件引用
const listRef = ref(null)
// 列表配置
const listConfig = reactive({
    curl: {
        ls: '/dishes/get_list', // 留空，使用模拟数据
        sort:'/dishes/post_sort'
    },
    details:'/dishConfigDetail', // 当sort为true时，需要填写
    sort:true,// 是否排序 默认为不填
    title:'菜品配置',
    postData: {
        orderby:'sort',
        dininghall_id:localStorage.getItem('dininghall')
    },
    search: {
        isShow: true,
        isShowPopup: true
    },
    tabs: {
        isShow: false
    },
    button: {
        isShow: false,
    },
    skeleton: skeletonConfig,
    // 模拟数据格式化
    format: (data) => {
        // 这里可以对数据进行格式化处理
        console.log('格式化数据:', data)
    },
    // 添加模拟数据标识
    mockData: true
})


// 新增按钮点击事件
const onAddClick = () => {
    router.push('/dishConfigAdd')
}
const { proxy } = getCurrentInstance();
const setRight = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title:'菜品配置',
  });
};
setRight()
// const onCardClick = (item) => {
//     router.push({ path: "/dishSortDetail", query: { id: item.id } });
// };
</script>

<style lang="scss" scoped>
.skeleton-demo-container {
    min-height: 100vh;
    background: #f7f8fa;
}

.add-button-container {
    padding: 16px;
    padding-bottom: 0;

    .add-button {
        display: flex;
        align-items: center;
        padding: 16px;
        background: #fff;
        border-radius: 8px;

        .add-icon {
            width: 24px;
            height: 24px;
            margin-right: 8px;
        }

        span {
            font-size: 16px;
            font-weight: normal;
            line-height: 22px;
            color: #323233;
        }
    }
}

// 菜品卡片样式
.dish-card {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    margin: 12px 16px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.98);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    }

    .card-image {
        margin-right: 12px;
        flex-shrink: 0;

        .van-image {
            border-radius: 8px;
            overflow: hidden;
        }
    }

    .card-content {
        flex: 1;
        min-width: 0;

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;

            .dish-title {
                font-size: 16px;
                font-weight: 600;
                line-height: 22px;
                color: #323233;
                margin: 0;
                flex: 1;
                min-width: 0;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-right: 8px;
            }

            .dish-tag {
                flex-shrink: 0;
                background: #1989fa;
                // color: #fff;
                border: none;
                font-size: 12px;
                padding: 2px 8px;
                // border-radius: 12px;
            }
        }

        .dish-desc {
            font-size: 12px;
            font-weight: 400;
            line-height: 18px;
            color: #969799;
            margin-bottom: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .dish-price {
            font-size: 16px;
            font-weight: 600;
            line-height: 22px;
            color: #ff6b35;
        }
    }
}
</style>
