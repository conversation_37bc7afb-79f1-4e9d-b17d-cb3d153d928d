<template>
  <div class="logo-config-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h3>Logo配置</h3>
      <p>设置企业Logo的显示尺寸和相关参数</p>
    </div>

    <!-- 配置表单 -->
    <div class="config-form">
      <!-- Logo预览 -->
      <div class="preview-section">
        <div class="section-title">Logo预览</div>
        <div class="logo-preview" :style="logoPreviewStyle">
          <img v-if="form.logoUrl" :src="form.logoUrl" alt="Logo预览" />
          <div v-else class="placeholder">
            <van-icon name="photo-o" size="40" />
            <p>暂无Logo</p>
          </div>
        </div>
      </div>

      <!-- Logo上传 -->
      <div class="form-section">
        <div class="section-title">Logo图片</div>
        <van-uploader
          v-model="fileList"
          :max-count="1"
          :preview-size="80"
          @after-read="onLogoUpload"
          @delete="onLogoDelete"
        >
          <van-button icon="plus" type="primary" size="small">上传Logo</van-button>
        </van-uploader>
      </div>

      <!-- 尺寸配置 -->
      <div class="form-section">
        <div class="section-title">尺寸设置</div>
        <van-cell-group inset>
          <van-field
            v-model="form.width"
            label="宽度"
            placeholder="请输入宽度"
            type="number"
            right-icon="question-o"
            @click-right-icon="showSizeHelp"
          >
            <template #right-icon>
              <span class="unit">px</span>
            </template>
          </van-field>
          <van-field
            v-model="form.height"
            label="高度"
            placeholder="请输入高度"
            type="number"
          >
            <template #right-icon>
              <span class="unit">px</span>
            </template>
          </van-field>
          <van-field
            v-model="form.maxWidth"
            label="最大宽度"
            placeholder="请输入最大宽度"
            type="number"
          >
            <template #right-icon>
              <span class="unit">px</span>
            </template>
          </van-field>
        </van-cell-group>
      </div>

      <!-- 显示位置 -->
      <div class="form-section">
        <div class="section-title">显示位置</div>
        <van-cell-group inset>
          <van-field
            v-model="form.position"
            label="位置"
            placeholder="选择显示位置"
            readonly
            is-link
            @click="showPositionPicker = true"
          />
        </van-cell-group>
      </div>

      <!-- 其他设置 -->
      <div class="form-section">
        <div class="section-title">其他设置</div>
        <van-cell-group inset>
          <van-cell title="保持宽高比" center>
            <template #right-icon>
              <van-switch v-model="form.keepAspectRatio" />
            </template>
          </van-cell>
          <van-cell title="启用点击效果" center>
            <template #right-icon>
              <van-switch v-model="form.clickable" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <van-button type="primary" block @click="saveLogo" :loading="saving">
        保存配置
      </van-button>
      <van-button block @click="resetForm" style="margin-top: 12px;">
        重置
      </van-button>
    </div>

    <!-- 位置选择器 -->
    <van-popup v-model:show="showPositionPicker" position="bottom">
      <van-picker
        :columns="positionOptions"
        @confirm="onPositionConfirm"
        @cancel="showPositionPicker = false"
      />
    </van-popup>

    <!-- 帮助弹窗 -->
    <van-dialog
      v-model:show="showHelp"
      title="尺寸设置说明"
      message="建议Logo宽度不超过200px，高度不超过80px，以确保在各种设备上的显示效果"
      show-cancel-button
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, getCurrentInstance, onMounted } from 'vue'
import { showToast, showSuccessToast } from 'vant'

const router = useRouter()
const { proxy } = getCurrentInstance()

// 响应式数据
const fileList = ref([])
const showPositionPicker = ref(false)
const showHelp = ref(false)
const saving = ref(false)

// 表单数据
const form = reactive({
  logoUrl: '',
  width: '',
  height: '',
  maxWidth: '200',
  position: '顶部居中',
  keepAspectRatio: true,
  clickable: false
})

// 位置选项
const positionOptions = [
  { text: '顶部居左', value: 'top-left' },
  { text: '顶部居中', value: 'top-center' },
  { text: '顶部居右', value: 'top-right' },
  { text: '中间居左', value: 'middle-left' },
  { text: '中间居中', value: 'middle-center' },
  { text: '中间居右', value: 'middle-right' }
]

// Logo预览样式
const logoPreviewStyle = computed(() => ({
  width: form.width ? `${form.width}px` : '120px',
  height: form.height ? `${form.height}px` : '60px',
  maxWidth: form.maxWidth ? `${form.maxWidth}px` : '200px'
}))

// Logo上传处理
const onLogoUpload = (file) => {
  // 这里应该调用上传接口
  console.log('上传Logo:', file)
  form.logoUrl = file.content || file.file
  fileList.value = [file]
  showToast('Logo上传成功')
}

// Logo删除处理
const onLogoDelete = () => {
  form.logoUrl = ''
  showToast('Logo已删除')
}

// 位置选择确认
const onPositionConfirm = ({ selectedOptions }) => {
  form.position = selectedOptions[0].text
  showPositionPicker.value = false
}

// 显示尺寸帮助
const showSizeHelp = () => {
  showHelp.value = true
}

// 保存Logo配置
const saveLogo = async () => {
  if (!form.logoUrl) {
    showToast('请先上传Logo图片')
    return
  }

  saving.value = true
  try {
    // 这里应该调用保存接口
    await new Promise(resolve => setTimeout(resolve, 1000))
    showSuccessToast('Logo配置保存成功')
    router.back()
  } catch (error) {
    showToast('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    logoUrl: '',
    width: '',
    height: '',
    maxWidth: '200',
    position: '顶部居中',
    keepAspectRatio: true,
    clickable: false
  })
  fileList.value = []
  showToast('表单已重置')
}

// 设置页面标题
const setPageTitle = () => {
  if (proxy && proxy.$_dd) {
    proxy.$_dd.biz.navigation.setTitle({
      title: 'Logo配置',
    })
  }
}

// 加载已有配置
const loadConfig = async () => {
  try {
    // 这里应该调用获取配置的接口
    // const config = await getLogoConfig()
    // Object.assign(form, config)
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

onMounted(() => {
  setPageTitle()
  loadConfig()
})
</script>

<style lang="scss" scoped>
.logo-config-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 80px;
}

.page-header {
  background: white;
  padding: 20px 16px;
  border-bottom: 1px solid #ebedf0;

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #323233;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #969799;
  }
}

.config-form {
  padding: 16px;
}

.form-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 12px;
    padding-left: 4px;
  }
}

.preview-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 12px;
    padding-left: 4px;
  }

  .logo-preview {
    background: white;
    border: 2px dashed #dcdee0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    min-height: 80px;
    transition: all 0.3s ease;

    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }

    .placeholder {
      text-align: center;
      color: #c8c9cc;

      .van-icon {
        margin-bottom: 8px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}

.unit {
  color: #969799;
  font-size: 14px;
  margin-left: 4px;
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #ebedf0;
}

:deep(.van-uploader__upload) {
  margin: 0;
}

:deep(.van-cell-group--inset) {
  margin: 0;
}
</style>
