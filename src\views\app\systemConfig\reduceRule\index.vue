<template>
  <div class="skeleton-demo-container">
    <!-- 列表组件演示 -->
    <yhc-list :key="listConfig.key || 'default'" :config="listConfig" @onButClick="onAddClick" ref="listRef">
      <!-- 新增按钮插槽 -->
      <template #header>
        <div class="add-button-container">
          <div class="add-button" @click="onAddClick">
            <img src="/img/add.svg" alt="新增" class="add-icon" />
            <span>新增减免规则</span>
          </div>
        </div>
      </template>
      <template #default="{ item, index }">
        <div class="demo-item" @click.stop="onCardClick(item)">
          <div class="item-content">
            <div class="item-header">
              <div class="item-title">{{ item.rule_name }}</div>
              <!-- <div class="item-expire" v-if="item.isExpired">过期</div> -->
            </div>
            <div class="item-desc">减免方式：{{ item.discount_type==0?'固定金额':'账单单百分比' }}</div>
            <div class="item-desc">减免上限：{{ item.limit_type==0?'无上限':'有上限' }}</div>
            <div class="item-desc">关联消费规则：{{ item.time }}</div>
            <!-- 显示其他标签 -->
            <!-- <div class="item-tags" v-if="item.otherTags && item.otherTags.length > 0">
              <span class="tag-item" v-for="tag in item.otherTags" :key="tag">{{ tag }}</span>
            </div> -->
          </div>
        </div>
      </template>
    </yhc-list>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
const router = useRouter();

// 骨架屏配置
const skeletonConfig = reactive({
  isShow: true,
  count: 3,
  row: 2,
  rowWidth: ['100%', '60%', '80%'],
  avatar: false,
  avatarSize: '40px',
  avatarShape: 'round',
  title: true,
  titleWidth: '50%',
  duration: 500
})
// 列表组件引用
const listRef = ref(null)
// 列表配置
const listConfig = reactive({
  curl: {
    ls: '/discount_rule/get_ls' // 留空，使用模拟数据
  },
  postData: {},
  search: {
    isShow: true,
    isShowPopup: false,
    key: 'rule_name'
  },
  tabs: {
    isShow: false
  },
  button: {
    isShow: false,
  },
  skeleton: skeletonConfig,
  // 模拟数据格式化
  format: (data) => {
    // 这里可以对数据进行格式化处理
    console.log('格式化数据:', data)
  },
  // 添加模拟数据标识
  mockData: false
})


// 新增按钮点击事件
const onAddClick = () => {
  router.push('/systemConfig/reduceRuleAdd')
}
const onCardClick = (item) => {
  router.push({ path: "/systemConfig/reduceRuleDetail", query: { id: item.id } });
};
const { proxy } = getCurrentInstance();
const setRight = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title:'减免规则',
  });
};
setRight()
</script>

<style lang="scss" scoped>
.skeleton-demo-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.add-button-container {
  padding: 16px;
  padding-bottom: 0;
  .add-button {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    .add-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: normal;
      line-height: 22px;
      color: #323233;
    }
  }
}

.demo-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin:16px;
  background: #fff;
  border-radius: 8px;
  .item-content {
    flex: 1;
    min-width: 0; /* 确保flex子项能够收缩 */

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items:center;
      margin-bottom: 8px;
    }

    .item-title {
      font-size: 17px;
      font-weight: 500;
      line-height: 23px;
      letter-spacing: normal;
      color: #171A1D;
      flex: 1;
    }

    .item-expire {
      background: #ED6A0C;
      color: #FFFFFF;
      font-size: 12px;
      line-height: 16px;
      padding: 0.5px 5px;
      border-radius: 22px;
    }

    .item-desc {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #9E9E9E;
      margin-bottom: 8px;
    }

    .item-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .tag-item {
        // background: #E8F4FD;
        color: #1989FA;
        font-size: 12px;
        line-height: 16px;
        padding: 2.5px 5px;
        border-radius: 2px;
        display: inline-block;
        border: 0.5px solid #1989FA;
      }
    }

    .item-time {
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #9E9E9E;
    }
  }

  .item-action {
    margin-left: 12px;
  }
}
</style>
