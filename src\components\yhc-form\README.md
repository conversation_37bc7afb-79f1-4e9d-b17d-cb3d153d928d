# yhc-form 组件按钮配置说明

## 概述

yhc-form 组件现在支持根据页面类型（add/detail）和是否有id参数来动态显示不同的按钮组合。

## Props 配置

### pageType
- **类型**: String
- **默认值**: 'add'
- **可选值**: 'add' | 'detail'
- **说明**: 页面类型，用于确定按钮显示逻辑

### editRedirectConfig
- **类型**: Object
- **默认值**: {}
- **说明**: 修改按钮跳转配置（仅在detail页面使用）
- **结构**:
  ```javascript
  {
    path: '/targetPage',  // 跳转路径
    query: {              // 额外的查询参数
      // 自定义参数
    }
  }
  ```

## 按钮显示规则

### add页面
- **有id参数时**: 显示单个"修改"按钮（编辑场景）
- **无id参数时**: 显示单个"新增"按钮（新增场景）

### detail页面
- **必须有id参数**: 显示"删除"和"修改"两个按钮
- **无id参数**: 不显示按钮

## 使用示例

### add页面使用示例

```vue
<template>
  <div>
    <!-- 新增页面 -->
    <yhc-form
      :config="addConfig"
      pageType="add"
    />
  </div>
</template>

<script setup>
const addConfig = {
  curl: {
    add: "/api/add",
    edit: "/api/edit",
    info: "/api/info"
  },
  postData: {}, // 无id，显示"新增"按钮
  form: [
    // 表单配置...
  ]
};
</script>
```

### detail页面使用示例

```vue
<template>
  <div>
    <!-- 详情页面 -->
    <yhc-form
      :config="detailConfig"
      pageType="detail"
      :editRedirectConfig="editConfig"
    />
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';

const route = useRoute();

const detailConfig = {
  curl: {
    info: "/api/info",
    del: "/api/delete"
  },
  postData: {
    id: route.query.id // 有id，显示"删除"和"修改"按钮
  },
  form: [
    // 表单配置...
  ]
};

// 修改按钮跳转配置
const editConfig = {
  path: '/editPage',
  query: {
    // 可以添加额外的查询参数
    from: 'detail'
  }
};
</script>
```

### 编辑页面使用示例

```vue
<template>
  <div>
    <!-- 编辑页面 -->
    <yhc-form
      :config="editConfig"
      pageType="add"
    />
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';

const route = useRoute();

const editConfig = {
  curl: {
    edit: "/api/edit",
    info: "/api/info"
  },
  postData: {
    id: route.query.id // 有id，显示"修改"按钮
  },
  form: [
    // 表单配置...
  ]
};
</script>
```

## 注意事项

1. **detail页面必须有id**: detail页面类型必须配合有效的id参数使用，否则不会显示任何按钮
2. **修改按钮跳转配置**: 在detail页面使用时，必须提供 `editRedirectConfig` 配置修改按钮的跳转目标
3. **向后兼容**: 现有的使用方式仍然有效，默认为add页面类型
4. **按钮样式**: 删除和修改按钮使用flex布局，各占50%宽度

## 技术实现

组件内部通过计算属性 `buttonConfig` 来动态决定按钮的显示逻辑：

- `showSingleButton`: 是否显示单个按钮
- `showDoubleButtons`: 是否显示双按钮
- `singleButtonText`: 单个按钮的文本内容

修改按钮点击时会调用 `onEdit` 方法，根据 `editRedirectConfig` 进行页面跳转。
