<template>
  <div class="report-result">
    <div class="res-top">
      <van-icon size="50px" color="#fff" name="checked" />
      <div class="text">报餐结果反馈</div>
    </div>
    <div :class="`res-list ${isAll ? 'all' : ''}`">
      <div class="title">部门报餐明细</div>
      <div class="title-info"><span>人员</span><span>结果</span></div>
      <div class="list-item" v-for="(item, i) in res" :key="i">
        <span class="name" >{{ item.name }}</span
        ><span class="res" :style="`color:${!item.code? '#07c160' : 'rgb(255, 151, 106)'}`">{{ item.msg }}</span>
      </div>
    </div>
    <div class="expand">
      <van-icon
        class="expand-icon"
        :name="isAll ? 'arrow-up' : 'arrow-down'"
      /><span @click="isAll = !isAll">{{ isAll ? "收起" : "展开" }}</span>
    </div>
  </div>
  <div class="bottom">
    <van-button type="primary" @click="router.go(-2)">返回首页</van-button>
  </div>
</template>
<script setup>
import { ref } from "vue";
const route = useRoute();
const router = useRouter();
let isAll = ref(false);
let res = JSON.parse(route.query.res) || [];
</script>
<style lang="scss" scoped>
.report-result {
    border-bottom: 1px solid transparent;
  .res-top {
    background: #007fff;
    text-align: center;
    padding: 80px 0;
    border-radius: 0 0 20px 20px;
    .text {
      font-size: 13px;
      color: #fff;
      font-weight: 300;
      margin-top: 20px;
    }
  }
  .res-list {
    background: #fff;
    margin: -40px 16px 0 16px;
    border-radius: 8px 8px 0 0;
    padding: 16px;
    max-height: 400px;
    overflow: hidden;
    .title {
      margin-bottom: 6px;
    }
    .title-info {
      font-size: 13px;
      color: rgba(0, 0, 0, 0.7);
    }
    .title-info,
    .list-item {
      display: flex;
      align-items: center;
      height: 50px;
      border-bottom: 1px solid #f6f6f6;
      span {
        flex: 1;
      }
    }
  }
  .all {
    max-height: none;
    height: auto !important;
  }
  .expand {
    background: #fff;
    text-align: center;
    border-radius: 0 0 8px 8px;
    margin: 0 16px;
    padding: 16px;
    margin-bottom: 150px;

    span {
      margin-left: 4px;
      color: #007fff;
    }
    .expand-icon {
      border-radius: 50%;
      background: #007fff;
      color: #fff;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      font-size: 12px;
      font-weight: 600;
    }
  }
}
.bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 12px 16px;
  text-align: right;
}
</style>
