<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form :config="basicFormConfig" pageType="add" @onSubmit="onBasicSubmit" />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    dininghall_id: localStorage.getItem('dininghall'),
  },
  curl: {
    add: '/dishes/post_add', // 新增接口
    edit: '/dishes/post_modify', // 编辑接口
    info: '/dishes/get_info' // 获取详情接口（编辑时需要）
  },
  groupForm: [
    [0, 1],
    [1, 5]
  ],
  form: [
    {
      label: "菜品名称",
      key: "title",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请填写菜品名称" }],
    },
    {
      label: "菜品分类",
      key: "category",
      component: "yhc-picker",
      placeholder: "请选择",
      required: true,
      rules: [{ required: true, message: "请选择菜品分类" }],
      // ellipsis: 2,
      opts: {
        url: "/dishes_category/get_all",
        postData: {
          dininghall_id: localStorage.getItem('dininghall'),
        },
        merge: true,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        // 判断多少个字符变成...
        // ellipsis: 2,
        keyMap: {
          category_id: "id",
          category_title: "title"
        },
        defaultList: []
      },
      card:{
        title:"title"
      }
    },
    {
      label: "菜品价格",
      key: "price",
      component: "yhc-input",
      type: "number",
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请填写菜品价格" }],
    },
    {
      label: "菜品图片",
      key: "image",
      component: "yhc-select-image",
      "max-count": 1,
    },
    {
      label: "备注",
      key: "desc",
      component: "yhc-input",
      type: "textarea",
      placeholder: "请输入",
      autosize: true,
      "show-word-limit": true,
      maxlength: 200,
      // desc: "支持多行文本输入，带字数限制"
    },
  ]
}
const { proxy } = getCurrentInstance();
const setRight = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: route.query.id ? '修改配置' : '新增配置',
  });
};
setRight()
// // 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
