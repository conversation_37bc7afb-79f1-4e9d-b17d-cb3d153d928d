<template>
    <div class="home-container">
        <!-- yhc-form 组件展示 -->
        <yhc-form :config="basicFormConfig" pageType="add" @onSubmit="onBasicSubmit" />
    </div>
</template>

<script setup>
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const route = useRoute()

// 基础表单配置
const basicFormConfig = {
    button: {
        isShow: true,
    },
    postData: {
        // 如果有id参数，则为编辑模式
        ...(route.query.id ? { id: route.query.id } : {})
    },
    curl: {
        add: '/attendance_rule/post_add', // 新增接口
        edit: '/attendance_rule/post_modify', // 编辑接口
        info: '/attendance_rule/get_info' // 获取详情接口（编辑时需要）
    },
    groupForm: [
        [0,1],
        [1,2],
    ],
    form: [
        {
            label: "考勤规则",
            key: "rule_name",
            component: "yhc-input",
            type: "text",
            placeholder: "请输入",
            required: true,
            // 不允许输入
            rules: [{ required: true, message: "请填写考勤规则名称" }],
        },
        {
            label: "查询方式",
            key: "query_type",
            component: "yhc-radio-group",
            options: [
                { value: "0", label: "按时间" },
                { value: "1", label: "按班次" }
            ],
            shape: "dot",
            required: true,
            child: {
                map: {
                    "0": [
                        {
                            label: "开始时间",
                            type: "time",
                            // format: 'HH:mm:ss',
                            key: "start_time",
                            component: "yhc-picker-date",
                            required: true,
                            rules: [{ required: true, message: "请填写开始时间" }],
                        },
                        {
                            label: "结束时间",
                            type: "time",
                            // format: 'HH:mm:ss',
                            key: "end_time",
                            component: "yhc-picker-date",
                            required: true,
                            rules: [{ required: true, message: "请填写结束时间" }],
                        },
                        {
                            label: "查询前置",
                            type: "number",
                            key: "pre_days",
                            "right-icon": "/天",
                            component: "yhc-input",
                            required: true,
                            default:0,
                            rules: [{ required: true, message: "请填写前置日期" }],
                        },
                    ],
                    "1": [{
                        label: "班次",
                        key: "shifts",
                        component: "yhc-picker",
                        required: true,
                        rules: [{ required: true, message: "请选择班次" }],
                        opts: {
                            url: "",
                            postData: {},
                            merge: false,
                            multiple: true,
                            text_key: "name",
                            contrast_key: "id",
                            keyMap: {},
                            defaultList: [],
                        },
                        card: {
                            // num: "id",
                            // price: "2.00",
                            // desc: "desc",
                            title: "name",
                            // thumb: "https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg",
                        }
                    },
                                            {
                            // 默认为8小时，以当前时间为基准向前回溯指定小时数
                            label: "查询时长",
                            key: "query_hours",
                            type: "number",
                            default: 8,
                            required: true,
                            // placeholder:"请填写查询时长/h",
                            "right-icon": "/h",
                            component: "yhc-input",
                            rules: [{ required: false, message: "请填写查询时长/h" }],
                        },
                    ],
                },
                form: [],
            },
        },
    ]
}
const { proxy } = getCurrentInstance();

const setRightA = () => {
    proxy.$_dd.biz.navigation.setTitle({
        title: route.query.id ? '修改规则' : '新增规则',
    });
};
setRightA()
// // 表单提交处理函数
const onBasicSubmit = (data) => {
    console.log('基础表单提交:', data)
    showToast('基础表单提交成功')
}
</script>

<style lang="scss" scoped>
.home-container {
    padding: 0;
    min-height: 100vh;
    // background: #f7f8fa;
}
</style>
