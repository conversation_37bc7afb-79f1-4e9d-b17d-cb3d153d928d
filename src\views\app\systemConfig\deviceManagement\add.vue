<template>
  <div class="device-detail-container">
    <div class="device-info">
      <!-- 设备类型选择 -->
      <van-cell-group inset>
        <van-cell title="设备类型" is-link @click="showDeviceTypePicker = !isQR">
          <template #value>
            <!-- <span :class="{ 'placeholder-text': !selectedDeviceType }">
              {{ selectedDeviceType }}
              {{selectedDeviceType ? deviceTypeOptions.find(item => item.value === selectedDeviceType)?.text :
              '请选择设备类型' }}
            </span> -->
            <span :class="{ 'placeholder-text': true }" v-if="selectedDeviceType==0">收银机</span>
            <span :class="{ 'placeholder-text': true }" v-else-if="selectedDeviceType==1">消费机</span>
            <span :class="{ 'placeholder-text': false }" v-else>请选择设备类型</span>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 设备名称 -->
      <van-cell-group inset class="device-name-card">
        <van-field v-model="formData.title" label="设备名称" placeholder="请输入设备名称"
          :rules="[{ required: true, message: '请输入设备名称' }]" class="device-name-field" :readonly="isQR" />
      </van-cell-group>

      <!-- 基本信息 -->
      <van-cell-group inset>
        <!-- <van-field v-model="formData.model" label="型号" placeholder="请输入型号" /> -->
        <van-field v-model="formData.deviceSn" label="SN" placeholder="请输入设备SN" :readonly="isQR" />
      </van-cell-group>

      <!-- 网络状态 -->
      <!-- <van-cell-group inset>
        <van-cell title="网络状态" is-link @click="showNetworkPicker = true">
          <template #value>
            <span class="network-status-text" :class="{
              'status-offline': formData.networkStatus === 'offline',
              'status-online': formData.networkStatus !== 'offline'
            }">
              {{ getNetworkStatusText(formData.networkStatus) }}
            </span>
          </template>
        </van-cell>
      </van-cell-group> -->

      <!-- 欢迎屏幕及功能 -->
      <!-- <van-cell-group v-if="selectedDeviceType == 0" inset>
        <van-cell>
          <template #title>
            欢迎屏幕
          </template>
          <template #value>
            <van-switch v-model="formData.functions.welcomeScreen" />
          </template>
        </van-cell>
        <template v-if="formData.functions.welcomeScreen">
          <van-cell title="刷脸">
            <template #value>
              <van-switch v-model="formData.functions.face" />
            </template>
          </van-cell>
          <van-cell title="刷卡">
            <template #value>
              <van-switch v-model="formData.functions.card" />
            </template>
          </van-cell>
          <van-cell title="输码">
            <template #value>
              <van-switch v-model="formData.functions.code" />
            </template>
          </van-cell>
          <van-cell title="扫码">
            <template #value>
              <van-switch v-model="formData.functions.scan" />
            </template>
          </van-cell>
        </template>
      </van-cell-group> -->

      <!-- 收银机功能 -->
      <!-- <van-cell-group v-if="selectedDeviceType == 1" inset>
        <van-cell title="刷脸">
          <template #value>
            <van-switch v-model="formData.functions.face" />
          </template>
        </van-cell>
        <van-cell title="刷卡">
          <template #value>
            <van-switch v-model="formData.functions.card" />
          </template>
        </van-cell>
        <van-cell title="输码">
          <template #value>
            <van-switch v-model="formData.functions.code" />
          </template>
        </van-cell>
        <van-cell title="扫码">
          <template #value>
            <van-switch v-model="formData.functions.scan" />
          </template>
        </van-cell>
      </van-cell-group> -->

      <!-- 开门延时 -->
      <!-- <van-cell-group v-if="selectedDeviceType == 0" inset>
        <van-cell>
          <template #title>
            开门延时(秒)
          </template>
          <template #value>
            <div class="door-delay-row">
              <van-button size="mini" class="door-delay-btn"
                @click="formData.functions.doorDelay = Math.max(1, formData.functions.doorDelay - 1)"
                :disabled="formData.functions.doorDelay <= 1">-</van-button>
              <span class="door-delay-value">{{ formData.functions.doorDelay }}</span>
              <van-button size="mini" class="door-delay-btn"
                @click="formData.functions.doorDelay = Math.min(20, formData.functions.doorDelay + 1)"
                :disabled="formData.functions.doorDelay >= 20">+</van-button>
            </div>
          </template>
        </van-cell>
      </van-cell-group> -->

      <!-- 设备音量 -->
      <!-- <span class="device-volume-label">0</span> -->
      <!-- <div class="device-volume-card device-volume-card-margin">
        <div class="device-volume-title" style="margin-bottom: 8px;">设备音量</div>
        <div class="device-volume-slider-row" style="margin-top: 5px;">
          <van-slider v-model="formData.functions.volume" :min="0" :max="100" :step="1" bar-height="4px"
            active-color="#1989fa" inactive-color="#ebedf0" class="device-volume-slider custom-volume-slider">
            <template #button>
              <div class="custom-slider-dot-small">{{ formData.functions.volume }}</div>
            </template>
          </van-slider>
        </div>
      </div> -->
      <!-- <span class="device-volume-label">100</span> -->

      <!-- 系统信息（新增页可省略或只做占位） -->
      <!-- <van-cell-group inset>
        <van-cell title="系统信息">
          <template #value>
            <span class="sysinfo-upgrade-btn">升级</span>
          </template>
        </van-cell>
      </van-cell-group> -->
    </div>

    <!-- 提交按钮 -->
    <div class="bottom-buttons">
      <van-button type="primary" block :loading="loading" @click="onSubmit">
        保存
      </van-button>
    </div>

    <!-- 设备类型选择器 -->
    <van-popup v-model:show="showDeviceTypePicker" position="bottom" round>
      <van-picker :columns="deviceTypeOptions" @confirm="onDeviceTypeConfirm" @cancel="showDeviceTypePicker = false" />
    </van-popup>

    <!-- 网络状态选择器 -->
    <van-popup v-model:show="showNetworkPicker" position="bottom" round>
      <van-picker :columns="networkOptions" @confirm="onNetworkConfirm" @cancel="showNetworkPicker = false" />
    </van-popup>
    <!-- ...existing code for image upload, etc... -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted,getCurrentInstance } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
const { proxy } = getCurrentInstance();
const router = useRouter()
const route = useRoute()

// 加载状态
const loading = ref(false)

// 弹窗控制
const showDeviceTypePicker = ref(false)
const showNetworkPicker = ref(false)
const showImageUpload = ref(false)
const showVolumeSelector = ref(false)
const selectedDeviceType = ref(1)
const uploadedImages = ref([])

// 设备类型选项
const deviceTypeOptions = [
  { text: '收银机', value: 0 },
  { text: '消费机', value: 1 }
]

// 网络状态选项
const networkOptions = [
  { text: '有线', value: 'wired' },
  { text: '无线', value: 'wireless' },
  { text: '离线', value: 'offline' }
]

// 音量选项
const volumeOptions = [0, 20, 40, 60, 80, 100]

// 表单数据
const formData = reactive({
  title: '',
  serialNumber: '',
  model: '',
  deviceSn: '',
  image: '',
  ip: '',
  mac: '',
  appVersion: '',
  networkStatus: 'wired',
  functions: {
    welcomeScreen: false,
    lockWelcomeScreen: false,
    pickupCode: false,
    accessControl: false,
    doorDelay: 5,
    scanCode: false,
    volume: 50
  },
  // 收银机特有字段
  config: {
    pickupCode: false,
    volume: 50,
    logo: ''
  }
})

// 获取网络状态文本
const getNetworkStatusText = (status) => {
  const option = networkOptions.find(opt => opt.value === status)
  return option ? option.text : '未知'
}

// 设备类型确认
const onDeviceTypeConfirm = ({ selectedValues }) => {
  selectedDeviceType.value = selectedValues[0]
  showDeviceTypePicker.value = false
}

// 网络状态选择确认
const onNetworkConfirm = ({ selectedOptions }) => {
  formData.networkStatus = selectedOptions[0].value
  showNetworkPicker.value = false
}

// 选择设备图片
const selectDeviceImage = () => {
  uploadedImages.value = []
  showImageUpload.value = true
}

// 图片上传处理
const onImageUpload = (file) => {
  console.log('上传图片:', file)
}

// 确认图片上传
const confirmImageUpload = () => {
  if (uploadedImages.value.length > 0) {
    formData.image = uploadedImages.value[0].content
    showToast('设备图片已上传')
    showImageUpload.value = false
  } else {
    showToast('请先上传图片')
  }
}

// 选择音量
const selectVolume = (volume) => {
  formData.functions.volume = volume
}

// 获取音量图标 - 使用简洁美观的图标
const getVolumeIcon = (volume) => {
  if (volume === 0) return 'volume-o'      // 静音 0%
  if (volume === 20) return 'music-o'      // 低音量 20%
  if (volume === 40) return 'music'        // 低音量 40%
  if (volume === 60) return 'sound-o'      // 中音量 60%
  if (volume === 80) return 'sound'        // 中高音量 80%
  if (volume === 100) return 'volume'      // 最大音量 100%
  return 'volume' // 默认
}

// 确认音量选择
const confirmVolumeSelection = () => {
  showVolumeSelector.value = false
  showToast(`音量已设置为 ${formData.functions.volume}%`)
}

// 表单提交
const onSubmit = async () => {
  // 基本验证
  if (!selectedDeviceType.value) {
    showToast('请选择设备类型')
    return
  }

  if (!formData.title.trim()) {
    showToast('请输入设备名称')
    return
  }

  if (!formData.deviceSn.trim()) {
    showToast('请输入设备SN')
    return
  }

  loading.value = true

  try {
    // 构建提交数据
    const submitData = {
      type: selectedDeviceType.value,
      title: formData.title,
      deviceSn: formData.deviceSn,
      // ip: formData.ip,
      // mac: formData.mac,
      // status: 'offline' // 新增设备默认离线
    }

    // 根据设备类型添加特有字段
    if (selectedDeviceType.value === 0) {
      submitData.serialNumber = formData.serialNumber
      submitData.model = formData.model
      submitData.appVersion = formData.appVersion
      submitData.network = { ...formData.network }
      submitData.announcement = { ...formData.announcement }
      submitData.total = 0
    } else if (selectedDeviceType.value === 1) {
      submitData.serialNumber = formData.serialNumber
      submitData.model = formData.model
      submitData.config = { ...formData.config }
    }

    console.log('提交设备数据:', submitData)

    // 这里应该调用API接口保存数据
    // await api.addDevice(submitData)

    // 模拟API调用
    // await new Promise(resolve => setTimeout(resolve, 1000))
    proxy.$post('/device/post_add_device', submitData).then((res) => {
      console.log('添加设备响应:', res)
      // if (res.code !== 200) {
      //   showToast(res.msg || '添加设备失败，请稍后重试')
      //   return
      // }
    }).catch(error => {
      showToast(error.msg || '添加设备失败，请稍后重试')
      return
    })
    showToast('设备添加成功')

    // 返回列表页面
    router.go(-1)

  } catch (error) {
    console.error('添加设备失败:', error)
    showToast('添加设备失败')
  } finally {
    loading.value = false
  }
}
let isQR = ref(false)
// 页面初始化
onMounted(() => {
  // 如果从列表页传递了设备类型，自动选择
  if (route.query.type) {
    selectedDeviceType.value =  parseInt(route.query.type)
    console.log('自动选择设备类型:',typeof selectedDeviceType.value)
  }
  if(route.query.data){
    let data=JSON.parse(route.query.data)
    formData.title = data.title
    formData.deviceSn = data.deviceSn
    isQR.value=true
    console.log('二维码数据:',formData)
  }
})
</script>

<style lang="scss" scoped>
.device-detail-container {
  min-height: 90vh;
  background: #f7f8fa;
  padding-bottom: 100px;

  .device-name-card {
    margin-bottom: 16px;
  }

  .door-delay-row {
    display: flex;
    align-items: center;
    gap: 8px;

    .door-delay-btn {
      padding: 0 10px;
    }

    .door-delay-value {
      font-size: 16px;
      color: #323233;
    }
  }

  .device-volume-card {
    background: #fff;
    border-radius: 12px;
    padding: 18px 18px 18px 18px;
    margin: 0 16px 16px 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .device-volume-card-margin {
    margin-left: 16px;
    margin-right: 16px;
    margin-top: 16px;
    margin-bottom: 0;
  }

  .bottom-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: white;
    border-top: 1px solid #ebedf0;
    z-index: 100;
    display: flex;
    gap: 12px;
  }
}

// 自定义 Vant 组件样式
:deep(.van-cell-group) {
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  .van-cell-group__title {
    padding: 12px 16px;
    background: #f8f9fa;
    font-weight: 600;
    color: #646566;
    border-bottom: 1px solid #ebedf0;
  }
}

// 统一字段标签宽度
:deep(.van-field) {
  .van-field__label {
    flex: none;
    min-width: 80px;
    text-align: left;
    white-space: nowrap; // 防止文字换行
  }

  .van-field__control {
    text-align: right;
  }
}

:deep(.van-cell) {
  height: 56px;
  align-items: center;

  // 统一标签宽度
  .van-cell__title {
    flex: none;
    min-width: 30%;
    text-align: left;
    font-size: 16px;
    // line-height: 38px;
    white-space: nowrap; // 防止文字换行
  }

  // 开关对齐
  .van-cell__value {
    display: flex;
    align-items: center;
    width: 60%;
    font-size: 15px;
    justify-content: flex-end;
    white-space: nowrap; // 防止文字换行
  }

  .van-switch {
    margin: 0;
  }
}

:deep(.van-switch--on) {
  background-color: #1989fa;
}

:deep(.van-popup) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.van-picker) {
  .van-picker__toolbar {
    background: #f8f9fa;
  }
}

// 子选项样式
.sub-option {
  :deep(.van-cell__title) {
    padding-left: 20px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 12px;
      height: 1px;
      background: #dcdee0;
    }
  }
}

// 音量控制样式
.volume-control-inline {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;

  .van-slider {
    width: 120px;
  }

  .volume-text {
    font-size: 14px;
    color: #323233;
    min-width: 35px;
    text-align: right;
    white-space: nowrap; // 防止文字换行
  }
}

// 单位文本样式
.unit-text {
  color: #969799;
  font-size: 14px;
  margin-left: 8px;
  white-space: nowrap; // 防止文字换行
}

// 音量显示样式
.volume-display {
  color: #1989fa;
  font-weight: 600;
  font-size: 14px;
}

// 音量选择器样式
.volume-selector {
  padding: 20px;

  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #323233;
    }

    .van-icon {
      font-size: 20px;
      color: #969799;
      cursor: pointer;
    }
  }

  .volume-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;

    .volume-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px 12px;
      border: 2px solid #ebedf0;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      &.active {
        border-color: #1989fa;
        background: #f0f8ff;

        .volume-icon {
          color: #1989fa;
        }

        .volume-label {
          color: #1989fa;
          font-weight: 600;
        }
      }

      .volume-icon {
        font-size: 24px;
        color: #646566;
        margin-bottom: 8px;
      }

      .volume-label {
        font-size: 14px;
        color: #323233;
      }
    }
  }

  .volume-actions {
    margin-top: 16px;
  }
}

// 图片上传弹窗样式
.upload-popup {
  height: 100%;
  display: flex;
  flex-direction: column;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #ebedf0;
    background: white;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #323233;
      white-space: nowrap; // 防止文字换行
    }

    .van-icon {
      font-size: 20px;
      color: #969799;
      cursor: pointer;
    }
  }

  .popup-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }

  .popup-actions {
    margin-top: 24px;
  }
}

// 图片样式
:deep(.van-image) {
  border-radius: 8px;
}

// 上传组件样式
:deep(.van-uploader) {
  .van-uploader__upload {
    border: 2px dashed #dcdee0;
    border-radius: 8px;

    &:hover {
      border-color: #1989fa;
    }
  }
}

.custom-volume-slider {
  :deep(.van-slider__button) {
    width: 28px !important;
    height: 20px !important;
    background: transparent;
    top: -8px !important;
    left: -14px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none;
  }
}

.custom-slider-dot-small {
  min-width: 28px;
  height: 20px;
  background: #1989fa;
  color: #fff;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(25, 137, 250, 0.10);
  position: relative;
  top: 0;
  left: 0;
  z-index: 2;
  user-select: none;
  transition: background 0.2s;
  border: none;
  padding: 0 6px;
}
</style>
