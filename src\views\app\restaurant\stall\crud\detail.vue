<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="detail"
      :editRedirectConfig="editRedirectConfig"
      @onSubmit="onBasicSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const { proxy } = getCurrentInstance(); 
const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    id: route.query.id // 从路由获取id参数
  },
  curl: {
    info: '/stall/get_info', // 获取详情接口
    del: '/stall/post_del' // 删除接口
  },
  groupForm: [
    [0, 1],
    [1, 4]
  ],
  form: [
    {
      label: "档口名称",
      key: "title",
      component: "yhc-input",
      type: "text",
      disabled: true,
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请填写用户名" }],
    },
    {
      label: "消费机",
      key: "device_consumptions",
      component: "yhc-picker",
      placeholder: "请选择",
      disabled: true,
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false
      },
      card: {
        title: "title",
        desc: "id"
      },
      opts: {
        url: "",
        postData: {},
        merge: false,
        multiple: true,
        text_key: "title",
        contrast_key: "id",
        keyMap: {},
        defaultList: [
          { id: 1, title: "技术部" },
          { id: 2, title: "市场部" },
          { id: 3, title: "人事部" },
          { id: 4, title: "财务部" },
          { id: 5, title: "技术部" }
        ]
      },
    },
    {
      label: "收银机",
      key: "device_cashiers",
      component: "yhc-picker",
      disabled: true,
      placeholder: "请选择",
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false
      },
      card: {
        title: "title",
      },
      opts: {
        url: "",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: {},
        defaultList: [
          { id: 1, title: "技术部" },
          { id: 2, title: "市场部" },
          { id: 3, title: "人事部" },
          { id: 4, title: "财务部" }
        ]
      },
    },
  ]
}

// 修改按钮跳转配置
const editRedirectConfig = {
  path: '/stallAdd', // 跳转到新增页面进行编辑
  query: {
    id: route.query.id, // 传递id参数
    from: 'detail' // 标识来源
  }
}

// 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: '档口详情',
  });
};
setRightA()
setRight()
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
