<template>
  <div class="wrapper" :class="{ 'required-field': config.required }">
    <van-field :name="config.key" :label="config.label" :required="config.required" :rules="config.rules"
      :disabled="config.disabled" :label-width="config.labelWidth" :input-align="config.inputAlign"
      :border="config.border">
      <template #input>
        <van-radio-group v-model="props.form[config.key]" :direction="config.direction" :disabled="config.disabled"
          @change="onChange">
          <van-radio v-for="option in options" :key="option.value" :name="option.value"
            :disabled="option.disabled || config.disabled">
            {{ option.label }}
          </van-radio>
        </van-radio-group>
      </template>
    </van-field>

    <!-- 子表单容器 -->
    <div class="child-container">
      <!-- 子表单项 - 使用 map 配置时显示 -->
      <template v-if="config.child && config.child.map">
        <transition name="child-fade">
          <div class="child-wrapper"
            v-if="config.child.form && Array.isArray(config.child.form) && config.child.form.length > 0">
            <div class="child-item" v-for="(childItem, i) in config.child.form" :key="childItem.key + i">
              <yhc-form-item v-if="childItem && childItem.key" :key="childItem.key" :config="childItem"
                :form="props.form" />
            </div>
          </div>
        </transition>
      </template>

      <!-- 子表单项 - 当选中值与 showMode 相等时显示 -->
      <template v-if="config.child && config.child.form && config.child.showMode !== undefined && !config.child.map">
        <transition name="child-fade">
          <div class="child-wrapper" v-if="currentValue === config.child.showMode">
            <div class="child-item" v-for="(childItem, i) in config.child.form" :key="childItem.key + i">
              <yhc-form-item :key="childItem.key" :config="childItem" :form="props.form" />
            </div>
          </div>
        </transition>
      </template>

      <!-- 子表单项 - 当选中值与 showMode 不相等时显示 -->
      <template
        v-if="config.child && config.child.formChild && config.child.showMode !== undefined && !config.child.map">
        <transition name="child-fade">
          <div class="child-wrapper" v-if="currentValue !== config.child.showMode">
            <div class="child-item" v-for="(childItem, i) in config.child.formChild" :key="childItem.key + i">
              <yhc-form-item :key="childItem.key" :config="childItem" :form="props.form" />
            </div>
          </div>
        </transition>
      </template>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, watch, computed, nextTick, shallowReactive } from "vue";
import { deepAssign } from "@/untils";
import { showToast } from "vant";

const { proxy } = getCurrentInstance();

// 默认配置
let config = {
  // 基础配置
  label: "单选框组",       // 字段标签 (字符串) - 显示在单选框组上方的标签文字
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"gender", "type"
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用所有选项, false: 可正常选择
  rules: [],               // 验证规则 (数组) - 表单验证规则配置
  defaultValue: '',        // 默认值 (字符串/数字) - 默认选中的选项值

  // 样式配置
  border: false,           // 是否显示边框 (布尔值) - true: 显示边框, false: 无边框
  labelWidth: '',          // 标签宽度 (字符串) - 标签区域的宽度，如"80px", "100px"
  inputAlign: "right",     // 输入内容对齐方式 (字符串) - "left": 左对齐, "center": 居中, "right": 右对齐
  direction: "vertical",   // 排列方向 (字符串) - "horizontal": 水平排列, "vertical": 垂直排列
  shape: "circle",         // 单选框形状 (字符串) - "circle": 圆形, "square": 方形

  // 选项配置
  options: [],             // 选项数组 (数组) - 单选框选项，格式: [{ value: "1", label: "选项1", disabled: false }]

  // 子表单配置 (高级功能)
  child: {
    map: false,            // 是否使用映射模式 (布尔值) - true: 根据选中值动态显示子表单
    showMode: undefined,   // 显示模式值 (字符串/数字) - 当选中此值时显示子表单
    form: [],              // 子表单字段 (数组) - 当选中特定值时显示的表单字段
    formChild: [],         // 备用子表单字段 (数组) - 当选中非showMode值时显示的表单字段
  }
};

const props = defineProps({
  config: Object,
  form: Object,
});

// 合并配置
props.config && deepAssign(config, props.config);

// 初始化表单值和默认值处理
if (props.form[config.key] === undefined || props.form[config.key] === '') {
  props.form[config.key] = '';
}

// 处理默认值 - 确保默认值被选中（修复：支持0值作为默认值）
if (config.defaultValue !== undefined && config.defaultValue !== null && (props.form[config.key] === '' || props.form[config.key] === undefined)) {
  props.form[config.key] = config.defaultValue;
}

// 定义事件
const emit = defineEmits(["change"]);

// 处理选项数据
const options = computed(() => {
  // 如果options是字符串数组，转换为对象数组
  if (config.options && Array.isArray(config.options)) {
    return config.options.map(option => {
      if (typeof option === 'string' || typeof option === 'number') {
        return {
          label: option.toString(),
          value: option,
          disabled: false
        };
      }
      return option;
    });
  }
  return config.options || [];
});

// 当前选中值
const currentValue = computed(() => {
  return props.form[config.key];
});

// 初始化子表单配置
if (config.child && config.child.map) {
  // 确保 child.form 存在且是响应式数组
  if (!config.child.form || !Array.isArray(config.child.form)) {
    config.child.form = shallowReactive([]);
  } else {
    config.child.form = shallowReactive(config.child.form);
  }
}

// 更新子表单函数
const updateChildForm = (value) => {
  // 处理 map 配置的子表单
  if (config.child && config.child.map && value !== undefined) {
    if (config.child.form && Array.isArray(config.child.form)) {
      config.child.form.splice(0);
      if (config.child.map[value]) {
        config.child.form.push(...config.child.map[value]);
      }
    }
  }
};

// 单选框组变化事件
const onChange = (value) => {
  // 处理子表单的动态显示
  updateChildForm(value);

  // 触发变化事件
  emit("change", {
    component: "yhc-radio-group",
    key: config.key,
    value: value,
  });
};

// 监听表单值变化
watch(() => props.form[config.key], (newValue) => {
  // 处理子表单的动态显示
  updateChildForm(newValue);

  // 强制更新视图
  if (config.child && (config.child.map || config.child.showMode !== undefined) && newValue !== undefined) {
    nextTick(() => {
      proxy.$forceUpdate();
    });
  }
}, { immediate: true });
</script>

<style lang="scss" scoped>
.wrapper {
  &.required-field {
    .van-field {
      --van-field-label-margin-right: 20px;

      :deep(.van-field__label) {
        margin-left: -8px !important;
      }
    }

    .van-field__label {
      margin-left: -8px !important;
    }
  }

  :deep(.van-radio-group--horizontal) {
    display: flex;

    flex-wrap: wrap;

    .van-radio {
      // margin-right: 16px;
      font-size: 14px;
      // margin-bottom: 8px;
    }
  }

  :deep(.van-radio-group--vertical) {
    display: flex;
    gap: 8px;

    .van-radio {
      // margin-bottom: 8px;
    }
  }

  :deep(.van-radio__label) {
    // color: #323233;
    // margin-left: 8px;
  }

  :deep(.van-radio__icon--checked) {
    color: var(--van-primary-color);
  }
}

// 子表单容器样式
.child-container {
  .child-wrapper {
    // margin-top: 16px;

    .child-item {
      // margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// // 子表单过渡动画
// .child-fade-enter-active,
// .child-fade-leave-active {
//   transition: all 0.3s ease;
// }

// .child-fade-enter-from {
//   opacity: 0;
//   transform: translateY(-10px);
// }

// .child-fade-leave-to {
//   opacity: 0;
//   transform: translateY(-10px);
// }</style>
