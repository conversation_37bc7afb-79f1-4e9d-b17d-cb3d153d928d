<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="add"
      @onSubmit="onBasicSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const { proxy } = getCurrentInstance();
const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    // 如果有id参数，则为编辑模式
    ...(route.query.id ? { id: route.query.id } : {}),
    dininghall_id:localStorage.getItem('dininghall')
  },
  curl: {
    add: '/mealtime/post_add', // 新增接口
    edit: '/mealtime/post_modify', // 编辑接口
    info: '/mealtime/get_info' // 获取详情接口（编辑时需要）
  },
  groupForm: [
    [0, 1],
    [1, 3],
    [3, 4]
  ],
  form: [
    {
      label: "餐时名称",
      key: "title",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请输入餐时名称" }],
    },
    {
      label: "开始时间",
      key: "start_time",
      component: "yhc-picker-date",
      required: true,
      typeLabel: "start",
      rules: [{ required: true, message: "请选择开始时间" }],
      type: "time",
      crossDayConfig: {
        startTimeKey: "start_time",
        endTimeKey: "end_time"
      },
      // 必填            
      // default: true, // 默认值
      // type: "time-short", //time-short 时分 date：日期 datetime：日期时间 time：时分秒
    },
    {
      label: "结束时间",
      key: "end_time",
      component: "yhc-picker-date",
      required: true,
      typeLabel: "end",
      rules: [{ required: true, message: "请选择结束时间" }],
      type: "time",
      crossDayConfig: {
        startTimeKey: "start_time",
        endTimeKey: "end_time"
      },
      // 必填            
      // default: true, // 默认值
      // type: "time-short", //time-short 时分 date：日期 datetime：日期时间 time：时分秒
    },
    {
        label:"当结束时间早于开始时间时，系统将视为跨天处理",
        component:"yhc-desc"
    }
  ]
}


// // 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: route.query.id ? '修改餐时' : '新增餐时',
  });
};
setRightA()
setRight()
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
