<template>
  <div class="example-container">
    <van-nav-bar title="yhc-desc 组件示例" left-arrow @click-left="$router.go(-1)" />
    
    <div class="example-content">
      <!-- 基础用法 -->
      <div class="example-section">
        <h3>基础用法</h3>
        <yhc-desc :config="basicConfig" />
      </div>
      
      <!-- 类似您图片中的样式 -->
      <div class="example-section">
        <h3>钉钉管理说明（类似您的图片）</h3>
        <yhc-desc :config="dingTalkConfig" />
      </div>
      
      <!-- 不同类型的提示 -->
      <div class="example-section">
        <h3>不同类型的提示</h3>
        <yhc-desc :config="infoConfig" />
        <yhc-desc :config="warningConfig" />
        <yhc-desc :config="errorConfig" />
        <yhc-desc :config="successConfig" />
      </div>
      
      <!-- 带图标和标题 -->
      <div class="example-section">
        <h3>带图标和标题</h3>
        <yhc-desc :config="iconTitleConfig" />
      </div>
      
      <!-- 可点击的提示 -->
      <div class="example-section">
        <h3>可点击的提示</h3>
        <yhc-desc :config="clickableConfig" />
      </div>
      
      <!-- 带链接的提示 -->
      <div class="example-section">
        <h3>带链接的提示</h3>
        <yhc-desc :config="linkConfig" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { showToast } from 'vant'

// 基础用法
const basicConfig = {
  label: "这是一个基础的提示文字组件，用于显示说明信息。"
}

// 类似您图片中的钉钉管理说明
const dingTalkConfig = {
  label: "说明：前往钉钉管理后台(https://oa.dingtalk.com)->工作台->应用管理->OA审批->员工->一般审批->五一请假->回到页面设计"
}

// 信息提示
const infoConfig = {
  label: "这是一个信息提示，用于显示一般性的提示信息。",
  type: "info",
  icon: "info-o"
}

// 警告提示
const warningConfig = {
  label: "这是一个警告提示，请注意相关操作可能带来的风险。",
  type: "warning",
  icon: "warning-o"
}

// 错误提示
const errorConfig = {
  label: "这是一个错误提示，操作失败或出现异常情况。",
  type: "error", 
  icon: "cross"
}

// 成功提示
const successConfig = {
  label: "这是一个成功提示，操作已成功完成。",
  type: "success",
  icon: "success"
}

// 带图标和标题
const iconTitleConfig = {
  title: "重要提醒",
  label: "请仔细阅读以下操作说明，确保按照正确的步骤进行操作。",
  type: "info",
  icon: "bell"
}

// 可点击的提示
const clickableConfig = {
  label: "点击此处查看详细的操作指南和帮助文档。",
  clickable: true,
  showArrow: true,
  onClick: () => {
    showToast('提示被点击了！')
  }
}

// 带链接的提示
const linkConfig = {
  label: "如果您在使用过程中遇到任何问题，可以联系我们的技术支持团队。",
  linkText: "点击获取帮助",
  onLinkClick: () => {
    showToast('链接被点击了！')
    // 这里可以跳转到帮助页面或打开外部链接
    // window.open('https://help.example.com')
  }
}
</script>

<style lang="scss" scoped>
.example-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.example-content {
  padding: 16px;
}

.example-section {
  margin-bottom: 24px;
  
  h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 500;
    color: #323233;
  }
  
  .yhc-desc {
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
