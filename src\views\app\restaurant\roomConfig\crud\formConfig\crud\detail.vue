<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="detail"
      :editRedirectConfig="editRedirectConfig"
      @onSubmit="onBasicSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    id: route.query.id // 从路由获取id参数
  },
  curl: {
    info: '/form_field/get_info', // 获取详情接口
    del: '/form_field/post_del' // 删除接口
  },
  groupForm: [
    [0, 1],
    [1, 4]
  ],
  groupForm: [
      [0, 2],
      [2, 3],
      [3, 4],
      [4, 5],
    ],
    form: [
      {
            label: "标题",
            key: "title",
            component: "yhc-input",
            required: true,
            rules: [{ required: true, message: "请填写标题" }],
          },
          {
            label: "必填",
            key: "required",
            component: "yhc-switch",
          },
          {
            label: "默认值",
            key: "default",
            component: "yhc-input",
          },
    ]
}

// 修改按钮跳转配置
const editRedirectConfig = {
  path: '/roomConfig/formConfig/add', // 跳转到新增页面进行编辑
  query: {
    id: route.query.id, // 传递id参数
    from: 'detail' // 标识来源
  }
}

// 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
