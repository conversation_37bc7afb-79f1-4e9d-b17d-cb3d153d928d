import { createApp } from "vue";
import router from "@/router";
import App from "./App.vue";
import { axiosPlugins } from "@/untils/methods";
import { dingPlugins } from "@/untils/dingtalk";
import store from "@/store";
import "vant/lib/index.css";
import { components } from "@/untils/components";
import VueAMap, { initAMapApiLoader } from "@vuemap/vue-amap";
import "@vuemap/vue-amap/dist/style.css";

const app = createApp(App);
app.use(router);
app.use(dingPlugins);
app.use(store);
app.use(axiosPlugins);
app.use(components);
// 1. 先初始化API加载器
initAMapApiLoader({
  key: "dd7b7e94a28a14ea8f13b83d71f9c81b", // 请替换为您的高德地图 key
  version: "2.0",
  securityJsCode: 'e5944c153662eb9392d0628ef3ea8721',
  plugins: [
    "AMap.ToolBar",
    "AMap.Scale",
    "AMap.HawkEye",
    "AMap.MapType",
    "AMap.Geolocation",
  ],
});
// 配置高德地图
app.use(VueAMap);

app.mount("#app");
