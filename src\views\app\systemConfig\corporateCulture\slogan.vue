<template>
  <div class="slogan-config-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h3>Slogan配置</h3>
      <p>设置企业标语的字数限制和显示样式</p>
    </div>

    <!-- 配置表单 -->
    <div class="config-form">
      <!-- Slogan预览 -->
      <div class="preview-section">
        <div class="section-title">Slogan预览</div>
        <div class="slogan-preview" :style="sloganPreviewStyle">
          <p v-if="form.content" :class="{ 'text-overflow': isTextOverflow }">
            {{ form.content }}
          </p>
          <p v-else class="placeholder">请输入Slogan内容</p>
          <div class="char-count" :class="{ 'over-limit': isOverLimit }">
            {{ form.content.length }}/{{ form.maxLength }}
          </div>
        </div>
      </div>

      <!-- Slogan内容 -->
      <div class="form-section">
        <div class="section-title">Slogan内容</div>
        <van-cell-group inset>
          <van-field
            v-model="form.content"
            type="textarea"
            placeholder="请输入企业Slogan"
            rows="3"
            autosize
            :maxlength="form.maxLength"
            show-word-limit
            @input="onContentChange"
          />
        </van-cell-group>
      </div>

      <!-- 字数限制 -->
      <div class="form-section">
        <div class="section-title">字数设置</div>
        <van-cell-group inset>
          <van-field
            v-model="form.maxLength"
            label="最大字数"
            placeholder="请输入最大字数"
            type="number"
            @input="onMaxLengthChange"
          />
          <van-cell title="当前字数" :value="`${form.content.length}字`" />
          <van-cell 
            title="超出提醒" 
            :value="isOverLimit ? '已超出' : '正常'"
            :class="{ 'over-limit-cell': isOverLimit }"
          />
        </van-cell-group>
      </div>

      <!-- 显示样式 -->
      <div class="form-section">
        <div class="section-title">显示样式</div>
        <van-cell-group inset>
          <van-field
            v-model="form.fontSize"
            label="字体大小"
            placeholder="请输入字体大小"
            type="number"
          >
            <template #right-icon>
              <span class="unit">px</span>
            </template>
          </van-field>
          <van-field
            v-model="form.color"
            label="字体颜色"
            placeholder="选择字体颜色"
            readonly
            is-link
            @click="showColorPicker = true"
          >
            <template #right-icon>
              <div class="color-preview" :style="{ backgroundColor: form.color }"></div>
            </template>
          </van-field>
          <van-field
            v-model="form.textAlign"
            label="对齐方式"
            placeholder="选择对齐方式"
            readonly
            is-link
            @click="showAlignPicker = true"
          />
        </van-cell-group>
      </div>

      <!-- 高级设置 -->
      <div class="form-section">
        <div class="section-title">高级设置</div>
        <van-cell-group inset>
          <van-cell title="启用动画效果" center>
            <template #right-icon>
              <van-switch v-model="form.enableAnimation" />
            </template>
          </van-cell>
          <van-cell title="自动换行" center>
            <template #right-icon>
              <van-switch v-model="form.autoWrap" />
            </template>
          </van-cell>
          <van-cell title="超出省略" center>
            <template #right-icon>
              <van-switch v-model="form.ellipsis" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <van-button type="primary" block @click="saveSlogan" :loading="saving">
        保存配置
      </van-button>
      <van-button block @click="resetForm" style="margin-top: 12px;">
        重置
      </van-button>
    </div>

    <!-- 颜色选择器 -->
    <van-popup v-model:show="showColorPicker" position="bottom">
      <div class="color-picker-container">
        <div class="picker-header">选择字体颜色</div>
        <div class="color-options">
          <div 
            v-for="color in colorOptions" 
            :key="color"
            class="color-option"
            :class="{ active: form.color === color }"
            :style="{ backgroundColor: color }"
            @click="selectColor(color)"
          ></div>
        </div>
        <van-button type="primary" block @click="showColorPicker = false" style="margin: 16px;">
          确定
        </van-button>
      </div>
    </van-popup>

    <!-- 对齐方式选择器 -->
    <van-popup v-model:show="showAlignPicker" position="bottom">
      <van-picker
        :columns="alignOptions"
        @confirm="onAlignConfirm"
        @cancel="showAlignPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, getCurrentInstance, onMounted } from 'vue'
import { showToast, showSuccessToast } from 'vant'

const router = useRouter()
const { proxy } = getCurrentInstance()

// 响应式数据
const showColorPicker = ref(false)
const showAlignPicker = ref(false)
const saving = ref(false)

// 表单数据
const form = reactive({
  content: '',
  maxLength: 50,
  fontSize: 16,
  color: '#323233',
  textAlign: '居中',
  enableAnimation: false,
  autoWrap: true,
  ellipsis: true
})

// 颜色选项
const colorOptions = [
  '#323233', '#969799', '#1989fa', '#07c160', 
  '#ff976a', '#ee0a24', '#ffd21e', '#c8c9cc'
]

// 对齐方式选项
const alignOptions = [
  { text: '居左', value: 'left' },
  { text: '居中', value: 'center' },
  { text: '居右', value: 'right' }
]

// 计算属性
const isOverLimit = computed(() => form.content.length > form.maxLength)
const isTextOverflow = computed(() => form.ellipsis && isOverLimit.value)

const sloganPreviewStyle = computed(() => ({
  fontSize: `${form.fontSize}px`,
  color: form.color,
  textAlign: form.textAlign === '居左' ? 'left' : form.textAlign === '居右' ? 'right' : 'center'
}))

// 内容变化处理
const onContentChange = (value) => {
  if (value.length > form.maxLength && !form.autoWrap) {
    showToast(`内容不能超过${form.maxLength}个字符`)
  }
}

// 最大字数变化处理
const onMaxLengthChange = (value) => {
  const maxLength = parseInt(value) || 50
  if (maxLength < 1) {
    form.maxLength = 1
    showToast('最大字数不能小于1')
  } else if (maxLength > 200) {
    form.maxLength = 200
    showToast('最大字数不能超过200')
  }
}

// 选择颜色
const selectColor = (color) => {
  form.color = color
}

// 对齐方式确认
const onAlignConfirm = ({ selectedOptions }) => {
  form.textAlign = selectedOptions[0].text
  showAlignPicker.value = false
}

// 保存Slogan配置
const saveSlogan = async () => {
  if (!form.content.trim()) {
    showToast('请输入Slogan内容')
    return
  }

  if (isOverLimit.value && !form.autoWrap) {
    showToast('内容超出字数限制，请修改后保存')
    return
  }

  saving.value = true
  try {
    // 这里应该调用保存接口
    await new Promise(resolve => setTimeout(resolve, 1000))
    showSuccessToast('Slogan配置保存成功')
    router.back()
  } catch (error) {
    showToast('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    content: '',
    maxLength: 50,
    fontSize: 16,
    color: '#323233',
    textAlign: '居中',
    enableAnimation: false,
    autoWrap: true,
    ellipsis: true
  })
  showToast('表单已重置')
}

// 设置页面标题
const setPageTitle = () => {
  if (proxy && proxy.$_dd) {
    proxy.$_dd.biz.navigation.setTitle({
      title: 'Slogan配置',
    })
  }
}

// 加载已有配置
const loadConfig = async () => {
  try {
    // 这里应该调用获取配置的接口
    // const config = await getSloganConfig()
    // Object.assign(form, config)
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

onMounted(() => {
  setPageTitle()
  loadConfig()
})
</script>

<style lang="scss" scoped>
.slogan-config-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 80px;
}

.page-header {
  background: white;
  padding: 20px 16px;
  border-bottom: 1px solid #ebedf0;

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #323233;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #969799;
  }
}

.config-form {
  padding: 16px;
}

.form-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 12px;
    padding-left: 4px;
  }
}

.preview-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 12px;
    padding-left: 4px;
  }

  .slogan-preview {
    background: white;
    border: 1px solid #ebedf0;
    border-radius: 8px;
    padding: 20px;
    position: relative;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;

    p {
      margin: 0;
      line-height: 1.5;
      word-break: break-all;

      &.placeholder {
        color: #c8c9cc;
        font-style: italic;
      }

      &.text-overflow {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .char-count {
      position: absolute;
      bottom: 8px;
      right: 12px;
      font-size: 12px;
      color: #969799;

      &.over-limit {
        color: #ee0a24;
        font-weight: 600;
      }
    }
  }
}

.unit {
  color: #969799;
  font-size: 14px;
  margin-left: 4px;
}

.color-preview {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #ebedf0;
}

.over-limit-cell {
  :deep(.van-cell__value) {
    color: #ee0a24;
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #ebedf0;
}

.color-picker-container {
  background: white;
  
  .picker-header {
    padding: 16px;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #ebedf0;
  }

  .color-options {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    padding: 20px;

    .color-option {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      border: 2px solid transparent;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        border-color: #1989fa;
        transform: scale(1.1);
      }
    }
  }
}

:deep(.van-cell-group--inset) {
  margin: 0;
}
</style>
