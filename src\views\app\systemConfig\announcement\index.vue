<template>
  <div class="announcement-container">
    <!-- 搜索和筛选 -->
    <div class="search-section">
      <van-search v-model="searchValue" placeholder="请输入公告标题搜索" :clearable="true" @search="onSearch" />
      
      <!-- 状态筛选 -->
      <div class="filter-section">
        <van-tabs v-model:active="activeStatus" @click-tab="onStatusChange" line-width="16px">
          <van-tab title="全部" name="all"></van-tab>
          <van-tab title="已发布" name="published"></van-tab>
          <van-tab title="草稿" name="draft"></van-tab>
          <van-tab title="已下线" name="offline"></van-tab>
        </van-tabs>
      </div>
    </div>

    <!-- 新增按钮 -->
    <div class="add-button-section">
      <van-button type="primary" block @click="onAddClick" icon="plus">
        新增公告
      </van-button>
    </div>

    <!-- 骨架屏 -->
    <div v-if="showSkeleton" class="skeleton-container">
      <van-skeleton v-for="n in 3" :key="n" :row="3" :row-width="['100%', '80%', '60%']" 
        :loading="true" :avatar="false" :title="true" :title-width="'70%'" class="skeleton-item" />
    </div>

    <!-- 公告列表 -->
    <div v-else class="announcement-list">
      <div v-for="(item, index) in filteredData" :key="item.id" 
           class="announcement-item" @click="onItemClick(item)">
        
        <!-- 置顶标识 -->
        <div v-if="item.isTop" class="top-badge">
          <van-tag type="danger" size="mini">置顶</van-tag>
        </div>
        
        <div class="item-content">
          <!-- 标题和状态 -->
          <div class="item-header">
            <div class="item-title">{{ item.title }}</div>
            <van-tag :type="getStatusType(item.status)" size="mini">
              {{ getStatusText(item.status) }}
            </van-tag>
          </div>
          
          <!-- 内容预览 -->
          <div class="item-preview">{{ item.content }}</div>
          
          <!-- 附件信息 -->
          <div v-if="item.attachments && item.attachments.length > 0" class="item-attachment">
            <van-icon name="paperclip" size="12" />
            <span>{{ item.attachments.length }}个附件</span>
          </div>
          
          <!-- 时间和操作 -->
          <div class="item-footer">
            <div class="item-time">{{ item.createTime }}</div>
            <div class="item-actions" @click.stop>
              <!-- 置顶/取消置顶 -->
              <van-button 
                :type="item.isTop ? 'default' : 'primary'" 
                size="mini" 
                plain
                @click="onToggleTop(item)"
              >
                {{ item.isTop ? '取消置顶' : '置顶' }}
              </van-button>
              
              <!-- 编辑 -->
              <van-button type="primary" size="mini" plain @click="onEditClick(item)">
                编辑
              </van-button>
              
              <!-- 删除 -->
              <van-button type="danger" size="mini" plain @click="onDeleteClick(item)">
                删除
              </van-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <van-empty v-if="!showSkeleton && filteredData.length === 0" 
               description="暂无公告" 
               image="search" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'

const router = useRouter()

// 响应式数据
const searchValue = ref('')
const activeStatus = ref('all')
const showSkeleton = ref(true)

// 模拟公告数据
const announcements = ref([
  {
    id: 1,
    title: '系统维护通知',
    content: '为了提升系统性能，我们将于本周六凌晨2:00-6:00进行系统维护，期间系统将暂停服务，请提前做好相关准备...',
    status: 'published',
    isTop: true,
    createTime: '2025-07-15 10:30:00',
    attachments: [
      { name: '维护计划.pdf', size: '2.5MB' }
    ]
  },
  {
    id: 2,
    title: '新功能上线公告',
    content: '我们很高兴地宣布，新的移动端功能已经正式上线，包括扫码支付、余额查询等便民功能...',
    status: 'published',
    isTop: false,
    createTime: '2025-07-14 15:20:00',
    attachments: []
  },
  {
    id: 3,
    title: '用户协议更新说明',
    content: '根据最新的法律法规要求，我们对用户协议进行了更新，主要涉及隐私保护和数据安全方面...',
    status: 'draft',
    isTop: false,
    createTime: '2025-07-13 09:15:00',
    attachments: [
      { name: '用户协议v2.0.pdf', size: '1.8MB' },
      { name: '隐私政策.pdf', size: '1.2MB' }
    ]
  },
  {
    id: 4,
    title: '节假日服务安排',
    content: '即将到来的国庆假期，我们的客服时间将有所调整，具体安排如下...',
    status: 'offline',
    isTop: false,
    createTime: '2025-07-12 14:45:00',
    attachments: []
  }
])

// 过滤后的数据
const filteredData = computed(() => {
  let filtered = announcements.value

  // 状态筛选
  if (activeStatus.value !== 'all') {
    filtered = filtered.filter(item => item.status === activeStatus.value)
  }

  // 搜索筛选
  if (searchValue.value) {
    filtered = filtered.filter(item => 
      item.title.includes(searchValue.value) ||
      item.content.includes(searchValue.value)
    )
  }

  // 置顶排序
  return filtered.sort((a, b) => {
    if (a.isTop && !b.isTop) return -1
    if (!a.isTop && b.isTop) return 1
    return new Date(b.createTime) - new Date(a.createTime)
  })
})

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    published: 'success',
    draft: 'warning',
    offline: 'default'
  }
  return statusMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    published: '已发布',
    draft: '草稿',
    offline: '已下线'
  }
  return statusMap[status] || '未知'
}

// 事件处理
const onSearch = () => {
  console.log('搜索:', searchValue.value)
}

const onStatusChange = (tab) => {
  console.log('状态切换:', tab.name)
}

const onAddClick = () => {
  router.push('/systemConfig/announcement/add')
}

const onItemClick = (item) => {
  router.push({
    path: '/systemConfig/announcement/detail',
    query: { id: item.id }
  })
}

const onEditClick = (item) => {
  router.push({
    path: '/systemConfig/announcement/edit',
    query: { id: item.id }
  })
}

const onToggleTop = async (item) => {
  try {
    const action = item.isTop ? '取消置顶' : '置顶'
    await showConfirmDialog({
      title: '确认操作',
      message: `确定要${action}这条公告吗？`
    })
    
    item.isTop = !item.isTop
    showToast(`${action}成功`)
  } catch (error) {
    // 用户取消操作
  }
}

const onDeleteClick = async (item) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '删除后无法恢复，确定要删除这条公告吗？'
    })
    
    const index = announcements.value.findIndex(a => a.id === item.id)
    if (index > -1) {
      announcements.value.splice(index, 1)
      showToast('删除成功')
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 模拟加载
onMounted(() => {
  setTimeout(() => {
    showSkeleton.value = false
  }, 1500)
})
</script>

<style lang="scss" scoped>
.announcement-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.search-section {
  background: #fff;
  
  .filter-section {
    padding: 0 16px 16px;
  }
}

.add-button-section {
  padding: 16px;
  background: #f7f8fa;
}

// 骨架屏样式
.skeleton-container {
  padding: 16px;
  
  .skeleton-item {
    margin-bottom: 16px;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 公告列表样式
.announcement-list {
  padding: 0 16px 16px;
}

.announcement-item {
  position: relative;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .top-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    z-index: 1;
  }
  
  .item-content {
    padding: 16px;
    padding-right: 60px; // 为置顶标识留空间
  }
  
  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
    
    .item-title {
      font-size: 16px;
      font-weight: 500;
      color: #323233;
      line-height: 22px;
      flex: 1;
      margin-right: 8px;
    }
  }
  
  .item-preview {
    font-size: 14px;
    color: #646566;
    line-height: 20px;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .item-attachment {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #969799;
    margin-bottom: 8px;
    
    span {
      margin-left: 4px;
    }
  }
  
  .item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .item-time {
      font-size: 12px;
      color: #969799;
    }
    
    .item-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  // 点击效果
  &:active {
    background: #f2f3f5;
  }
}
</style>
