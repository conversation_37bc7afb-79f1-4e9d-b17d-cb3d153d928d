<template>
  <div class="wrapper" :class="{ 'required-field': config.required }">
    <van-field v-model="displayValue" :label="config.label" :name="config.key" :type="config.type"
      :label-align="config.align" :placeholder="config.placeholder" :required="config.required"
      :input-align="config.inputAalign" :border="config.border" :autosize="config.autosize"
      :show-word-limit="config['show-word-limit']" :maxlength="config.maxlength" :rules="config.rules"
      :disabled="config.disabled" :label-width="config.labelWidth" @update:model-value="handleInput" @blur="handleBlur"
      :min="config.min">
      <template #right-icon v-if="config['right-icon']"> {{ config['right-icon'] }} </template>
    </van-field>
  </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance, computed, watch } from "vue";
import { deepAssign } from "@/untils";
import { showToast } from "vant";
const { proxy } = getCurrentInstance();

let config = {
  // 基础配置
  label: "文本",           // 字段标签 (字符串) - 显示在输入框左侧的标签文字
  type: "text",            // 输入框类型 (字符串) - "text": 文本, "number": 数字, "password": 密码, "textarea": 多行文本
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"username", "email"
  placeholder: "请输入",    // 占位符 (字符串) - 输入框为空时显示的提示文字
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用输入, false: 可正常输入
  rules: [],               // 验证规则 (数组) - 表单验证规则，如: [{ required: true, message: '请填写用户名' }]

  // 样式配置
  align: "",               // 标签对齐方式 (字符串) - "left": 左对齐, "center": 居中, "right": 右对齐
  inputAalign: "left",     // 输入内容对齐方式 (字符串) - "left": 左对齐, "center": 居中, "right": 右对齐
  labelWidth: '',          // 标签宽度 (字符串) - 标签区域的宽度，如"80px", "100px"
  border: true,            // 是否显示边框 (布尔值) - true: 显示下边框, false: 无边框

  // 输入限制配置
  maxlength: undefined,    // 最大输入长度 (数字) - 限制输入字符数，undefined表示不限制
  min: 1,                  // 最小值 (数字) - 仅对number类型有效，限制输入的最小数值
  autosize: false,         // 是否自适应高度 (布尔值) - 仅对textarea类型有效，true: 自动调整高度
  "show-word-limit": false, // 是否显示字数统计 (布尔值) - true: 显示已输入/最大字符数, false: 不显示
  decimalPlaces: false,    // 是否启用2位小数位格式化 (布尔值) - 仅对number类型有效，true: 限制最多2位小数

  // 图标配置
  "right-icon": null,      // 右侧图标 (字符串) - 输入框右侧显示的图标名称或文字，null表示不显示
};

const props = defineProps({
  config: Object,
  form: Object,
});

props.config && deepAssign(config, props.config);

// 内部显示值，用于控制输入框显示
const displayValue = ref(props.form[config.key] || '');

// 监听表单数据变化，同步到显示值
watch(() => props.form[config.key], (newValue) => {
  displayValue.value = newValue || '';
}, { immediate: true });

const validateDecimalInput = (value) => {
  if (!value) return '';

  // 只允许数字和一个小数点，最多两位小数
  const regex = /^\d*\.?\d{0,2}$/;

  if (!regex.test(value)) {
    // 如果输入不符合格式，截取有效部分
    const validPart = value.match(/^\d*\.?\d{0,2}/)?.[0] || '';
    return validPart;
  }

  // 如果输入超过两位小数，进行截断处理
  if (value.includes('.')) {
    const parts = value.split('.');
    if (parts[1] && parts[1].length > 2) {
      return parts[0] + '.' + parts[1].substring(0, 2);
    }
  }

  return value;
};

const handleInput = (value) => {
  if (config.type === 'number' && config.decimalPlaces) {
    // 对数字类型且启用小数位格式化的输入进行验证和截断
    const validatedValue = validateDecimalInput(value);
    displayValue.value = validatedValue;
    props.form[config.key] = validatedValue;
  } else {
    // 普通输入处理
    displayValue.value = value;
    props.form[config.key] = value;
  }

  // 调用原有的update函数以保持兼容性
  update(value);
};

const handleBlur = () => {
  // 失焦时不进行格式化，保持用户输入的原始格式（在允许范围内）
  // 这里可以添加其他失焦时需要的逻辑
};

const update = (e) => {
  // 保持原有逻辑，可以在这里添加额外的处理
};
</script>
<style lang="scss" scoped>
.wrapper {
  .van-field {
    font-size: 16px;
    padding: 16px;
  }

  // 当required为true时，输入框padding往左调整-8px
  &.required-field {
    .van-field {
      --van-field-label-margin-right: 20px;

      :deep(.van-field__label) {
        margin-left: -8px !important;
      }
    }

  }
}
</style>
