<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form :config="basicFormConfig" pageType="add" @onSubmit="onBasicSubmit" />
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { showToast } from 'vant'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    // 如果有id参数，则为编辑模式
    ...(route.query.id ? { id: route.query.id } : {})
  },
  curl: {
    add: '/cost_center/add', // 新增接口
    edit: '/cost_center/edit', // 编辑接口
    info: '/cost_center/info' // 获取详情接口（编辑时需要）
  },
  groupForm: [
    [0, 1],
    [1, 3]
  ],
  form: [
    {
      label: "成本名称",
      key: "title",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入名称",
      required: true,
      rules: [{ required: true, message: "请输入成本名称" }],
    },
    {
      label: "内部员工",
      key: "userlst",
      component: "yhc-select-user",
      placeholder: "请选择人员",
      required: false,
    },
    {
      label: "外部人员",
      key: "outsiderlst",
      component: "yhc-select-user",
      placeholder: "请选择人员",
      required: false,
    }
  ]
}

// 设置页面标题
const setPageTitle = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: route.query.id ? '修改成本中心' : '新增成本中心',
  })
}
setPageTitle()

// 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('成本中心表单提交:', data)
  showToast('提交成功')
  // 提交成功后返回列表页
  setTimeout(() => {
    router.back()
  }, 1000)
}
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100%;
  background: #f7f8fa;
}
</style>
