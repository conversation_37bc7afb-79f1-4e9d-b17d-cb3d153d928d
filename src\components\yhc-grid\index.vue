<template>
  <div class="wraper-grid">
    <div class="title-block" v-if="props.data.title">
      <div class="icon-title"></div>
      <span>{{ props.data.title }}</span>
    </div>
    <van-grid :column-num="props.data.count" :border="false">
      <van-grid-item :class="`yhc-${props.data.ctype}${props.data.count}`" v-for="(item, i) in props.data.data" :key="i"
        :icon="item.icon" :text="props.data.ctype != 'grid' ? item.title : ''" @click="navClick(item)">
      </van-grid-item>
    </van-grid>
  </div>
</template>
<script setup>
import { watch } from "vue";
import { showToast } from "vant";
const router = useRouter();
// Props定义 - 网格布局组件配置
const props = defineProps({
  config: Object,          // 组件配置 (对象) - 预留的配置对象，暂未使用
  data: Object,            // 网格数据 (对象) - 网格的完整数据配置
});

// 网格数据结构说明:
// props.data = {
//   title: "功能菜单",      // 网格标题 (字符串) - 显示在网格上方的标题文字
//   count: 4,              // 列数 (数字) - 每行显示的网格项数量，如3, 4, 5
//   ctype: "grid",         // 网格类型 (字符串) - "grid": 纯图标网格, 其他类型显示文字
//   data: [                // 网格项数据 (数组) - 网格中的各个项目
//     {
//       icon: "home-o",    // 图标名称 (字符串) - vant图标名称
//       title: "首页",     // 项目标题 (字符串) - 网格项显示的文字
//       text: "首页",      // 项目文本 (字符串) - 备用文字字段
//       url: "/home",      // 跳转地址 (字符串) - 点击时的跳转路径
//       app_type: "native" // 应用类型 (字符串) - "native": 内部路由, "diy": 外部链接
//     }
//   ]
// }
const navClick = (item) => {
  if (!item.url) {
    showToast(`${item.text}未设置路径`);
    return;
  }
  if (item.app_type === "diy") {
    // url: "http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/huashan/2023-07-05/7L8UFnAIbEQvAXDPKXcU1b7RuhQjyjFB.pdf",
    window.location.href = item.url;
  } else {
    router.push(item.url);
  }
};
</script>
<style lang="scss">
.wraper-grid {
  padding: 16px 16px 0 16px;
  margin: 16px;
  border-radius: 8px;
  background: #fff;
  color: #171a1d;
  font-size: 12px;

  .icon-title {
    height: 17px;
    width: 4px;
    background-color: rgba(22, 120, 255, 1);
    border-radius: 2px;
    margin-right: 6px;
  }

  .title-block {
    color: rgba(51, 51, 51, 100);
    font-size: 15px;
    align-items: center;
    display: flex;
    margin-bottom: 8px;

    image {
      width: 17px;
      height: 17px;
    }
  }

  .yhc-grid1 {
    .van-grid-item__content {
      padding: 0;

      .van-icon {
        width: 100%;
        height: 100%;

        .van-icon__image {
          object-fit: cover;
          width: 100%;
          height: 164px;
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }

  .yhc-grid2 {
    .van-grid-item__content {
      padding: 5px;

      .van-icon {
        width: 100%;
        height: 100%;

        .van-icon__image {
          object-fit: cover;
          width: 100%;
          height: 82px;
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }

  .yhc-grid3 {
    .van-grid-item__content {
      padding: 5px;

      .van-icon {
        width: 100%;
        height: 100%;

        .van-icon__image {
          object-fit: cover;
          width: 100%;
          height: 53px;
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }

  .yhc-grid4 {
    .van-grid-item__content {
      padding: 5px;

      .van-icon {
        width: 100%;
        height: 100%;

        .van-icon__image {
          object-fit: cover;
          width: 100%;
          height: 42px;
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }

  .yhc-grid5 {
    .van-grid-item__content {
      padding: 5px;

      .van-icon {
        width: 100%;
        height: 100%;

        .van-icon__image {
          object-fit: cover;
          width: 100%;
          height: 32px;
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }

  .yhc-app_group3 {
    .van-grid-item__content {
      .van-icon {
        width: 100%;
        height: 100%;

        .van-icon__image {
          object-fit: cover;
          width: 66px;
          height: 66px;
        }
      }

      .van-grid-item__text {
        font-size: 13px;
      }
    }
  }

  .yhc-app_group4 {
    .van-grid-item__content {
      .van-icon {
        width: 100%;
        height: 100%;

        .van-icon__image {
          object-fit: cover;
          width: 54px;
          height: 54px;
        }
      }

      .van-grid-item__text {
        font-size: 12px;
      }
    }
  }

  .yhc-app_group5 {
    .van-grid-item__content {
      .van-icon {
        width: 100%;
        height: 100%;

        .van-icon__image {
          object-fit: cover;
          width: 46px;
          height: 46px;
        }
      }

      .van-grid-item__text {
        font-size: 12px;
      }
    }
  }
}
</style>
