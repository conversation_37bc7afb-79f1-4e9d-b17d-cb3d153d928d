<template>
  <div class="yp_qr_auth">
    <div class="icon">
      <van-image
        width="109"
        height="109"
        radius="4"
        src="http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/huashan/2023-09-02/CAmDgzhqlMxqwsBZTFtNMV3CxexFob5B.png"
      />
    </div>
    <span>{{ config.button.sub_text }}</span>
    <div style="padding: 40px 16px 0">
      <van-button block type="primary" @click="auth" :loading="loading">{{
        config.button.but_text
      }}</van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import { showToast } from "vant";
import { useLoginStore } from "@/store/dingLogin";

const app = useLoginStore();
const router = useRouter();
const route = useRoute();
let loading = ref(false);
let { proxy } = getCurrentInstance();
let config = {
  button: {
    // sub_text: "授权后大屏可以登录",
    but_text: "设备授权",
  },
};
localStorage.clear();
if (!app.token) {
  // console.log("跳转---->",app.token);
  router.push({ path: "/login", query: route.query });
}
let auth = () => {
  loading.value = true;
  proxy
    .$post("device/get_datav_qrcode_login", {
      userid: app.loginData.user_info.userid,
      ...route.query,
    })
    .then((res) => {
      if (!res.errcode) {
        showToast(res.voice || "授权成功");
        proxy.$_dd.biz.navigation.close({
          message: "quit message", //退出信息，传递给openModal或者openSlidePanel的onSuccess函数的result参数
          onSuccess: function (result) {
            /**/
          },
          onFail: function () {},
        });
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      showToast(err);
    })
    .finally(() => {
      setTimeout(() => {
        loading.value = false;
      }, 1500);
    });
};
</script>

<style scoped lang="scss">
.yp_qr_auth {
  text-align: center;

  .icon {
    margin-top: 80px;
  }

  span {
    font-size: 12px;
    color: #ccc;
    margin: 16px;
  }
}
</style>
