# yhc-booking-type 预定方式选择组件

基于项目 yhc 组件规范设计的预定方式选择组件，采用下拉选择器的交互形式，支持个人预定、预定多份、待他人预定三种模式，并根据选择动态显示相应的子组件。

## 功能特性

- ✅ 支持三种预定方式：个人预定、预定多份、待他人预定
- ✅ 默认选中"个人预定"
- ✅ 选择"待他人预定"时显示钉钉选人组件（yhc-select-user）
- ✅ 选择"预定多份"时显示份数输入框
- ✅ 支持表单验证和数据绑定
- ✅ 遵循项目 yhc- 组件规范
- ✅ 与 Vant4 组件库保持一致的样式风格
- ✅ 下拉选择器交互方式，节省页面空间
- ✅ 自动数据清理和初始化
- ✅ 参考 yhc-picker 组件的样式和交互规范

## 使用方式

### 1. 在表单中使用

```vue
<template>
  <yhc-form :config="formConfig" :form="formData" @onSubmit="handleSubmit" />
</template>

<script setup>
import { reactive } from 'vue';

const formData = reactive({
  booking_type: "个人预定",    // 预定方式
  quantity: null,             // 份数（预定多份时使用）
  selected_user: null,        // 选择的用户（待他人预定时使用）
});

const formConfig = {
  form: [
    {
      component: "yhc-booking-type",
      key: "booking_type",
      label: "预定方式",
      required: true,
      rules: [{ required: true, message: '请选择预定方式' }]
    }
  ]
};

const handleSubmit = (data) => {
  console.log('提交的数据:', data);
  // 根据预定方式获取相应数据
  if (data.booking_type === '预定多份') {
    console.log('预定份数:', data.quantity);
  } else if (data.booking_type === '待他人预定') {
    console.log('预定人员:', data.selected_user);
  }
};
</script>
```

### 2. 直接使用组件

```vue
<template>
  <yhc-booking-type 
    :config="bookingConfig" 
    :form="formData" 
    @change="onBookingTypeChange"
  />
</template>

<script setup>
import { reactive } from 'vue';

const formData = reactive({
  booking_type: "个人预定",
  quantity: null,
  selected_user: null,
});

const bookingConfig = {
  label: "预定方式",
  key: "booking_type",
  quantityKey: "quantity",
  userKey: "selected_user",
  required: true,
  rules: [{ required: true, message: '请选择预定方式' }]
};

const onBookingTypeChange = (data) => {
  console.log('预定方式变化:', data);
};
</script>
```

## 配置参数

### 基础配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| label | String | "预定方式" | 字段标签文本 |
| type | String | "text" | 字段类型 |
| key | String | "booking_type" | 预定方式字段名 |
| quantityKey | String | "quantity" | 份数字段名 |
| userKey | String | "selected_user" | 用户字段名 |
| placeholder | String | "请选择" | 占位符文本 |
| required | Boolean | false | 是否必填 |
| disabled | Boolean | false | 是否禁用 |
| border | Boolean | true | 是否显示边框 |
| labelWidth | String | "" | 标签宽度 |
| rules | Array | [] | 表单验证规则 |

### 弹窗配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| popup.round | Boolean | true | 是否圆角弹窗 |
| popup.position | String | "bottom" | 弹窗位置 |
| popup.style | Object | `{ height: "50vh", overflow: "hidden" }` | 弹窗样式 |
| popup.closeable | Boolean | false | 是否显示关闭按钮 |

### 选项配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| options | Array | 见下方 | 单选框选项配置 |
| defaultValue | String | "个人预定" | 默认选中值 |

默认选项配置：
```javascript
options: [
  { value: "个人预定", label: "个人预定", disabled: false },
  { value: "预定多份", label: "预定多份", disabled: false },
  { value: "待他人预定", label: "待他人预定", disabled: false }
]
```

### 选人组件配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| selectUserConfig | Object | 见下方 | 选人组件配置 |

默认选人组件配置：
```javascript
selectUserConfig: {
  label: "选择人员",
  placeholder: "请选择预定人员",
  multiple: false,
  required: true,
  rules: [{ required: true, message: "请选择预定人员" }]
}
```

### 份数输入配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| quantityConfig | Object | 见下方 | 份数输入配置 |

默认份数输入配置：
```javascript
quantityConfig: {
  min: 1,
  max: 99,
  defaultQuantity: 1,
  required: true,
  rules: [
    { required: true, message: "请输入份数" },
    { pattern: /^[1-9]\d*$/, message: "请输入有效的份数" }
  ]
}
```

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 预定方式或子组件值变化时触发 | `{ component, key, value, form, subComponent? }` |

## 数据结构

### 表单数据结构

```javascript
{
  booking_type: "个人预定",     // 预定方式：个人预定/预定多份/待他人预定
  quantity: 2,                 // 份数（仅在预定多份时有值）
  selected_user: {             // 选择的用户（仅在待他人预定时有值）
    departments: [],
    users: [
      {
        userid: "user123",
        name: "张三",
        avatar: "avatar_url"
      }
    ]
  }
}
```

## 使用示例

### 完整示例

```vue
<template>
  <div class="booking-demo">
    <yhc-booking-type 
      :config="config" 
      :form="form" 
      @change="handleChange"
    />
    
    <div class="result">
      <h3>当前数据：</h3>
      <pre>{{ JSON.stringify(form, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue';

const form = reactive({
  booking_type: "个人预定",
  quantity: null,
  selected_user: null
});

const config = {
  label: "预定方式",
  key: "booking_type",
  quantityKey: "quantity", 
  userKey: "selected_user",
  required: true,
  rules: [{ required: true, message: '请选择预定方式' }],
  
  // 自定义份数配置
  quantityConfig: {
    min: 1,
    max: 50,
    defaultQuantity: 2
  },
  
  // 自定义选人配置
  selectUserConfig: {
    label: "代预定人员",
    placeholder: "请选择代预定的人员",
    multiple: false
  }
};

const handleChange = (data) => {
  console.log('数据变化:', data);
};
</script>
```

## 如何获取选中的值

### 1. 通过表单数据直接获取

```javascript
// 在组件的 form 对象中直接获取
const formData = reactive({
  booking_type: "个人预定",    // 当前选中的预定方式
  quantity: null,             // 份数（仅在预定多份时有值）
  selected_user: null,        // 选择的用户（仅在待他人预定时有值）
});

// 获取当前预定方式
console.log('当前预定方式:', formData.booking_type);

// 根据预定方式获取相应数据
if (formData.booking_type === '预定多份') {
  console.log('预定份数:', formData.quantity);
} else if (formData.booking_type === '待他人预定') {
  console.log('选择的用户:', formData.selected_user);
}
```

### 2. 通过 change 事件监听

```javascript
const onBookingTypeChange = (data) => {
  console.log('组件类型:', data.component);
  console.log('字段名:', data.key);
  console.log('当前值:', data.value);
  console.log('完整表单数据:', data.form);

  // 判断是主组件还是子组件的变化
  if (data.subComponent) {
    console.log('子组件类型:', data.subComponent);
  }
};
```

### 3. 在表单提交时获取

```javascript
const onFormSubmit = (data) => {
  // 获取预定方式
  const bookingType = data.booking_type;

  // 根据预定方式处理不同的数据
  switch (bookingType) {
    case '个人预定':
      console.log('个人预定，无需额外数据');
      break;

    case '预定多份':
      const quantity = data.quantity;
      console.log(`预定多份，数量: ${quantity}份`);
      break;

    case '待他人预定':
      const selectedUser = data.selected_user;
      if (selectedUser && selectedUser.users) {
        const userNames = selectedUser.users.map(user => user.name);
        console.log(`代他人预定，用户: ${userNames.join(', ')}`);
      }
      break;
  }
};
```

### 4. 数据结构说明

```javascript
// 选择"个人预定"时的数据结构
{
  booking_type: "个人预定",
  quantity: null,
  selected_user: null
}

// 选择"预定多份"时的数据结构
{
  booking_type: "预定多份",
  quantity: 5,                // 用户输入的份数
  selected_user: null
}

// 选择"待他人预定"时的数据结构
{
  booking_type: "待他人预定",
  quantity: null,
  selected_user: {            // yhc-select-user 组件返回的数据结构
    departments: [],          // 选择的部门（如果有）
    users: [                  // 选择的用户列表
      {
        userid: "user123",
        name: "张三",
        avatar: "avatar_url"
      }
    ]
  }
}
```

## 注意事项

1. **数据清理**：切换预定方式时，会自动清理之前模式的相关数据
2. **表单验证**：各子组件都有独立的验证规则
3. **钉钉集成**：选人组件依赖钉钉环境，需要在钉钉应用中使用
4. **样式继承**：组件样式与项目整体风格保持一致
5. **数据监听**：建议使用 change 事件监听数据变化，而不是直接监听表单数据
6. **交互方式**：采用下拉选择器形式，点击输入框弹出选项列表

## 更新日志

- v1.1.0: 重构为下拉选择器形式，参考 yhc-picker 组件的样式和交互规范
- v1.0.0: 初始版本，支持三种预定方式及相应子组件（单选按钮组形式）
