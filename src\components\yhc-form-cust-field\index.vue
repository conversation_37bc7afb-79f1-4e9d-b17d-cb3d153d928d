<template>
  <div class="wrapper-form" v-if="isShow">
    <van-form @submit="onSubmit">
      <div class="group-wrapper" v-for="(arr, index) in config.groupForm" :key="index">
        <yhc-form-item v-for="(item, i) in config.form.slice(...arr)" :key="item.key" :config="item" :form="form" />
      </div>
      <div style="margin: 16px" :class="`${config.button.position == 'bottom' ? 'button-pos' : ''}`">
        <van-button v-if="config.button.isShow" :loading="loading" block type="primary"
          :loading-text="`正在${config.button.text}..`" native-type="submit">
          {{ config.button.text }}
        </van-button>
      </div>
    </van-form>
  </div>
  <van-empty v-else description="加载中.." />
</template>
<script setup>
import {
  ref,
  reactive,
  getCurrentInstance,
  watchEffect,
  watch,
  toRaw,
} from "vue";
import { deepAssign, handleJson } from "@/untils";
import { showToast } from "vant";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
// Props定义 - 自定义表单字段组件配置
const props = defineProps({
  config: Object,          // 表单配置 (对象) - 包含表单的完整配置信息
  form: Object,            // 表单数据 (对象) - 外部传入的表单数据，用于初始化
});

// 组件状态管理
let isShow = ref(false);   // 是否显示表单 (布尔值) - 控制表单的显示和隐藏

// 表单数据初始化 - 支持多种组件类型的数据格式
let form = reactive({
  // 示例数据格式说明:
  // "yhc-switch": 1,                    // 开关组件: 数字 (0/1)
  // "yhc-calendar": '["2023-6-19", "2023-6-22"]',  // 日历组件: JSON字符串数组
  // "yhc-input": "dfgdfgdfg",           // 输入框组件: 字符串
  // "yhc-picker": '[{"id":96},{"id":93}]',  // 选择器组件: JSON字符串对象数组
  // "yhc-select-image": '["http://..."]',   // 图片选择组件: JSON字符串URL数组
  // "yhc-select-user": '{"departments":[...], "users":[...]}',  // 用户选择组件: JSON字符串对象
});

// 处理JSON格式的表单数据
handleJson(form, false);

// 默认配置
let config = {
  // API接口配置
  curl: {
    info: "",            // 获取详情接口地址 (字符串) - 例: "/api/info" - 编辑模式时获取数据
    add: "",             // 新增数据接口地址 (字符串) - 例: "/api/add" - 表单提交时调用
    edit: "",            // 编辑数据接口地址 (字符串) - 例: "/api/edit" - 修改数据时调用
  },
  postData: {},          // 请求参数 (对象) - 发送给接口的额外参数

  // 表单字段配置
  form: [],              // 表单字段数组 (数组) - 表单项配置，每个元素为一个字段配置对象
  groupForm: [],         // 表单分组配置 (数组) - 控制表单字段的分组显示，如: [[0,2], [2,4]]

  // 按钮配置
  button: {
    isShow: true,        // 是否显示按钮 (布尔值) - true: 显示提交按钮, false: 隐藏按钮
    text: "提交",        // 按钮文字 (字符串) - 按钮显示的文本
    position: "normal",  // 按钮位置 (字符串) - "bottom": 底部固定, "normal": 正常位置
  },

  // 数据处理配置
  format: null,          // 数据格式化函数 (函数) - 获取数据后的自定义处理函数
};
deepAssign(config, props.config, { postData: route.query });
if (props.form) {
  console.log(111111, props.form);
  deepAssign(form, props.form);
}
console.log(2222, form);

if (!config.groupForm && config.form) {
  config.groupForm = [0, config.form.length - 1];
}
let url = null;
let loading = ref(false);
let emits = defineEmits(["onSubmit"]);
var getInfo = () => {
  loading.value = true;
  proxy
    .$post(config.curl.info, config.postData)
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        handleJson(res, false);
        if (config.format) {
          config.format(res);
        }
        Object.assign(form, res);
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    })
    .finally(() => {
      loading.value = false;
      isShow.value = true;
    });
};
const getCustField = () => {
  loading.value = true;
  proxy
    .$post(config.curl.custfield, {})
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        handleCustField(res);
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    })
    .finally(() => {
      loading.value = false;
    });
};
const handleCustField = (res) => {
  let map = {
    0: "yhc-input",
    1: "yhc-picker",
    2: "yhc-switch",
    3: "yhc-select-image",
    4: "yhc-picker-date",
  };
  let configFieldLs = res.map((el) => {
    switch (el.type * 1) {
      case 0:
        return {
          label: el.title,
          type: "text",
          key: el.title,
          placeholder: el.placeholder,
          sort: el.sort,
          // "right-icon": "/人",
          component: "yhc-input",
          rules: [{ required: !!el.required, message: `请填写${el.title}` }],
        };
        break;
      default:
        console.log("没有此种类型组件");
    }
  });
  let cf_from = config.form;
  configFieldLs.sort((a, b) => a.sort - b.sort);
  configFieldLs.forEach((el, i) => {
    if (el.sort >= cf_from.length) {
      cf_from.push(el);
    } else {
      cf_from.splice(el.sort - 1, 0, el);
    }
  });
};
if (config.curl.custfield) {
  getCustField();
}
if ("id" in config.postData) {
  url = config.curl.edit;
  getInfo();
} else {
  url = config.curl.add;
  isShow.value = true;
}
// watch(form, (v, o) => {
//   console.log("watch---form----》", v, o);
// });
// 过滤空值的工具函数
const filterEmptyValues = (obj) => {
  const filtered = {};

  Object.keys(obj).forEach(key => {
    const value = obj[key];

    // 保留有意义的值
    if (value !== undefined &&
        value !== null &&
        value !== '' &&
        !(Array.isArray(value) && value.length === 0) &&
        !(typeof value === 'object' && value !== null && Object.keys(value).length === 0)) {
      filtered[key] = value;
    }
  });

  return filtered;
};

const onSubmit = (e) => {
  let postData = toRaw(form);
  handleJson(postData, true);
  console.log("提交数据---11---》", e, form, postData, url);
  emits("onSubmit", postData);
  if (url) {
    loading.value = true;

    // 🔧 关键修复：只发送有数据的字段
    // 过滤掉空值的表单数据
    const filteredPostData = filterEmptyValues(postData);

    // 过滤掉空值的config.postData（保留必要的字段如id、dininghall_id等）
    const filteredConfigData = filterEmptyValues(config.postData);

    // 合并过滤后的数据
    const finalData = Object.assign(filteredPostData, filteredConfigData);

    console.log("原始表单数据:", postData);
    console.log("原始配置数据:", config.postData);
    console.log("过滤后的最终提交数据:", finalData);

    proxy
      .$post(url, finalData)
      .then((res) => {
        if (!res.errcode) {
          showToast("操作成功~");
          setTimeout(() => {
            router.go(-1);
          }, 2000);
        } else {
          throw res.errmsg;
        }
      })
      .catch((err) => {
        console.log(err);
        showToast(err);
      })
      .finally(() => {
        loading.value = false;
      });
  }
};
</script>
<style lang="scss" scoped>
.wrapper-form {
  background: #f2f3f4;
  box-sizing: border-box;
  border-top: 0.5px solid #f2f3f4;
  padding-bottom: 60px;

  .group-wrapper {
    margin: 16px 16px 24px 16px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .button-pos {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
