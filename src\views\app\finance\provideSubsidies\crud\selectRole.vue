<template>
  <div class="select-role-container">
    <!-- 导航栏 -->
    <!-- <van-nav-bar 
      title="选择角色" 
      left-arrow 
      @click-left="goBack"
      fixed
      placeholder
    /> -->
    
    <!-- 搜索栏 -->
    <div class="search-header">
      <van-search
        v-model="searchValue"
        placeholder="搜索角色"
        background="transparent"
        shape="round" 
        :show-action="false"
      />
    </div>
    
    <!-- 角色列表 -->
    <div class="role-list">
      <van-checkbox-group v-model="selectedRoles">
        <template v-for="role in filteredRoles" :key="role.id">
          <div class="role-item">
            <van-checkbox :name="role.id" class="role-checkbox">
              <div class="role-info">
                <div class="role-name">{{ role.name }}</div>
                <div class="role-desc">{{ role.description || '暂无描述' }}</div>
                <div class="role-count">{{ role.memberCount || 0 }}人</div>
              </div>
            </van-checkbox>
          </div>
        </template>
      </van-checkbox-group>
      
      <!-- 空状态 -->
      <van-empty 
        v-if="filteredRoles.length === 0" 
        description="暂无角色数据"
        image="search"
      />
    </div>
    
    <!-- 底部操作栏 -->
    <van-action-bar class="action-bar">
      <van-action-bar-icon 
        icon="clear" 
        text="清空" 
        @click="clearSelection"
        v-if="selectedRoles.length > 0"
      />
      <van-action-bar-button 
        type="primary" 
        :text="`确定(${selectedRoles.length})`"
        @click="confirmSelection"
      />
    </van-action-bar>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showFailToast, showDialog } from 'vant'
import { useLoginStore } from '@/store/dingLogin'

const router = useRouter()
const app = useLoginStore()

// 搜索值
const searchValue = ref('')

// 选中的角色
const selectedRoles = ref([])

// 角色列表数据
const roleList = ref([
  {
    id: 1,
    name: '管理员',
    description: '系统管理员角色',
    memberCount: 5
  },
  {
    id: 2,
    name: '财务',
    description: '财务管理角色',
    memberCount: 3
  },
  {
    id: 3,
    name: '人事',
    description: '人事管理角色',
    memberCount: 2
  },
  {
    id: 4,
    name: '销售',
    description: '销售人员角色',
    memberCount: 10
  },
  {
    id: 5,
    name: '技术',
    description: '技术开发角色',
    memberCount: 8
  }
])

// 过滤后的角色列表
const filteredRoles = computed(() => {
  if (!searchValue.value) {
    return roleList.value
  }
  return roleList.value.filter(role => 
    role.name.toLowerCase().includes(searchValue.value.toLowerCase()) ||
    (role.description && role.description.toLowerCase().includes(searchValue.value.toLowerCase()))
  )
})

// 页面加载
onMounted(() => {
  // 如果有之前选择的角色，恢复选择状态
  if (app.visiterList && app.visiterList.length > 0) {
    selectedRoles.value = app.visiterList.map(item => item.id)
  }

  // 这里可以调用API获取真实的角色数据
  loadRoleList()
})

// 加载角色列表
const loadRoleList = async () => {
  try {
    // 这里可以调用真实的API
    // const response = await proxy.$get('role/list')
    // roleList.value = response.data

    // 目前使用模拟数据
    console.log('角色列表加载完成')
  } catch (error) {
    console.error('加载角色列表失败:', error)
    showToast('加载角色列表失败')
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 清空选择
const clearSelection = () => {
  selectedRoles.value = []
}

// 确认选择
const confirmSelection = () => {
  if (selectedRoles.value.length === 0) {
    showToast({
      message: '请选择角色',
      type: 'text',
      duration: 2000
    })
    return
  }
  
  // 构造选中的角色数据
  const selectedRoleData = roleList.value.filter(role => 
    selectedRoles.value.includes(role.id)
  ).map(role => ({
    id: role.id,
    name: role.name,
    description: role.description,
    memberCount: role.memberCount
  }))
  
  // 保存到store
  app.visiterList = selectedRoleData
  
  showToast(`已选择${selectedRoles.value.length}个角色`)
  
  // 返回上一页
  setTimeout(() => {
    router.go(-1)
  }, 1000)
}
</script>

<style lang="scss" scoped>
.select-role-container {
  background: #f2f3f4;
  min-height: 100vh;
  padding-bottom: 60px;
}

.search-header {
  background: #fff;
  padding: 0px 12px;
  margin-top: 10px;
  
  .van-search {
    :deep(.van-search__content) {
      background: #f5f5f5 !important;
      border-radius: 20px !important;
      border: none !important;
      padding: 8px 16px !important;
    }
    
    :deep(.van-field__control) {
      background: transparent !important;
      font-size: 14px !important;
      color: #999 !important;
    }
    
    :deep(.van-search__field) {
      background: transparent !important;
      padding: 0 !important;
    }
    
    :deep(.van-field__body) {
      background: transparent !important;
    }
    
    :deep(.van-icon-search) {
      color: #999 !important;
      font-size: 16px !important;
    }
  }
}

.role-list {
  padding: 16px;
  
  .role-item {
    background: #fff;
    border-radius: 12px;
    margin-bottom: 12px;
    overflow: hidden;
    
    .role-checkbox {
      width: 100%;
      padding: 16px;
      
      :deep(.van-checkbox__icon) {
        margin-right: 12px;
      }
      
      .role-info {
        flex: 1;
        
        .role-name {
          font-size: 16px;
          color: #323233;
          font-weight: 500;
          margin-bottom: 4px;
        }
        
        .role-desc {
          font-size: 14px;
          color: #969799;
          margin-bottom: 4px;
        }
        
        .role-count {
          font-size: 12px;
          color: #1989fa;
          background: #f0f9ff;
          padding: 2px 8px;
          border-radius: 10px;
          display: inline-block;
        }
      }
    }
  }
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #ebedf0;
  
  :deep(.van-action-bar-button) {
    flex: 1;
  }
}
</style>
