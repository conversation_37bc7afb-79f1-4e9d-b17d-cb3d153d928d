# yhc-tag-selector 标签选择器组件

一个基于 Vant UI 的可复用标签选择器组件，支持预设标签选择和自定义标签管理。

## 功能特性

- 🏷️ **预设标签选择**: 显示预设标签列表，支持多选
- ✏️ **自定义标签管理**: 支持添加、删除自定义标签
- ⌨️ **快捷操作**: 支持回车键快速添加标签
- 🎯 **选中状态管理**: 支持标签的选中/取消选中切换
- 📱 **响应式设计**: 适配移动端和桌面端
- 🔗 **双向绑定**: 支持v-model双向绑定返回选中的标签ID数组
- 🌐 **API集成**: 支持后端API获取预设标签数据
- 🎨 **视觉区分**: 选中标签有明显的视觉效果

## 配置参数

### 基础配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| label | String | "标签选择" | 字段标签文本 |
| key | String | "" | 字段名称，用于表单数据绑定 |
| required | Boolean | false | 是否必填 |
| disabled | Boolean | false | 是否禁用整个组件 |
| border | Boolean | true | 是否显示边框 |
| labelWidth | String | "" | 标签宽度 |
| rules | Array | [] | 表单验证规则 |
| defaultSelected | Array | [] | 默认选中的标签ID数组 |

### 选项配置 (opts)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| url | String | "" | 获取预设标签的API地址 |
| postData | Object | {} | API请求参数 |
| text_key | String | "name" | 预设标签显示字段名 |
| contrast_key | String | "id" | 预设标签值字段名 |
| defaultList | Array | [] | 默认预设标签数据 |
| maxTagLength | Number | 10 | 自定义标签最大长度 |
| maxCustomTags | Number | 20 | 最大自定义标签数量 |

## 基础用法

### 使用默认数据

```vue
<template>
  <yhc-tag-selector :config="tagConfig" :form="form" />
</template>

<script setup>
import { reactive } from 'vue'
import yhcTagSelector from '@/components/yhc-tag-selector/index.vue'

const form = reactive({
  selected_tags: []
})

const tagConfig = {
  label: "选择标签",
  key: "selected_tags",
  opts: {
    defaultList: [
      { id: 1, name: "热门" },
      { id: 2, name: "推荐" },
      { id: 3, name: "新品" },
      { id: 4, name: "特价" }
    ]
  },
  defaultSelected: [1, 2] // 默认选中"热门"和"推荐"
}
</script>
```

### 使用API数据

```vue
<template>
  <yhc-tag-selector :config="tagConfig" :form="form" @change="onTagChange" />
</template>

<script setup>
import { reactive } from 'vue'

const form = reactive({
  dish_tags: []
})

const tagConfig = {
  label: "菜品标签",
  key: "dish_tags",
  required: true,
  opts: {
    url: "/api/tags/list", // 后端API地址
    postData: {
      type: "dish" // 请求参数
    },
    text_key: "tag_name", // 后端返回的显示字段
    contrast_key: "tag_id", // 后端返回的ID字段
    maxTagLength: 8,
    maxCustomTags: 10
  },
  rules: [
    { required: true, message: "请选择至少一个标签" }
  ]
}

const onTagChange = (data) => {
  console.log('标签变化:', data.value)
}
</script>
```

### 在yhc-form中使用

```javascript
{
  label: "商品标签",
  key: "product_tags",
  component: "yhc-tag-selector",
  required: true,
  opts: {
    url: "/api/product/tags",
    text_key: "name",
    contrast_key: "id",
    maxTagLength: 12,
    maxCustomTags: 15
  },
  defaultSelected: [1, 3, 5],
  rules: [
    { required: true, message: "请选择商品标签" }
  ]
}
```

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 标签选择变化时触发 | { component: "yhc-tag-selector", key: 字段名, value: 选中标签ID数组 } |

## 数据格式

### 预设标签数据格式

```javascript
// API返回或defaultList格式
[
  {
    id: 1,
    name: "热门标签",
    // 其他字段...
  },
  {
    id: 2, 
    name: "推荐标签",
    // 其他字段...
  }
]
```

### 表单数据格式

```javascript
// 组件绑定的表单值
{
  selected_tags: [1, 2, -1640995200123] // 包含预设标签ID和自定义标签临时ID
}
```

## 样式定制

组件支持通过CSS变量或深度选择器自定义样式：

```scss
// 自定义标签样式
:deep(.tag-item) {
  border-radius: 20px; // 更圆的标签
  font-weight: 500;
}

// 自定义选中状态颜色
:deep(.tag-selected) {
  background-color: #07c160; // 绿色主题
  border-color: #07c160;
}

// 自定义删除按钮样式
:deep(.tag-delete) {
  color: #ff4444;
}
```

## API接口规范

### 获取预设标签接口

**请求方式**: GET  
**请求地址**: 配置中的 `opts.url`  
**请求参数**: 配置中的 `opts.postData`

**响应格式**:
```json
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "name": "标签名称"
    }
  ]
}
```

## 注意事项

1. 自定义标签使用负数作为临时ID，避免与后端ID冲突
2. 组件会自动去重，不允许添加重名标签
3. 支持表单验证，可配置必填和自定义规则
4. 禁用状态下用户无法进行任何操作
5. 删除自定义标签时会同时从选中列表中移除
6. 组件名称和所有文本内容使用中文，符合项目规范
7. 与yhc-form组件体系完全兼容，支持自动注册和使用

## 最佳实践

1. **合理设置标签长度限制**: 根据UI设计设置合适的`maxTagLength`
2. **控制自定义标签数量**: 通过`maxCustomTags`避免标签过多影响用户体验
3. **提供默认选中**: 使用`defaultSelected`提升用户体验
4. **配置表单验证**: 根据业务需求设置合适的验证规则
5. **响应式设计**: 组件已适配移动端，无需额外处理
