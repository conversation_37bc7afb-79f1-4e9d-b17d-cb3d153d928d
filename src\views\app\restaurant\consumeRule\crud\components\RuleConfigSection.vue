<template>
  <div class="rule-config-section">
    <!-- 考勤规则 -->
    <div class="form-group">
      <van-cell-group inset>
        <van-field
          v-model="attendanceRuleText"
          label="考勤规则"
          placeholder="请选择考勤规则"
          readonly
          :disabled="loading"
          @click="showAttendanceRulePicker = true"
        >
          <template #button>
            <van-button 
              size="small" 
              type="primary" 
              plain
              @click.stop="handleAddAttendanceRule"
            >
              添加
            </van-button>
          </template>
        </van-field>
      </van-cell-group>
    </div>

    <!-- 减免规则 -->
    <div class="form-group">
      <van-cell-group inset>
        <van-field
          v-model="discountRuleText"
          label="减免规则"
          placeholder="请选择减免规则"
          readonly
          :disabled="loading"
          @click="showDiscountRulePicker = true"
        >
          <template #button>
            <van-button 
              size="small" 
              type="primary" 
              plain
              @click.stop="handleAddDiscountRule"
            >
              添加
            </van-button>
          </template>
        </van-field>
      </van-cell-group>
    </div>

    <!-- 考勤规则选择器 -->
    <van-popup v-model:show="showAttendanceRulePicker" position="bottom" :style="{ height: '50vh' }">
      <van-picker
        :columns="attendanceRuleColumns"
        title="选择考勤规则"
        @confirm="handleAttendanceRuleConfirm"
        @cancel="showAttendanceRulePicker = false"
      />
    </van-popup>

    <!-- 减免规则选择器 -->
    <van-popup v-model:show="showDiscountRulePicker" position="bottom" :style="{ height: '50vh' }">
      <van-picker
        :columns="discountRuleColumns"
        title="选择减免规则"
        @confirm="handleDiscountRuleConfirm"
        @cancel="showDiscountRulePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const { proxy } = getCurrentInstance()
const router = useRouter()

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:formData'])

// 本地表单数据
const localFormData = computed({
  get: () => props.formData,
  set: (value) => emit('update:formData', value)
})

// 选择器状态
const showAttendanceRulePicker = ref(false)
const showDiscountRulePicker = ref(false)

// 选择器数据
const attendanceRuleColumns = ref([])
const discountRuleColumns = ref([])

// 显示文本
const attendanceRuleText = ref('')
const discountRuleText = ref('')

// 获取考勤规则列表
const fetchAttendanceRules = async () => {
  try {
    const res = await proxy.$get('/attendance_rule/get_all', {})
    if (res.code === 200) {
      attendanceRuleColumns.value = res.data.map(item => ({
        text: item.rule_name,
        value: item.id,
        ...item
      }))
    }
  } catch (error) {
    console.error('获取考勤规则列表失败:', error)
  }
}

// 获取减免规则列表
const fetchDiscountRules = async () => {
  try {
    const res = await proxy.$get('/discount_rule/get_all', {})
    if (res.code === 200) {
      discountRuleColumns.value = res.data.map(item => ({
        text: item.rule_name,
        value: item.id,
        detail: item.detail,
        ...item
      }))
    }
  } catch (error) {
    console.error('获取减免规则列表失败:', error)
  }
}

// 处理考勤规则确认
const handleAttendanceRuleConfirm = ({ selectedOptions }) => {
  const selected = selectedOptions[0]
  localFormData.value.attendance_rule = selected.value
  attendanceRuleText.value = selected.text
  showAttendanceRulePicker.value = false
}

// 处理减免规则确认
const handleDiscountRuleConfirm = ({ selectedOptions }) => {
  const selected = selectedOptions[0]
  localFormData.value.discount_rule = selected.value
  discountRuleText.value = selected.text
  showDiscountRulePicker.value = false
}

// 添加考勤规则
const handleAddAttendanceRule = () => {
  router.push('/systemConfig/attendanceRuleAdd')
}

// 添加减免规则
const handleAddDiscountRule = () => {
  router.push('/systemConfig/reduceRuleAdd')
}

onMounted(() => {
  fetchAttendanceRules()
  fetchDiscountRules()
})
</script>

<style lang="scss" scoped>
.rule-config-section {
  .form-group {
    margin-bottom: 12px;
  }
  
  .van-cell-group {
    margin: 0 16px;
  }
}
</style>
