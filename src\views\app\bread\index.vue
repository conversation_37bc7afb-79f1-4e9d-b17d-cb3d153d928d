<template>
  <div class="demo-container">
    <van-nav-bar title="面包屑组件演示" left-arrow @click-left="$router.go(-1)" />

    <div class="demo-section">
      <h3>基本用法 (basic.tsx)</h3>
      <yhc-breadcrumb :config="basicConfig" :form="form1" />
    </div>

    <div class="demo-section">
      <h3>带图标</h3>
      <yhc-breadcrumb :config="iconConfig" :form="form2" />
    </div>

    <div class="demo-section">
      <h3>自定义分隔符</h3>
      <yhc-breadcrumb :config="separatorConfig" :form="form3" />
    </div>

    <div class="demo-section">
      <h3>限制显示数量</h3>
      <yhc-breadcrumb :config="maxItemsConfig" :form="form4" />
    </div>

    <div class="demo-section">
      <h3>不同尺寸</h3>
      <div class="size-demo">
        <div class="size-item">
          <p>Small</p>
          <yhc-breadcrumb :config="smallConfig" :form="form5" />
        </div>
        <div class="size-item">
          <p>Normal</p>
          <yhc-breadcrumb :config="normalConfig" :form="form6" />
        </div>
        <div class="size-item">
          <p>Large</p>
          <yhc-breadcrumb :config="largeConfig" :form="form7" />
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h3>自动生成（根据路由）</h3>
      <yhc-breadcrumb :config="autoConfig" :form="form8" />
    </div>

    <div class="demo-section">
      <h3>禁用状态</h3>
      <yhc-breadcrumb :config="disabledConfig" :form="form9" />
    </div>

    <div class="demo-section">
      <h3>自定义样式</h3>
      <yhc-breadcrumb :config="customConfig" :form="form10" />
    </div>

    <div class="demo-section">
      <h3>点击事件测试</h3>
      <yhc-breadcrumb :config="clickConfig" :form="form11" @click="handleBreadcrumbClick" />
      <p class="result" v-if="clickResult">{{ clickResult }}</p>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from "vue";

const clickResult = ref("");

// 表单数据
const form1 = reactive({ breadcrumb: "" });
const form2 = reactive({ breadcrumb: "" });
const form3 = reactive({ breadcrumb: "" });
const form4 = reactive({ breadcrumb: "" });
const form5 = reactive({ breadcrumb: "" });
const form6 = reactive({ breadcrumb: "" });
const form7 = reactive({ breadcrumb: "" });
const form8 = reactive({ breadcrumb: "" });
const form9 = reactive({ breadcrumb: "" });
const form10 = reactive({ breadcrumb: "" });
const form11 = reactive({ breadcrumb: "" });

// 配置对象
const basicConfig = {
  key: "breadcrumb",
  label: "基本用法 (basic.tsx)",
  items: [
    { text: "首页", path: "/home", clickable: true },
    { text: "工作台", path: "/app", clickable: true },
    { text: "演示页面", path: "/demo", clickable: true },
    { text: "面包屑演示", path: "/demo/breadcrumb", clickable: true },
    { text: "当前页" }
  ]
};

const iconConfig = {
  key: "breadcrumb",
  items: [
    { text: "首页", path: "/home", icon: "home-o", clickable: true },
    { text: "工作台", path: "/app", icon: "setting-o", clickable: true },
    { text: "订单管理", path: "/bill", icon: "records-o", clickable: true },
    { text: "个人中心", path: "/user", icon: "user-o", clickable: true },
    { text: "当前页面", icon: "location-o" }
  ]
};

const separatorConfig = {
  key: "breadcrumb",
  separatorIcon: "arrow-right",
  separatorColor: "#1989fa",
  items: [
    { text: "首页", path: "/home", clickable: true },
    { text: "工作台", path: "/app", clickable: true },
    { text: "演示页面", path: "/demo", clickable: true },
    { text: "当前页面" }
  ]
};

const maxItemsConfig = {
  key: "breadcrumb",
  maxItems: 4,
  items: [
    { text: "首页", path: "/home", clickable: true },
    { text: "工作台", path: "/app", clickable: true },
    { text: "订单管理", path: "/bill", clickable: true },
    { text: "个人中心", path: "/user", clickable: true },
    { text: "演示页面", path: "/demo", clickable: true },
    { text: "面包屑演示", path: "/demo/breadcrumb", clickable: true },
    { text: "当前页面" }
  ]
};

const smallConfig = {
  key: "breadcrumb",
  size: "small",
  items: [
    { text: "首页", path: "/home", clickable: true },
    { text: "工作台", path: "/app", clickable: true },
    { text: "当前页" }
  ]
};

const normalConfig = {
  key: "breadcrumb",
  size: "normal",
  items: [
    { text: "首页", path: "/home", clickable: true },
    { text: "工作台", path: "/app", clickable: true },
    { text: "当前页" }
  ]
};

const largeConfig = {
  key: "breadcrumb",
  size: "large",
  items: [
    { text: "首页", path: "/home", clickable: true },
    { text: "工作台", path: "/app", clickable: true },
    { text: "当前页" }
  ]
};

const autoConfig = {
  key: "breadcrumb",
  autoGenerate: true,
  showHome: true,
  homeText: "首页",
  homePath: "/home"
};

const disabledConfig = {
  key: "breadcrumb",
  disabled: true,
  items: [
    { text: "首页", path: "/home", clickable: true },
    { text: "工作台", path: "/app", clickable: true },
    { text: "当前页" }
  ]
};

const customConfig = {
  key: "breadcrumb",
  separator: "/",
  linkColor: "#07c160",
  activeColor: "#ee0a24",
  separatorColor: "#ff976a",
  items: [
    { text: "首页", path: "/home", clickable: true },
    { text: "工作台", path: "/app", clickable: true },
    { text: "订单管理", path: "/bill", clickable: true },
    { text: "当前页面" }
  ]
};

const clickConfig = {
  key: "breadcrumb",
  items: [
    { text: "首页", path: "/home", clickable: true },
    { text: "工作台", path: "/app", clickable: true },
    { text: "订单管理", path: "/bill", clickable: true },
    { text: "个人中心", path: "/user", clickable: true },
    { text: "当前页面" }
  ],
  onClick: (value, item, index) => {
    clickResult.value = `点击了: ${item.text} (值: ${value}, 索引: ${index})`;
  }
};

const handleBreadcrumbClick = (value, item, index) => {
  console.log("面包屑点击事件:", value, item, index);
};
</script>

<style lang="scss" scoped>
.demo-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.demo-section {
  background: white;
  margin: 8px;
  padding: 16px;
  border-radius: 8px;

  h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #323233;
    font-weight: 500;
  }

  .result {
    margin: 12px 0 0 0;
    padding: 8px 12px;
    background: #f7f8fa;
    border-radius: 4px;
    font-size: 14px;
    color: #646566;
    border-left: 3px solid #1989fa;
  }
}

.size-demo {
  .size-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    p {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: #646566;
    }
  }
}
</style>
