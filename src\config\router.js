export const config = {
  // 开启白名单验证
  isOffWhiteList:
    import.meta.env.VITE_APP_ISOFFWHITELIST === "false" ? false : true,
  // 路由免验白名单
  whiteList: ["/login", "/home", "/user"],
};

const routes = [
  {
    path: "/",
    redirect: "/login",
  },
  {
    name: "login",
    path: "/login",
    component: () => import(/* webpackChunkName: "login" */ "@/views/login"),
    children: [],
  },
  {
    name: "home",
    path: "/home",
    meta: { hasTabbar: true },
    component: () => import(/* webpackChunkName: "home" */ "@/views/home"),
    children: [],
  },
  {
    name: "orderMeal",
    path: "/orderMeal",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "orderMeal" */ "@/views/home/<USER>/orderMeal"
      ),
    children: [],
  },
   {
    name: "editAddress",
    path: "/editAddress",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "deptReceditAddressordDetail" */ "@/views/home/<USER>/orderMeal/subpage/editAddress"
      ),
    children: [],
  },
  {
    name: "confirmOrder",
    path: "/confirmOrder",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "confirmOrder" */ "@/views/home/<USER>/orderMeal/subpage/confirmOrder"
      ),
    children: [],
  },
  {
    name: "reportSenior",
    path: "/reportSenior",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "reportSenior" */ "@/views/home/<USER>/reportSenior"
      ),
    children: [],
  },
  {
    name: "orderDishDetail",
    path: "/orderDishDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "orderDishDetail" */ "@/views/home/<USER>/diashDetail"
      ),
    children: [],
  },
  {
    name: "report",
    path: "/report",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "manageVisitPlan" */ "@/views/home/<USER>/report"
      ),
    children: [
      {
        name: "reportConfirm",
        path: "/report/confirm",
        meta: { hasTabbar: false },
        component: () =>
          import(
            /* webpackChunkName: "manageVisitPlan" */ "@/views/home/<USER>/report/subpage/report"
          ),
        children: [],
      },
      {
        name: "selectDept",
        path: "/selectDept",
        meta: { hasTabbar: false },
        component: () =>
          import(
            /* webpackChunkName: "manageVisitPlan" */ "@/views/home/<USER>/report/subpage/selectUser"
          ),
        children: [],
      },
      {
        name: "reportResult",
        path: "/reportResult",
        meta: { hasTabbar: false },
        component: () =>
          import(
            /* webpackChunkName: "manageVisitPlan" */ "@/views/home/<USER>/report/subpage/reportResult"
          ),
        children: [],
      },
      
    ],
  },
  {
    name: "bill",
    path: "/bill",
    meta: { hasTabbar: true },
    component: () => import(/* webpackChunkName: "bill" */ "@/views/bill"),
    children: [],
  },
  {
    name: "billdetail",
    path: "/billdetail",
    meta: { hasTabbar: false },
    component: () =>
      import(/* webpackChunkName: "bill" */ "@/views/bill/subpage/detail"),
    children: [],
  },
  {
    name: "app",
    path: "/app",
    meta: { hasTabbar: true },
    component: () => import(/* webpackChunkName: "app" */ "@/views/app"),
    children: [],
  },
  {
    name: "user",
    path: "/user",
    meta: { hasTabbar: true },
    component: () => import(/* webpackChunkName: "user" */ "@/views/user"),
    children: [],
  },
  // demo
  {
    name: "demo",
    path: "/demo",
    meta: { hasTabbar: false },
    component: () =>
      import(/* webpackChunkName: "busManagement" */ "@/views/app/demo"),
    children: [],
  },
  {
    name: "bread",
    path: "/bread",
    meta: { hasTabbar: false },
    component: () =>
      import(/* webpackChunkName: "busManagement" */ "@/views/app/bread"),
    children: [],
  },
  // 表单demo
  {
    name: "form",
    path: "/form",
    meta: { hasTabbar: false },
    component: () =>
      import(/* webpackChunkName: "busManagement" */ "@/views/app/form"),
    children: [],
  },
  // 分段器
  {
    name: "steps",
    path: "/steps",
    meta: { hasTabbar: false },
    component: () =>
      import(/* webpackChunkName: "busManagement" */ "@/views/app/steps"),
    children: [],
  },
  // 骨架屏演示
  {
    name: "skeleton-demo",
    path: "/skeleton-demo",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/skeleton-demo"
      ),
    children: [],
  },
  // 表格演示
  {
    name: "table-demo",
    path: "/table-demo",
    meta: { hasTabbar: false },
    component: () =>
      import(/* webpackChunkName: "busManagement" */ "@/views/app/table-demo"),
    children: [],
  },
  // 联系人页面
  {
    name: "contacts",
    path: "/contacts",
    meta: { hasTabbar: false },
    component: () =>
      import(/* webpackChunkName: "busManagement" */ "@/views/app/contacts"),
    children: [],
  },
  // 部门详情页面
  {
    name: "department",
    path: "/department",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/contacts/department"
      ),
    children: [],
  },
  // 添加子部门页面
  {
    name: "add-department",
    path: "/add-department",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/contacts/add-department"
      ),
    children: [],
  },
  // 部门设置页面
  {
    name: "department-settings",
    path: "/department-settings",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/contacts/department-settings"
      ),
    children: [],
  },
  // 添加人员页面
  {
    name: "add-member",
    path: "/add-member",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/contacts/add-member"
      ),
    children: [],
  },
  // 人员详情页面
  {
    name: "member-detail",
    path: "/member-detail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/contacts/member-detail"
      ),
    children: [],
  },
  // 人员恢复页面
  {
    name: "member-restore",
    path: "/member-restore",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/contacts/member-restore"
      ),
    children: [],
  },
  // 应用市场
  {
    name: "app-market",
    path: "/app-market",
    meta: { hasTabbar: false },
    component: () =>
      import(/* webpackChunkName: "busManagement" */ "@/views/app/app-market"),
    children: [],
  },

  // 菜品发布
  {
    name: "dishRelease",
    path: "/dishRelease",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/dishRelease"
      ),
    children: [],
  },
  {
    name: "release_info",
    path: "/app/restaurant/dishRelease/crud/releaseInfo",
    meta: { hasTabbar: false },
    component: () =>
      import("@/views/app/restaurant/dishRelease/crud/releaseInfo"),
  },
  {
    name: "dishes_list",
    path: "/app/restaurant/dishRelease/crud/dishes_list",
    meta: { hasTabbar: false },
    component: () =>
      import("@/views/app/restaurant/dishRelease/crud/dishes_list"),
  },
  //档口管理
  {
    name: "stall",
    path: "/stall",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/stall"
      ),
    children: [],
  },
  {
    name: "stallAdd",
    path: "/stallAdd",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/stall/crud/add"
      ),
    children: [],
  },
  {
    name: "stallDetail",
    path: "/stallDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/stall/crud/Detail"
      ),
    children: [],
  },
  //餐时管理
  {
    name: "menuTime",
    path: "/menuTime",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/menuTime"
      ),
    children: [],
  },
  {
    name: "menuTimeAdd",
    path: "/menuTimeAdd",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/menuTime/crud/add"
      ),
    children: [],
  },
  {
    name: "menuTimeDetail",
    path: "/menuTimeDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/menuTime/crud/detail"
      ),
    children: [],
  },
  //餐时组
  {
    name: "menuTimeGroup",
    path: "/menuTimeGroup",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/menuTimeGroup"
      ),
    children: [],
  },
  {
    name: "menuTimeGroupAdd",
    path: "/menuTimeGroupAdd",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/menuTimeGroup/crud/add"
      ),
    children: [],
  },
  {
    name: "menuTimeGroupDetail",
    path: "/menuTimeGroupDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/menuTimeGroup/crud/detail"
      ),
    children: [],
  },
  //餐费设置
  {
    name: "menuMeal",
    path: "/menuMeal",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/menuMeal"
      ),
    children: [],
  },
  // 餐厅管理-公费预定
  {
    name: "publicRese",
    path: "/publicRese",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/publicRese"
      ),
    children: [],
  },
  //消费规则
  {
    name: "consumeRule",
    path: "/consumeRule",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/consumeRule"
      ),
    children: [],
  },
  {
    name: "consumeRuleAdd",
    path: "/consumeRuleAdd",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/consumeRule/crud/add"
      ),
    children: [],
  },
  {
    name: "consumeRuleDetail",
    path: "/consumeRuleDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/consumeRule/crud/detail"
      ),
    children: [],
  },
  {
    name: "consumeRuleOnlineBook",
    path: "/consumeRuleOnlineBook",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/consumeRule/crud/onlineBook"
      ),
    children: [],
  },
  //财务管理-交易流水
  {
    name: "runningTab",
    path: "/finance/runningTab",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/runningTab"
      ),
    children: [],
  },
  //财务管理-交易流水列表
  {
    name: "savingsAccount",
    path: "/finance/runningTab/savingsAccount",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/runningTab/crud/savingsAccount"
      ),
    children: [],
  },
  //财务管理-交易流水详情
  {
    name: "result",
    path: "/finance/runningTab/result",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/runningTab/crud/result"
      ),
    children: [],
  },

  // 菜品分类
  {
    name: "dishSort",
    path: "/dishSort",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/dishSort"
      ),
    children: [],
  },
  // 新增菜品分类
  {
    name: "dishSortAdd",
    path: "/dishSortAdd",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/dishSort/crud/add"
      ),
    children: [],
  },
  // 菜品分类详情
  {
    name: "dishSortDetail",
    path: "/dishSortDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/dishSort/crud/detail"
      ),
    children: [],
  },
  // 菜品配置
  {
    name: "dishConfig",
    path: "/dishConfig",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/dishConfig"
      ),
    children: [],
  },
  // 菜品配置添加
  {
    name: "dishConfigAdd",
    path: "/dishConfigAdd",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/dishConfig/crud/add"
      ),
    children: [],
  },
  // 菜品配置详情
  {
    name: "dishConfigDetail",
    path: "/dishConfigDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/dishConfig/crud/detail"
      ),
    children: [],
  },
  // 包间配置
  {
    name: "roomConfig",
    path: "/roomConfig",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/roomConfig"
      ),
    children: [],
  },
  // 包间管理
  {
    name: "roomManagement",
    path: "/roomConfig/roomManagement",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/roomConfig/crud/roomManagement"
      ),
    children: [],
  },
  // 包间管理添加
  {
    name: "roomManagementAdd",
    path: "/roomConfig/roomManagement/add",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/roomConfig/crud/roomManagement/crud/add"
      ),
    children: [],
  },
  // 包间管理详情
  {
    name: "roomManagementDetail",
    path: "/roomConfig/roomManagement/detail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/roomConfig/crud/roomManagement/crud/detail"
      ),
    children: [],
  },
  // 表单配置
  {
    name: "formConfig",
    path: "/roomConfig/formConfig",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/roomConfig/crud/formConfig"
      ),
    children: [],
  },
  // 表单配置增加
  {
    name: "formConfigAdd",
    path: "/roomConfig/formConfig/add",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/roomConfig/crud/formConfig/crud/add"
      ),
    children: [],
  },
  // 表单配置详情
  {
    name: "formConfigDetail",
    path: "/roomConfig/formConfig/detail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/roomConfig/crud/formConfig/crud/detail"
      ),
    children: [],
  },
  // 套餐
  {
    name: "package",
    path: "/roomConfig/package",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/roomConfig/crud/package"
      ),
    children: [],
  },
  // 套餐增加
  {
    name: "form_dishes_publicAdd",
    path: "/form_dishes_publicAdd",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/roomConfig/crud/package/crud/add"
      ),
    children: [],
  },
  {
    name: "form_dishes_publicdetail",
    path: "/form_dishes_publicdetail",
    meta: { hasTabbar: false },
    component: () =>
      import("@/views/app/restaurant/roomConfig/crud/package/crud/detail"),
    children: [],
  },
  {
    name: "form_dishes_publicmodify",
    path: "/form_dishes_publicmodify",
    meta: { hasTabbar: false },
    component: () =>
      import("@/views/app/restaurant/roomConfig/crud/package/crud/modify"),
    children: [],
  },
  {
    name: "form_selectDishList",
    path: "/form_selectDishList",
    meta: { hasTabbar: false },
    component: () =>
      import(
        "@/views/app/restaurant/roomConfig/crud/package/crud/selectDishList"
      ),
    children: [],
  },

  // 套餐详情
  {
    name: "packageDetail",
    path: "/roomConfig/package/detail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/roomConfig/crud/package/crud/detail"
      ),
    children: [],
  },
  // 配置
  {
    name: "config",
    path: "/roomConfig/config",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/restaurant/roomConfig/crud/config"
      ),
    children: [],
  },
  // 系统管理-审计日志
  {
    name: "auditLog",
    path: "/auditLog",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/auditLog"
      ),
    children: [],
  },
  // 审计日志详情
  {
    name: "auditLogDetail",
    path: "/systemConfig/auditLog/detail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/auditLog/detail"
      ),
    children: [],
  },

  // 系统管理-系统公告
  {
    name: "announcement",
    path: "/announcement",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/announcement"
      ),
    children: [],
  },
  // 系统公告新增
  {
    name: "announcementAdd",
    path: "/systemConfig/announcement/add",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/announcement/add"
      ),
    children: [],
  },
  // 系统公告编辑
  {
    name: "announcementEdit",
    path: "/systemConfig/announcement/edit",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/announcement/edit"
      ),
    children: [],
  },
  // 系统公告详情
  {
    name: "announcementDetail",
    path: "/systemConfig/announcement/detail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/announcement/detail"
      ),
    children: [],
  },

  // 个性化配置
  {
    name: "personalSpace",
    path: "/personalSpace",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/personalSpace/config"
      ),
    children: [],
  },

  // 系统管理-计划任务
  {
    name: "scheduledTask",
    path: "/scheduledTask",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/scheduledTask"
      ),
    children: [],
  },
  // 计划任务新增
  {
    name: "scheduledTaskAdd",
    path: "/systemConfig/scheduledTask/add",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/scheduledTask/crud/add"
      ),
    children: [],
  },
  // 计划任务详情
  {
    name: "scheduledTaskDetail",
    path: "/systemConfig/scheduledTask/detail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/scheduledTask/crud/detail"
      ),
    children: [],
  },
  // 成本中心
  {
    name: "costCenter",
    path: "/costCenter",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/cost"
      ),
    children: [],
  },
  // 成本中心新增
  {
    name: "costCenterAdd",
    path: "/costCenter/add",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/cost/crud/add"
      ),
    children: [],
  },
  // 成本中心详情
  {
    name: "costCenterDetail",
    path: "/costCenter/detail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/cost/crud/detail"
      ),
    children: [],
  },

  //系统管理-场所设置
  {
    name: "venueSetup",
    path: "/venueSetup",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/system/venueSetup"
      ),
    children: [],
  },
  //系统管理-场所设置修改
  {
    name: "venueSetupAdd",
    path: "/venueSetupAdd",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/system/venueSetup/crud/add"
      ),
    children: [],
  },
  {
    name: "venueSetupDetail",
    path: "/venueSetupDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/system/venueSetup/crud/detail"
      ),
    children: [],
  },
  // 预定包间预定
  {
    name: "roomReserve",
    path: "/reserve/roomReserve",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/reserve/roomReserve"
      ),
    children: [],
  },
  // 新增预定包间预定
  {
    name: "roomReserveAdd",
    path: "/reserve/roomReserveAdd",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/reserve/roomReserve/crud/add"
      ),
    children: [],
  },
  // 详情 预定包间预定
  {
    name: "roomReserveDetail",
    path: "/reserve/roomReserveDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/reserve/roomReserve/crud/detail"
      ),
    children: [],
  },
  // 预定公费预定
  {
    name: "publicReserve",
    path: "/reserve/publicReserve",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/reserve/publicReserve"
      ),
    children: [],
  },
  // 新增公费预定
  {
    name: "publicReserveAdd",
    path: "/reserve/publicReserveAdd",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/reserve/publicReserve/crud/add"
      ),
    children: [],
  },
  // 详情 公费预定
  {
    name: "publicReserveDetail",
    path: "/reserve/publicReserveDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/reserve/publicReserve/crud/detail"
      ),
    children: [],
  },
  //系统配置-基础配置
  {
    name: "basicConfiguration",
    path: "/systemConfig/basicConfiguration",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/basicConfiguration"
      ),
    children: [],
  },
  //系统配置-设备管理
  {
    name: "deviceManagement",
    path: "/systemConfig/deviceManagement",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/deviceManagement"
      ),
    children: [],
  },
  //系统配置-设备管理-详情
  {
    name: "deviceManagementDetail",
    path: "/app/systemConfig/deviceManagement/detail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/deviceManagement/detail"
      ),
    children: [],
  },
  //系统配置-设备管理-详情-网络状态
  {
    name: "deviceManagementNetworkState",
    path: "/app/systemConfig/deviceManagement/networkState",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/deviceManagement/networkState"
      ),
    children: [],
  },
  //系统配置-设备管理-详情-系统信息
  {
    name: "deviceManagementSystemInfo",
    path: "/app/systemConfig/deviceManagement/systemInfo",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/deviceManagement/systemInfo"
      ),
    children: [],
  },
  //系统配置-设备管理-修改
  {
    name: "deviceManagementEdit",
    path: "/app/systemConfig/deviceManagement/edit",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/deviceManagement/edit"
      ),
    children: [],
  },
  //系统配置-设备管理-新增
  {
    name: "deviceManagementAdd",
    path: "/app/systemConfig/deviceManagement/add",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/deviceManagement/add"
      ),
    children: [],
  },
  //系统配置-设备管理-设备组详情
  {
    name: "deviceManagementGroupDetail",
    path: "/app/systemConfig/deviceManagement/groupDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/deviceManagement/groupDetail"
      ),
    children: [],
  },
  //系统配置-设备管理-编辑设备组
  {
    name: "deviceManagementEditGroup",
    path: "/app/systemConfig/deviceManagement/editGroup",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/deviceManagement/editGroup"
      ),
    children: [],
  },
  //系统配置-设备管理-创建设备组
  {
    name: "deviceManagementCreateGroup",
    path: "/app/systemConfig/deviceManagement/createGroup",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/deviceManagement/createGroup"
      ),
    children: [],
  },
  //系统配置-设备管理-创建设备组-选择设备
  {
    name: "deviceManagementCreateEquipmentSelection",
    path: "/app/systemConfig/deviceManagement/equipmentSelection",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/deviceManagement/equipmentSelection"
      ),
    children: [],
  },
  //系统管理-系统配置-企业文化与驾驶舱
  {
    name: "corporateCulture",
    path: "/systemConfig/corporateCulture",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/corporateCulture"
      ),
    children: [],
  },
  // 企业文化-Logo配置
  {
    name: "corporateCultureLogo",
    path: "/systemConfig/corporateCulture/logo",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/corporateCulture/logo"
      ),
    children: [],
  },
  // 企业文化-Slogan配置
  {
    name: "corporateCultureSlogan",
    path: "/systemConfig/corporateCulture/slogan",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/corporateCulture/slogan"
      ),
    children: [],
  },
  // 企业文化-启动页配置
  {
    name: "corporateCultureStartup",
    path: "/systemConfig/corporateCulture/startup",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/corporateCulture/startup"
      ),
    children: [],
  },
  // 企业文化-付款成功语音播报配置
  {
    name: "corporateCultureVoicePayment",
    path: "/systemConfig/corporateCulture/voice-payment",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/corporateCulture/voice-payment"
      ),
    children: [],
  },
  // 企业文化-输入金额语音播报配置
  {
    name: "corporateCultureVoiceAmount",
    path: "/systemConfig/corporateCulture/voice-amount",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/corporateCulture/voice-amount"
      ),
    children: [],
  },
  // 企业文化-预定获得语音播报配置
  {
    name: "corporateCultureVoiceBooking",
    path: "/systemConfig/corporateCulture/voice-booking",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/corporateCulture/voice-booking"
      ),
    children: [],
  },
  // 企业文化-达到消费次数上限语音播报配置
  {
    name: "corporateCultureVoiceConsumptionCount",
    path: "/systemConfig/corporateCulture/voice-consumption-count",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/corporateCulture/voice-consumption-count"
      ),
    children: [],
  },
  // 企业文化-达到消费金额上限语音播报配置
  {
    name: "corporateCultureVoiceConsumptionAmount",
    path: "/systemConfig/corporateCulture/voice-consumption-amount",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/corporateCulture/voice-consumption-amount"
      ),
    children: [],
  },
  // 企业文化-线上预定成功语音播报配置
  {
    name: "corporateCultureVoiceOnlineSuccess",
    path: "/systemConfig/corporateCulture/voice-online-success",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/corporateCulture/voice-online-success"
      ),
    children: [],
  },
  // 企业文化-余额不足语音播报配置
  {
    name: "corporateCultureVoiceBalance",
    path: "/systemConfig/corporateCulture/voice-balance",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/corporateCulture/voice-balance"
      ),
    children: [],
  },

  // 系统配置 减免规则
  {
    name: "reduceRule",
    path: "/systemConfig/reduceRule",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/reduceRule"
      ),
    children: [],
  },
  // 系统配置减免规则添加
  {
    name: "reduceRuleAdd",
    path: "/systemConfig/reduceRuleAdd",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/reduceRule/crud/add"
      ),
    children: [],
  },
  // 系统配置减免规则详情
  {
    name: "reduceRuleDetail",
    path: "/systemConfig/reduceRuleDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/reduceRule/crud/detail"
      ),
    children: [],
  },
  // 系统配置 考勤规则
  {
    name: "attendanceRule",
    path: "/systemConfig/attendanceRule",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/attendanceRule"
      ),
    children: [],
  },
  // 系统配置考勤规则 新增
  {
    name: "attendanceRuleAdd",
    path: "/systemConfig/attendanceRuleAdd",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/attendanceRule/crud/add"
      ),
    children: [],
  },
  // 系统配置 考勤规则 详情
  {
    name: "attendanceRuleDetail",
    path: "/systemConfig/attendanceRuleDetail",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/systemConfig/attendanceRule/crud/detail"
      ),
    children: [],
  },
  //清空欠款
  {
    name: "clearDebt",
    path: "/clearDebt",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/clearDebt"
      ),
  },
  //清空欠款-选择角色
  {
    name: "clearDebtSelectRole",
    path: "/clearDebt/crud/selectRole",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/clearDebt/crud/selectRole"
      ),
  },
  //清空欠款-选择外部人员
  {
    name: "clearDebtSelectExternalUser",
    path: "/clearDebt/crud/selectExternalUser",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/clearDebt/crud/selectExternalUser"
      ),
  },
  //清空欠款-结果页面
  {
    name: "clearDebtResult",
    path: "/clearDebt/result",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/clearDebt/result"
      ),
  },
  //发放补贴
  {
    name: "provideSubsidies",
    path: "/provideSubsidies",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/provideSubsidies"
      ),
  },
  //扣除补贴
  {
    name: "deductSubsidies",
    path: "/deductSubsidies",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/deductSubsidies"
      ),
  },
  //扣除补贴-选择外部人员
  {
    name: "deductSubsidiesSelectExternalUser",
    path: "/deductSubsidies/crud/selectExternalUser",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/deductSubsidies/crud/selectExternalUser"
      ),
  },
  //扣除补贴-结果页面
  {
    name: "deductSubsidiesResult",
    path: "/deductSubsidies/result",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/deductSubsidies/result"
      ),
  },
  //选择角色
  {
    name: "selectRole",
    path: "/provideSubsidies/crud/selectRole",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/provideSubsidies/crud/selectRole"
      ),
  },
  //选择外部人员
  {
    name: "selectExternalUser",
    path: "/provideSubsidies/crud/selectExternalUser",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/provideSubsidies/crud/selectExternalUser"
      ),
  },
  //发放结果页面
  {
    name: "provideSubsidiesResult",
    path: "/provideSubsidies/result",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/app/finance/provideSubsidies/result"
      ),
  },
  //我的模块转账页面
  {
    name: "transfer",
    path: "/transfer",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/user/crud/transfer"
      ),
  },
  //帮助页面
  {
    name: "help",
    path: "/help",
    meta: { hasTabbar: false },
    component: () =>
      import(/* webpackChunkName: "busManagement" */ "@/views/user/crud/help"),
  },
  //充值
  {
    name: "recharge",
    path: "/recharge",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/user/crud/recharge"
      ),
  },
  //授权信息
  {
    name: "authorize",
    path: "/authorize",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/user/crud/authorize"
      ),
  },
  //付款码
  {
    name: "payment",
    path: "/payment",
    meta: { hasTabbar: false },
    component: () =>
      import(
        /* webpackChunkName: "busManagement" */ "@/views/user/crud/payment"
      ),
  },
  // 500
  {
    name: "500",
    path: "/500",
  },
];

export default routes;
