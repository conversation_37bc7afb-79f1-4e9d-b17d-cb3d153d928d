<template>
  <div class="online-book-wrapper">
    <van-form @submit="onSubmit" class="form-content">
      <!-- 基础配置区域 -->
      <BasicConfigSection
        :formData="formData"
        :disabled="isReadonlyMode"
        @showTypeSelector="showDiningTypeDrawer"
        @showDiningModeSelector="showDiningModeSelector"
      />

      <!-- 预定截止时间配置区域 -->
      <BookingDeadlineSection
        :formData="formData"
        :disabled="isReadonlyMode"
        @showBookingDeadlineSelector="showBookingDeadlineSelector"
        @showUnifiedTimeSelector="showUnifiedTimeSelector"
        @showMealTimeSelector="showMealTimeSelector"

      />

      <!-- 高级配置区域 -->
      <AdvancedConfigSection
        :formData="formData"
        :disabled="isReadonlyMode"
        @showCancelBookingDeadlineSelector="showCancelBookingDeadlineSelector"
        @showCancelUnifiedTimeSelector="showCancelUnifiedTimeSelector"
        @showCancelMealTimeSelector="showCancelMealTimeSelector"
        @showChargeMethodSelector="showChargeMethodSelector"

      />
    </van-form>

    <!-- 固定在底部的按钮组 -->
    <div class="fixed-button-wrapper" v-if="!isReadonlyMode">
      <div class="button-group">
        <!-- 编辑模式：显示清空配置和保存按钮 -->
        <template v-if="isEditMode">
          <van-button
            type="default"
            block
            @click="handleClearConfig"
            class="clear-btn"
            
          >
            清空配置
          </van-button>
          <van-button
            type="primary"
            block
            native-type="submit"
            :loading="uiState.loading"
            @click="handleSave"
            class="save-btn"
          >
            保存
          </van-button>
        </template>

        <!-- 新增模式：只显示保存按钮 -->
        <template v-else>
          <van-button
            type="primary"
            block
            native-type="submit"
            :loading="uiState.loading"
            @click="handleSave"
            class="save-btn"
          >
            保存
          </van-button>
        </template>
      </div>
    </div>

    <!-- 就餐方式多选抽屉 -->
    <DiningTypeDrawer
      :show="showDiningTypeDrawerPopup"
      :options="diningTypeOptions"
      :selectedTypes="tempSelectedDiningTypes"
      @update:show="showDiningTypeDrawerPopup = $event"
      @toggle="toggleDiningType"
      @cancel="cancelDiningTypeSelection"
      @confirm="confirmDiningTypeSelection"
    />

    <!-- 选择器弹窗组件 -->
    <SelectorPopups
      :uiState="uiState"
      :formData="formData"
      :OPTIONS="OPTIONS"
      @diningModeSelect="onDiningModeSelect"
      @bookingDeadlineSelect="onBookingDeadlineSelect"
      @cancelBookingDeadlineSelect="onCancelBookingDeadlineSelect"
      @chargeMethodSelect="onChargeMethodSelect"
      @timeSelectionConfirm="confirmTimeSelection"
      @timeSelectionCancel="cancelTimeSelection"
    />


  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, computed, watch, nextTick, onBeforeUnmount, toRaw, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import { useLoginStore } from '@/store/dingLogin'
import BasicConfigSection from '../components/BasicConfigSection.vue'
import BookingDeadlineSection from '../components/BookingDeadlineSection.vue'
import AdvancedConfigSection from '../components/AdvancedConfigSection.vue'
import DiningTypeDrawer from '../components/DiningTypeDrawer.vue'
import SelectorPopups from '../components/SelectorPopups.vue'

// 获取当前实例
const { proxy } = getCurrentInstance()
const router = useRouter()
const route = useRoute()
const app = useLoginStore()

// ==================== 只读模式检测 ====================
// 检测是否为只读模式（从detail页面直接跳转）
const isReadonlyMode = computed(() => {
  const readonly = route.query.readonly === 'true'
  console.log('onlineBook isReadonlyMode:', readonly, 'route.query:', route.query)
  return readonly
})

// 检测是否为编辑模式（从add页面跳转且有from=detail参数，表示是从detail->add->onlineBook的编辑流程）
const isEditMode = computed(() => {
  const editMode = route.query.from === 'detail' && route.query.readonly !== 'true'
  console.log('onlineBook isEditMode:', editMode, 'route.query:', route.query)
  return editMode
})

// ==================== 缓存功能 ====================
// 缓存键值，基于路由路径和可能的ID参数
const cacheKey = computed(() => {
  const basePath = route.path.replace(/\/\d+$/, '') // 移除末尾的数字ID
  const id = route.params.id || 'new'
  return `onlineBook_${basePath.replace(/\//g, '_')}_${id}`
})

// 缓存工具函数
const cacheUtils = {
  // 保存数据到缓存
  save: (data) => {
    try {
      const cacheData = {
        data: toRaw(data),
        timestamp: Date.now(),
        version: '1.0'
      }
      localStorage.setItem(cacheKey.value, JSON.stringify(cacheData))
    } catch (error) {
      // 缓存保存失败
    }
  },

  // 从缓存加载数据
  load: () => {
    try {
      const cached = localStorage.getItem(cacheKey.value)
      if (cached) {
        const cacheData = JSON.parse(cached)
        return cacheData.data
      }
    } catch (error) {
      // 缓存加载失败
    }
    return null
  },

  clear: () => {
    try {
      localStorage.removeItem(cacheKey.value)
    } catch (error) {
      // 缓存清除失败
    }
  },

  // 检查是否有缓存
  exists: () => {
    return localStorage.getItem(cacheKey.value) !== null
  }
}

// 清理缓存数据中的英文值
const cleanCachedData = (data) => {
  const cleaned = { ...data }

  // 清理核销方式中的英文值
  if (cleaned.verification_methods && Array.isArray(cleaned.verification_methods)) {
    const englishToChinese = {
      'active': '主动',
      'meal_serve': '出餐',
      'meal_end': '闭餐'
    }

    cleaned.verification_methods = cleaned.verification_methods.map(method => {
      return englishToChinese[method] || method
    }).filter((method, index, arr) => arr.indexOf(method) === index) // 去重

    // 确保主动选项存在
    if (!cleaned.verification_methods.includes('主动')) {
      cleaned.verification_methods.unshift('主动')
    }
  }

  return cleaned
}

// ==================== 表单数据 ====================
// 定义默认值对象，用于清空配置时重置
const defaultFormData = {
  // 基础字段 - 直接使用API格式
  dining_methods: ['堂食'], // 直接使用中文，符合API要求
  type_text: '堂食', // 默认显示堂食
  meal_selection_mode: 0, // 选餐模式值，0预设套餐 1自助选餐
  temporary_text: '预设套餐', // 选餐模式显示文本
  preset_menu_options: [], // 预设套餐选项：['multi_booking_enabled', 'proxy_booking_enabled'] 可多选

  // 预定截止时间相关 - 直接使用API数值格式
  booking_deadline_type: 0, // 预定截止时间类型，0开餐时截止 1闭餐时截止 2自定义截止
  booking_deadline_type_text: '开餐时截止', // 预定截止时间显示文本
  booking_deadline_time_type: 0, // 截止时间类型：0统一截止 1分餐截止
  unified_deadline_time: '', // 统一截止时间
  unified_deadline_time_text: '', // 统一截止时间显示文本
  booking_deadline_advance_days: 0, // 提前天数

  // 提前预定上限
  booking_advance_limit_days: 0, // 提前预定上限天数

  // 取消预定截止时间相关 - 直接使用API数值格式
  cancel_deadline_type: 0, // 取消预定截止时间类型，0不可取消 1开餐时截止 2闭餐时截止 3自定义截止
  cancel_booking_deadline_type_text: '不可取消', // 取消预定截止时间显示文本
  cancel_deadline_time_type: 0, // 取消截止时间类型：0统一截止 1分餐截止
  cancel_unified_deadline_time: '', // 取消预定统一截止时间
  cancel_unified_deadline_time_text: '', // 取消预定统一截止时间显示文本
  cancel_deadline_advance_days: 0, // 取消预定提前天数

  // 餐时数据 - 从接口获取
  mealtimes_list: [], // 餐时列表：[{id: 1, title: '早餐'}, {id: 2, title: '午餐'}]

  // 分餐截止时间集合 - 最终提交的数据格式
  mealtime_deadlines: [], // [{mealtime_id: 1, booking_deadline_time: '10:00:00', cancel_deadline_time: '09:00:00'}]

  // 预定时核销 - 直接使用API数值格式
  booking_verification_enabled: 0, // 是否预定时核销：0关闭 1开启
  verification_methods: ['主动'], // 核销方式：主动、出餐、闭餐（中文格式）

  // 扣费方式 - 直接使用API数值格式
  charge_method: 0 // 扣费方式：0核销时扣费 1预定时扣费
}

// 使用默认值初始化formData，并添加其他必要字段
const formData = reactive({
  ...defaultFormData,
  // 扣费方式显示文本
  charge_method_text: '核销时扣费',
  // 其他字段
  specific_open: [],
  specific_open_text: ''
})

// ==================== 缓存初始化 ====================
// 判断是否为新增模式（没有ID参数）
const isAddMode = computed(() => !route.query.id)

// 页面初始化时恢复缓存数据（仅在新增模式且没有id参数时）
const initializeFormData = () => {
  // 只在真正的新增模式下（没有id参数）才恢复表单缓存
  if (!isAddMode.value) {
    console.log('非新增模式，跳过表单缓存恢复')
    return
  }

  const cachedData = cacheUtils.load()
  if (cachedData) {
    // 使用 nextTick 确保在组件完全初始化后再恢复数据
    nextTick(() => {
      // 清理缓存数据中的英文值
      const cleanedData = cleanCachedData(cachedData)
      Object.assign(formData, cleanedData)

      // 同步相关的响应式数据
      if (cleanedData.dining_methods) {
        tempSelectedDiningTypes.value = [...cleanedData.dining_methods]
        selectedDiningTypes.value = [...cleanedData.dining_methods]
      }

      // 显示缓存恢复提示
      showToast({
        message: '已恢复上次填写的内容',
        type: 'success',
        duration: 3000
      })
    })
  }
}

// 防抖定时器
let cacheTimeoutId = null

// 监听表单数据变化，实时保存到缓存（仅在新增模式下）
watch(formData, () => {
  // 只在新增模式下保存表单缓存
  if (isAddMode.value) {
    // 使用防抖，避免频繁保存
    clearTimeout(cacheTimeoutId)
    cacheTimeoutId = setTimeout(() => {
      cacheUtils.save(formData)
    }, 500) // 500ms 防抖
  }
}, { deep: true })

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  clearTimeout(cacheTimeoutId)
})

// 选择器相关
const showPickerPopup = ref(false)
const currentPickerType = ref('')
const currentPickerColumns = ref([])

// 就餐方式抽屉相关
const showDiningTypeDrawerPopup = ref(false)
const selectedDiningTypes = ref(['堂食']) // 默认选中堂食
const tempSelectedDiningTypes = ref(['堂食']) // 临时选中状态，用于取消时恢复

// ==================== 响应式数据 ====================
// 统一的UI状态管理
const uiState = reactive({
  // 弹窗状态
  popups: {
    diningMode: false,
    bookingDeadline: false,
    cancelBookingDeadline: false,
    chargeMethod: false,
    timePicker: false
  },
  // 临时选择值
  temp: {
    diningMode: '',
    bookingDeadline: '', // 修正字段名，与 confirmSelection 中的 type 参数一致
    cancelBookingDeadline: '', // 修正字段名
    chargeMethod: ''
  },
  // 时间选择器
  timePicker: {
    selectedTime: ['12', '00', '00'],
    title: '选择时间',
    currentField: '',
    currentMealIndex: -1
  },

  // 加载状态
  loading: false
})

// 就餐方式选项数据 - value直接使用中文，符合API要求
const diningTypeOptions = [
  { text: '堂食', value: '堂食', disabled: true}, // 堂食锁定不可取消
  { text: '外卖', value: '外卖', disabled: false },
  { text: '打包', value: '打包', disabled: false},
  { text: '自提', value: '自提', disabled: false }
]

// ==================== 选项配置 - value直接使用API要求的数值格式 ====================
const OPTIONS = {
  diningMode: [
    { text: '预设套餐', value: 0 }, // 0预设套餐 1自助选餐
    { text: '自助选餐', value: 1 }
  ],
  bookingDeadline: [
    { text: '开餐时截止', value: 0 }, // 0开餐时截止 1闭餐时截止 2自定义截止
    { text: '闭餐时截止', value: 1 },
    { text: '自定义截止', value: 2 }
  ],
  cancelBookingDeadline: [
    { text: '不可取消', value: 0 }, // 0不可取消 1开餐时截止 2闭餐时截止 3自定义截止
    { text: '开餐时截止', value: 1 },
    { text: '闭餐时截止', value: 2 },
    { text: '自定义截止', value: 3 }
  ],
  chargeMethod: [
    { text: '核销时扣费', value: 0 }, // 0核销时扣费 1预定时扣费
    { text: '预定时扣费', value: 1 }
  ],
  // 预设套餐选项 - value直接使用API字段名
  presetMenuOptions: [
    { text: '预定多份', value: 'multi_booking_enabled' },
    { text: '代预定', value: 'proxy_booking_enabled' }
  ],
  // 截止时间类型选项
  deadlineTimeType: [
    { text: '统一截止', value: 0 }, // 0统一截止 1分餐截止
    { text: '分餐截止', value: 1 }
  ]
}

// ==================== 方法 ====================
// 显示就餐方式抽屉
const showDiningTypeDrawer = () => {
  showDiningTypeDrawerPopup.value = true
  // 确保堂食始终在选中列表中
  const currentTypes = [...formData.dining_methods]
  if (!currentTypes.includes('堂食')) {
    currentTypes.push('堂食')
  }
  tempSelectedDiningTypes.value = currentTypes
}

// 显示选餐模式选择器
const showDiningModeSelector = () => {
  uiState.popups.diningMode = true
  uiState.temp.diningMode = formData.meal_selection_mode
}

// 显示预定截止时间选择器
const showBookingDeadlineSelector = () => {
  uiState.popups.bookingDeadline = true
  uiState.temp.bookingDeadline = formData.booking_deadline_type
}

// 显示取消预定截止时间选择器
const showCancelBookingDeadlineSelector = () => {
  uiState.popups.cancelBookingDeadline = true
  uiState.temp.cancelBookingDeadline = formData.cancel_deadline_type // 修正字段名
}

// 显示扣费方式选择器
const showChargeMethodSelector = () => {
  uiState.popups.chargeMethod = true
  uiState.temp.chargeMethod = formData.charge_method
}

// 通用选择确认方法已删除，改为各选择器单独处理

// 选择器方法
const onDiningModeSelect = (item) => {
  uiState.temp.diningMode = item.value
  // 手动更新显示文本，因为字段名不匹配
  formData.meal_selection_mode = item.value
  formData.temporary_text = item.text
  uiState.popups.diningMode = false
}

const onBookingDeadlineSelect = (item) => {
  uiState.temp.bookingDeadline = item.value
  // 手动更新字段值和显示文本
  formData.booking_deadline_type = item.value
  formData.booking_deadline_type_text = item.text
  uiState.popups.bookingDeadline = false
}

const onCancelBookingDeadlineSelect = (item) => {
  uiState.temp.cancelBookingDeadline = item.value
  // 手动更新字段值和显示文本，因为字段名不匹配
  formData.cancel_deadline_type = item.value
  formData.cancel_booking_deadline_type_text = item.text
  uiState.popups.cancelBookingDeadline = false
}

const onChargeMethodSelect = (item) => {
  uiState.temp.chargeMethod = item.value
  // 手动更新字段值和显示文本
  formData.charge_method = item.value
  formData.charge_method_text = item.text
  uiState.popups.chargeMethod = false
}

// 时间选择器方法
const showUnifiedTimeSelector = () => {
  console.log('🔧 显示统一截止时间选择器，当前值:', formData.unified_deadline_time_text)

  uiState.timePicker.currentField = 'unified_deadline_time'
  uiState.timePicker.title = '选择截止时间'
  const timeParts = formData.unified_deadline_time ? formData.unified_deadline_time.split(':') : ['12', '00', '00']
  uiState.timePicker.selectedTime = timeParts.length === 3 ? timeParts : ['12', '00', '00']
  uiState.popups.timePicker = true
}

const showMealTimeSelector = (mealtimeId, type = 'booking') => {
  const mealtime = formData.mealtimes_list.find(m => m.id === mealtimeId)
  const deadline = formData.mealtime_deadlines.find(d => d.mealtime_id === mealtimeId)

  uiState.timePicker.currentField = type === 'booking' ? 'mealtime_booking' : 'mealtime_cancel'
  uiState.timePicker.currentMealIndex = mealtimeId
  uiState.timePicker.title = `选择${mealtime?.title || ''}${type === 'booking' ? '预定' : '取消'}截止时间`

  // 正确处理：根据类型使用对应的字段
  const currentTime = type === 'booking' ? deadline?.booking_deadline_time : deadline?.cancel_deadline_time
  const timeParts = currentTime ? currentTime.split(':') : ['12', '00', '00']
  uiState.timePicker.selectedTime = timeParts.length === 3 ? timeParts : ['12', '00', '00']

  uiState.popups.timePicker = true
}

// 取消预定餐时时间选择器
const showCancelMealTimeSelector = (mealtimeId, type) => {
  showMealTimeSelector(mealtimeId, type)
}

const showCancelUnifiedTimeSelector = () => {
  uiState.timePicker.currentField = 'cancel_unified_deadline_time'
  uiState.timePicker.title = '选择取消预定截止时间'
  const timeParts = formData.cancel_unified_deadline_time ? formData.cancel_unified_deadline_time.split(':') : ['12', '00', '00']
  uiState.timePicker.selectedTime = timeParts.length === 3 ? timeParts : ['12', '00', '00']
  uiState.popups.timePicker = true
}

const cancelTimeSelection = () => {
  uiState.popups.timePicker = false
  uiState.timePicker.currentField = ''
  uiState.timePicker.currentMealIndex = -1
}

const confirmTimeSelection = () => {
  const timeString = uiState.timePicker.selectedTime.join(':')
  const field = uiState.timePicker.currentField
  const mealIndex = uiState.timePicker.currentMealIndex

  console.log('🔧 时间选择确认:', { timeString, field, mealIndex })

  // 时间字段映射
  const timeFieldMap = {
    'unified_deadline_time': () => {
      // 🔧 关键修复：使用Vue.set或直接赋值确保响应式更新
      formData.unified_deadline_time = timeString
      formData.unified_deadline_time_text = timeString

      // 🔧 额外保险：强制触发响应式更新
      nextTick(() => {
        console.log('🔧 统一截止时间已设置:', timeString, '显示文本:', formData.unified_deadline_time_text)
      })
    },
    'mealtime_booking': () => {
      if (mealIndex) {
        let deadline = formData.mealtime_deadlines.find(d => d.mealtime_id === mealIndex)
        if (!deadline) {
          // 🔧 关键修复：如果找不到deadline对象，创建一个新的
          console.log('🔧 未找到对应的deadline，为餐时ID', mealIndex, '创建新的deadline对象')
          deadline = {
            mealtime_id: mealIndex,
            booking_deadline_time: '',
            cancel_deadline_time: ''
          }
          formData.mealtime_deadlines.push(deadline)
        }
        deadline.booking_deadline_time = timeString
        console.log('🔧 餐时预定截止时间已设置:', timeString, '餐时ID:', mealIndex)
      }
    },
    'cancel_unified_deadline_time': () => {
      formData.cancel_unified_deadline_time = timeString
      formData.cancel_unified_deadline_time_text = timeString
      console.log('🔧 取消预定统一截止时间已设置:', timeString)
    },
    'mealtime_cancel': () => {
      if (mealIndex) {
        let deadline = formData.mealtime_deadlines.find(d => d.mealtime_id === mealIndex)
        if (!deadline) {
          // 🔧 关键修复：如果找不到deadline对象，创建一个新的
          console.log('🔧 未找到对应的deadline，为餐时ID', mealIndex, '创建新的deadline对象')
          deadline = {
            mealtime_id: mealIndex,
            booking_deadline_time: '',
            cancel_deadline_time: ''
          }
          formData.mealtime_deadlines.push(deadline)
        }
        deadline.cancel_deadline_time = timeString
        console.log('🔧 餐时取消截止时间已设置:', timeString, '餐时ID:', mealIndex)
      }
    }
  }

  const handler = timeFieldMap[field]
  if (handler) {
    handler()
  } else {
    console.log('🔧 未找到对应的处理函数，字段:', field)
  }

  cancelTimeSelection()
}


// 就餐方式相关方法
const toggleDiningType = (option) => {
  if (option.disabled) return // 堂食不能取消选中

  const index = tempSelectedDiningTypes.value.indexOf(option.value)
  if (index > -1) {
    tempSelectedDiningTypes.value.splice(index, 1)
  } else {
    tempSelectedDiningTypes.value.push(option.value)
  }
}

const cancelDiningTypeSelection = () => {
  // 恢复到原始状态，确保堂食始终在选中列表中
  const originalTypes = [...formData.dining_methods]
  if (!originalTypes.includes('堂食')) {
    originalTypes.push('堂食')
  }
  tempSelectedDiningTypes.value = originalTypes
  showDiningTypeDrawerPopup.value = false
}

const confirmDiningTypeSelection = () => {
  // 确保堂食始终在最终选择中
  const finalTypes = [...tempSelectedDiningTypes.value]
  if (!finalTypes.includes('堂食')) {
    finalTypes.push('堂食')
  }
  formData.dining_methods = finalTypes

  // 更新显示文本
  const selectedTexts = diningTypeOptions
    .filter(opt => finalTypes.includes(opt.value))
    .map(opt => opt.text)
  formData.type_text = selectedTexts.join('；')

  showDiningTypeDrawerPopup.value = false
}

// 表单处理方法
const onSubmit = async (event) => {
  event?.preventDefault()
  if (!validateFormData()) return

  try {
    uiState.loading = true

    const apiData = transformFormDataForAPI(formData)

    // 这里应该是实际的API调用
    // const response = await proxy.$post('/api/consume-rule', apiData)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 提交成功后清除缓存
    if (isAddMode.value) {
      cacheUtils.clear()
    }

    showToast({
      message: '提交成功',
      type: 'success',
      duration: 2000
    })

    // 延迟返回上一页
    // setTimeout(() => {
    //   router.go(-1)
    // }, 1000)

  } catch (error) {
    showToast({
      message: '提交失败，请重试',
      type: 'fail'
    })
  } finally {
    uiState.loading = false
  }
}

const handleSave = async () => {
  try {
    uiState.loading = true

    // 获取当前表单的最新数据（确保是最新的用户输入）
    const currentFormData = toRaw(formData)
    console.log('onlineBook页面当前表单数据:', currentFormData)

    // 转换为API格式
    const apiData = transformFormDataForAPI(currentFormData)
    console.log('onlineBook页面转换后的API数据:', apiData)

    // 过滤空值数据，只传递有效字段
    const filteredApiData = filterEmptyValues(apiData)
    console.log('onlineBook页面过滤后的数据:', filteredApiData)

    // 清除之前的传递数据，确保使用最新数据
    localStorage.removeItem('consumeRuleFormData')

    // 将过滤后的数据传递给add页面
    localStorage.setItem('consumeRuleFormData', JSON.stringify(filteredApiData))

    // 保存原始表单数据到缓存（用于页面恢复）
    cacheUtils.save(currentFormData)

    // 如果有id参数（编辑模式），更新详情数据缓存
    if (route.query.id) {
      const detailCacheKey = `consumeRule_detail_${route.query.id}`
      // 将当前表单数据转换为API格式并缓存，确保下次访问时使用最新数据
      const apiFormatData = transformFormDataToApiFormat(currentFormData)
      localStorage.setItem(detailCacheKey, JSON.stringify(apiFormatData))
      console.log('onlineBook页面已更新详情数据缓存，id:', route.query.id)
    }

    showToast({
      message: '保存成功',
      type: 'success',
      duration: 2000
    })

    // 返回上一页
    setTimeout(() => {
      router.go(-1)
    }, 1000)
  } catch (error) {
    console.error('onlineBook页面保存失败:', error)
    showToast({
      message: '保存失败，请重试',
      type: 'fail'
    })
  } finally {
    uiState.loading = false
  }
}

// ==================== 清空配置处理函数 ====================
const handleClearConfig = () => {
  // 重置表单数据为默认值
  Object.keys(defaultFormData).forEach(key => {
    if (key === 'mealtimes_list') {
      // 保留餐时列表数据，不清空
      return
    }
    formData[key] = Array.isArray(defaultFormData[key])
      ? [...defaultFormData[key]]
      : defaultFormData[key]
  })

  // 重置其他相关字段
  formData.charge_method_text = '核销时扣费'
  formData.specific_open = []
  formData.specific_open_text = ''

  // 重置UI状态
  selectedDiningTypes.value = ['堂食']
  tempSelectedDiningTypes.value = ['堂食']

  // 重新初始化分餐截止时间数据
  initializeMealtimeDeadlines()

  showToast({
    message: '配置已清空',
    type: 'success',
    duration: 1500
  })
}

// ==================== 数据初始化处理 ====================
// 根据路由参数获取详情数据并初始化表单（带缓存机制）
const initializeFromDetailData = async () => {
  // 检查是否有id参数（从detail页面或add页面跳转）
  const id = route.query.id
  if (!id) return

  // 生成详情数据的缓存键
  const detailCacheKey = `consumeRule_detail_${id}`
  console.log('onlineBook页面检查缓存，缓存键:', detailCacheKey)

  try {
    // 先检查是否有缓存的详情数据
    const cachedDetailData = localStorage.getItem(detailCacheKey)
    console.log('onlineBook页面缓存检查结果:', cachedDetailData ? '有缓存' : '无缓存')

    if (cachedDetailData) {
      console.log('onlineBook页面使用缓存的详情数据，id:', id)
      const apiData = JSON.parse(cachedDetailData)
      console.log('onlineBook页面缓存详情数据:', apiData)

      // 将缓存的API数据转换并填充到表单
      await transformAndFillFormData(apiData)

      console.log('onlineBook页面缓存数据填充完成')
      return
    }

    // 没有缓存时，调用接口获取数据（只在第一次访问时调用）
    console.log('onlineBook页面开始获取详情数据，id:', id)
    const response = await proxy.$get('/consumption_rule/get_info', { id })

    if (response && response.code === 200 && response.data) {
      const apiData = response.data
      console.log('onlineBook页面获取到详情数据:', apiData)

      // 缓存详情数据，确保后续访问使用缓存
      localStorage.setItem(detailCacheKey, JSON.stringify(apiData))
      console.log('onlineBook页面详情数据已缓存，缓存键:', detailCacheKey)

      // 将API数据转换并填充到表单
      await transformAndFillFormData(apiData)

      console.log('onlineBook页面数据填充完成')
    } else {
      showToast({
        message: '获取数据失败',
        type: 'fail'
      })
    }
  } catch (error) {
    console.error('onlineBook页面获取详情数据失败:', error)
    showToast({
      message: '获取数据失败，请重试',
      type: 'fail'
    })
  }
}

// 将API数据转换并填充到表单
const transformAndFillFormData = async (apiData) => {
  // 基础字段映射
  if (apiData.dining_methods) {
    const methods = typeof apiData.dining_methods === 'string'
      ? apiData.dining_methods.split(',')
      : Array.isArray(apiData.dining_methods)
        ? apiData.dining_methods
        : [apiData.dining_methods]

    formData.dining_methods = methods
    formData.type_text = methods.join('；')
    selectedDiningTypes.value = [...methods]
    tempSelectedDiningTypes.value = [...methods]
  }

  // 选餐模式
  if (apiData.meal_selection_mode !== undefined) {
    formData.meal_selection_mode = apiData.meal_selection_mode
    formData.temporary_text = apiData.meal_selection_mode === 0 ? '预设套餐' : '自助选餐'
  }

  // 预设套餐选项
  const presetOptions = []
  if (apiData.multi_booking_enabled === 1) presetOptions.push('multi_booking_enabled')
  if (apiData.proxy_booking_enabled === 1) presetOptions.push('proxy_booking_enabled')
  formData.preset_menu_options = presetOptions

  // 预定截止时间相关
  if (apiData.booking_deadline_type !== undefined) {
    formData.booking_deadline_type = apiData.booking_deadline_type
    const deadlineTexts = ['开餐时截止', '闭餐时截止', '自定义截止']
    formData.booking_deadline_type_text = deadlineTexts[apiData.booking_deadline_type] || '开餐时截止'
  }

  if (apiData.booking_deadline_time_type !== undefined) {
    formData.booking_deadline_time_type = apiData.booking_deadline_time_type
  }

  if (apiData.unified_deadline_time) {
    formData.unified_deadline_time = apiData.unified_deadline_time
    formData.unified_deadline_time_text = apiData.unified_deadline_time
  }

  if (apiData.booking_deadline_advance_days !== undefined) {
    formData.booking_deadline_advance_days = apiData.booking_deadline_advance_days
  }

  // 提前预定上限
  if (apiData.booking_advance_limit_days !== undefined) {
    formData.booking_advance_limit_days = apiData.booking_advance_limit_days
  }

  // 取消预定截止时间相关
  if (apiData.cancel_deadline_type !== undefined) {
    formData.cancel_deadline_type = apiData.cancel_deadline_type
    const cancelTexts = ['不可取消', '开餐时截止', '闭餐时截止', '自定义截止']
    formData.cancel_booking_deadline_type_text = cancelTexts[apiData.cancel_deadline_type] || '不可取消'
  }

  if (apiData.cancel_deadline_time_type !== undefined) {
    formData.cancel_deadline_time_type = apiData.cancel_deadline_time_type
  }

  if (apiData.cancel_unified_deadline_time) {
    formData.cancel_unified_deadline_time = apiData.cancel_unified_deadline_time
    formData.cancel_unified_deadline_time_text = apiData.cancel_unified_deadline_time
  }

  if (apiData.cancel_deadline_advance_days !== undefined) {
    formData.cancel_deadline_advance_days = apiData.cancel_deadline_advance_days
  }

  // 分餐截止时间数据
  if (apiData.mealtime_deadlines && Array.isArray(apiData.mealtime_deadlines)) {
    formData.mealtime_deadlines = apiData.mealtime_deadlines
  }

  // 预定时核销
  if (apiData.booking_verification_enabled !== undefined) {
    formData.booking_verification_enabled = apiData.booking_verification_enabled
  }

  if (apiData.verification_methods) {
    const methods = typeof apiData.verification_methods === 'string'
      ? apiData.verification_methods.split(',')
      : Array.isArray(apiData.verification_methods)
        ? apiData.verification_methods
        : [apiData.verification_methods]
    formData.verification_methods = methods
  }

  // 扣费方式
  if (apiData.charge_method !== undefined) {
    formData.charge_method = apiData.charge_method
    const chargeTexts = ['核销时扣费', '预定时扣费']
    formData.charge_method_text = chargeTexts[apiData.charge_method] || '核销时扣费'
  }
}

// 将表单数据转换为API格式（用于缓存更新）
const transformFormDataToApiFormat = (formData) => {
  const apiData = { ...formData }

  // 将数组格式的dining_methods转换为逗号分隔的字符串
  if (apiData.dining_methods && Array.isArray(apiData.dining_methods)) {
    apiData.dining_methods = apiData.dining_methods.join(',')
  }

  // 将数组格式的verification_methods转换为逗号分隔的字符串
  if (apiData.verification_methods && Array.isArray(apiData.verification_methods)) {
    apiData.verification_methods = apiData.verification_methods.join(',')
  }

  // 处理预设套餐选项
  if (apiData.preset_menu_options && Array.isArray(apiData.preset_menu_options)) {
    apiData.multi_booking_enabled = apiData.preset_menu_options.includes('multi_booking_enabled') ? 1 : 0
    apiData.proxy_booking_enabled = apiData.preset_menu_options.includes('proxy_booking_enabled') ? 1 : 0
  }

  return apiData
}

// 数据过滤工具函数 - 过滤空值数据
const filterEmptyValues = (obj) => {
  const filtered = {}

  // 🔧 关键修复：线上预定相关字段即使为0也应该保留
  const onlineBookingFields = [
    'online_booking_enabled', 'meal_selection_mode', 'booking_deadline_type',
    'booking_deadline_time_type', 'cancel_deadline_type', 'cancel_deadline_time_type',
    'charge_method', 'booking_verification_enabled', 'multi_booking_enabled',
    'proxy_booking_enabled', 'booking_advance_limit_days', 'booking_deadline_advance_days',
    'cancel_deadline_advance_days'
  ]

  for (const [key, value] of Object.entries(obj)) {
    // 🔧 修复过滤条件：线上预定字段即使为0也保留，其他字段按原逻辑过滤
    if (onlineBookingFields.includes(key)) {
      // 线上预定字段：只要不是undefined、null就保留（包括0、空字符串）
      if (value !== undefined && value !== null) {
        filtered[key] = value
      }
    } else {
      // 其他字段：按原逻辑过滤（不是undefined、null、空字符串、空数组）
      if (value !== undefined &&
          value !== null &&
          value !== '' &&
          !(Array.isArray(value) && value.length === 0)) {
        filtered[key] = value
      }
    }
  }

  return filtered
}

// 数据转换方法 - 简化版，大部分字段已经是API格式
const transformFormDataForAPI = (data) => {
  const apiData = {}

  // 直接复制的字段（已经是API格式）- 移除mealtime_deadlines，需要条件处理
  const directFields = [
    'title', 'expire_time', 'internal_staffs', 'external_staffs', 'mealtimes', 'stalls',
    'meal_selection_mode', 'booking_deadline_type', 'booking_deadline_time_type',
    'cancel_deadline_type', 'cancel_deadline_time_type', 'charge_method',
    'booking_verification_enabled'
  ]

  directFields.forEach(field => {
    if (data[field] !== undefined) apiData[field] = data[field]
  })

  if ((data.booking_deadline_time_type === 1 || data.cancel_deadline_time_type === 1) && data.mealtime_deadlines !== undefined) {
    // 清理mealtime_deadlines数据：只传递有数据的字段
    apiData.mealtime_deadlines = data.mealtime_deadlines.map(deadline => {
      const cleanedDeadline = {
        mealtime_id: deadline.mealtime_id // 餐时ID必需字段
      }

      // 预定截止时间：有数据才传递
      if (deadline.booking_deadline_time && deadline.booking_deadline_time.trim() !== '') {
        cleanedDeadline.booking_deadline_time = deadline.booking_deadline_time
      }

      // 取消截止时间：有数据才传递
      if (deadline.cancel_deadline_time && deadline.cancel_deadline_time.trim() !== '') {
        cleanedDeadline.cancel_deadline_time = deadline.cancel_deadline_time
      }

      return cleanedDeadline
    }).filter(deadline => {
      // 过滤掉只有mealtime_id而没有时间数据的项
      return Object.keys(deadline).length > 1
    })
  }

  if (data.booking_deadline_time_type === 0 && data.unified_deadline_time !== undefined) {
    apiData.unified_deadline_time = data.unified_deadline_time
  }

  if (data.cancel_deadline_time_type === 0 && data.cancel_unified_deadline_time !== undefined) {
    apiData.cancel_unified_deadline_time = data.cancel_unified_deadline_time
  }

  // 布尔值转0/1的字段 - 移除online_booking_enabled，需要智能处理
  const booleanFields = ['is_temporary', 'amount_limit_enabled', 'enjoy_first_pay_later_enabled']
  booleanFields.forEach(field => {
    if (data[field] !== undefined) apiData[field] = data[field] ? 1 : 0
  })

  const hasOnlineBookingData = (
    (data.meal_selection_mode !== undefined) ||
    (data.booking_deadline_type !== undefined) ||
    (data.booking_deadline_time_type !== undefined) ||
    (data.cancel_deadline_type !== undefined) ||
    (data.cancel_deadline_time_type !== undefined) ||
    (data.charge_method !== undefined) ||
    (data.booking_verification_enabled !== undefined) ||
    (data.dining_methods && data.dining_methods !== '') ||
    (data.verification_methods && data.verification_methods !== '') ||
    (data.mealtime_deadlines && Array.isArray(data.mealtime_deadlines)) ||
    (data.unified_deadline_time !== undefined) ||
    (data.cancel_unified_deadline_time !== undefined) ||
    (data.preset_menu_options !== undefined) ||
    (data.booking_advance_limit_days !== undefined) ||
    (data.booking_deadline_advance_days !== undefined) ||
    (data.cancel_deadline_advance_days !== undefined)
  )

  const isFromOnlineBookPage = (
    Object.keys(data).some(key => [
      'meal_selection_mode', 'booking_deadline_type', 'booking_deadline_time_type',
      'cancel_deadline_type', 'cancel_deadline_time_type', 'charge_method',
      'booking_verification_enabled', 'dining_methods', 'verification_methods',
      'mealtime_deadlines', 'unified_deadline_time', 'cancel_unified_deadline_time',
      'preset_menu_options', 'booking_advance_limit_days', 'booking_deadline_advance_days',
      'cancel_deadline_advance_days', 'multi_booking_enabled', 'proxy_booking_enabled'
    ].includes(key))
  )

  if (hasOnlineBookingData || isFromOnlineBookPage) {
    apiData.online_booking_enabled = 1
  } else {
    apiData.online_booking_enabled = 0
  }

  // 数值转换的字段 - 移除需要条件处理的字段
  const numberFields = [
    'apply_personnel_type', 'apply_mealtime_type', 'apply_stall_type',
    'booking_advance_limit_days', 'attendance_rule_id', 'discount_rule_id', 'consumption_times_per_meal',
    'amount_limit', 'amount_limit_scope', 'amount_limit_period', 'single_consumption_markup'
  ]
  numberFields.forEach(field => {
    if (data[field] !== undefined) apiData[field] = Number(data[field])
  })

  if (data.booking_deadline_type === 2 && data.booking_deadline_advance_days !== undefined) {
    apiData.booking_deadline_advance_days = Number(data.booking_deadline_advance_days)
  }

  if (data.cancel_deadline_type === 3 && data.cancel_deadline_advance_days !== undefined) {
    apiData.cancel_deadline_advance_days = Number(data.cancel_deadline_advance_days)
  }

  // 数组转逗号分隔字符串的字段
  if (data.dining_methods && Array.isArray(data.dining_methods)) {
    apiData.dining_methods = data.dining_methods.join(',')
  }

  if (data.verification_methods && Array.isArray(data.verification_methods)) {
    apiData.verification_methods = data.verification_methods.join(',')
  }

  // 预设套餐选项特殊处理
  if (data.preset_menu_options && Array.isArray(data.preset_menu_options)) {
    apiData.multi_booking_enabled = data.preset_menu_options.includes('multi_booking_enabled') ? 1 : 0
    apiData.proxy_booking_enabled = data.preset_menu_options.includes('proxy_booking_enabled') ? 1 : 0
  } else {
    apiData.multi_booking_enabled = 0
    apiData.proxy_booking_enabled = 0
  }

  const filteredApiData = filterEmptyValues(apiData)
  return filteredApiData
}

// 工具方法
const validateFormData = () => {
  if (!formData.type_text) { showToast('请选择就餐方式'); return false }
  if (!formData.temporary_text) { showToast('请选择选餐模式'); return false }
  return true
}

// ==================== API调用方法 ====================
// 获取餐时数据
const fetchMealtimes = async () => {
  try {
    const dininghall_id = localStorage.getItem('dininghall')
    if (!dininghall_id) {
      return
    }

    const response = await proxy.$get('/mealtime/get_all', { dininghall_id })
    if (response && response.code === 200 && response.data) {
      formData.mealtimes_list = response.data.map(item => ({
        id: item.id,
        title: item.title
      }))

      initializeMealtimeDeadlines()
    }
  } catch (error) {
    // 获取餐时数据异常
  }
}

// 初始化分餐截止时间数据
const initializeMealtimeDeadlines = () => {
  if (formData.mealtimes_list.length > 0) {
    console.log('🔧 初始化餐时截止时间数据')
    console.log('🔧 当前餐时列表:', formData.mealtimes_list)
    console.log('🔧 当前deadline数组:', formData.mealtime_deadlines)

    // 🔧 关键修复：确保每个餐时都有对应的deadline对象
    formData.mealtimes_list.forEach(mealtime => {
      const existingDeadline = formData.mealtime_deadlines.find(d => d.mealtime_id === mealtime.id)
      if (!existingDeadline) {
        // 如果不存在，则添加新的deadline对象
        formData.mealtime_deadlines.push({
          mealtime_id: mealtime.id,
          booking_deadline_time: '',
          cancel_deadline_time: ''
        })
        console.log('🔧 为餐时ID', mealtime.id, '添加了新的deadline对象')
      }
    })

    console.log('🔧 初始化后的deadline数组:', formData.mealtime_deadlines)
  }
}

// ==================== 监听器 ====================
// 确保核销方式中始终包含"主动"选项，并清理英文值
watch(() => formData.verification_methods, (newMethods) => {
  if (Array.isArray(newMethods)) {
    // 清理英文值
    const englishToChinese = {
      'active': '主动',
      'meal_serve': '出餐',
      'meal_end': '闭餐'
    }

    // 转换英文值为中文
    const cleanedMethods = newMethods.map(method => {
      return englishToChinese[method] || method
    }).filter((method, index, arr) => arr.indexOf(method) === index) // 去重

    // 确保主动选项存在
    if (!cleanedMethods.includes('主动')) {
      cleanedMethods.unshift('主动')
    }

    // 如果数组发生了变化，更新formData
    if (JSON.stringify(cleanedMethods) !== JSON.stringify(newMethods)) {
      formData.verification_methods = cleanedMethods
    }
  }
}, { immediate: true, deep: true })

// ==================== 页面初始化 ====================
// 页面加载时的初始化逻辑
onMounted(async () => {
  console.log('onlineBook页面开始初始化，route.query:', route.query)

  // 1. 首先获取餐时数据
  await fetchMealtimes()

  // 2. 检查是否有id参数，如果有则获取详情数据
  if (route.query.id) {
    console.log('onlineBook页面检测到id参数，开始获取详情数据')
    await initializeFromDetailData()
  } else {
    console.log('onlineBook页面没有id参数，初始化表单数据')
    // 3. 没有id参数时，初始化表单数据（包括缓存恢复）
    initializeFormData()
  }

  console.log('onlineBook页面初始化完成')
})
const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: '线上预订',
  });
};
setRightA()
setRight()

</script>

<style lang="scss" scoped>
// 导入通用样式
@import '../components/common-styles.scss';

// ==================== 页面布局样式 ====================
.online-book-wrapper {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px; // 为固定按钮留出空间
}

.form-content {
  padding: 16px;

  // 确保组件间有适当间距
  > section {
    margin-bottom: 0;

    &:not(:last-child) {
      margin-bottom: 12px;
    }
  }
}

// ==================== 固定按钮样式 ====================
.fixed-button-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: #fff;
  border-radius: 12px;
  // border-top: 1px solid #ebedf0;
  z-index: 100;

  .button-group {
    display: flex;
    gap: 12px;
    align-items: center;
    
    .clear-draft-btn {
      flex-shrink: 0;
      min-width: 80px;
      height: 44px;
      // border-color: #dcdee0;
      color: #646566;

      &:active {
        background-color: #f2f3f5;
      }
    }
    .clear-btn { 
      flex: 1;
    }
    .save-btn {
      flex: 1;
      height: 44px;
    }
  }
}


</style>
