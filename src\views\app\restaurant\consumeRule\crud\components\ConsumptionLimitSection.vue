<template>
  <div class="consumption-limit-section">
    <!-- 每餐消费次数 -->
    <div class="form-group">
      <van-cell-group inset>
        <van-field label="每餐消费次数" required>
          <template #input>
            <van-stepper 
              v-model="localFormData.consumption_times_per_meal" 
              :disabled="loading"
              :min="1"
            />
          </template>
        </van-field>
      </van-cell-group>
    </div>

    <!-- 消费金额上限 -->
    <div class="form-group">
      <van-cell-group inset>
        <van-cell title="消费金额上限" />
        <van-radio-group v-model="localFormData.amount_limit_enabled" :disabled="loading">
          <van-cell clickable @click="localFormData.amount_limit_enabled = 0">
            <template #title>
              <van-radio :name="0">无上限</van-radio>
            </template>
          </van-cell>
          <van-cell clickable @click="localFormData.amount_limit_enabled = 1">
            <template #title>
              <van-radio :name="1">自定义上限</van-radio>
            </template>
          </van-cell>
        </van-radio-group>
        
        <!-- 自定义上限时显示 -->
        <template v-if="localFormData.amount_limit_enabled === 1">
          <van-field
            v-model="localFormData.amount_limit"
            label="上限金额"
            placeholder="请输入限额金额"
            type="number"
            required
            :disabled="loading"
            :formatter="formatAmount"
            @blur="handleAmountBlur"
          >
            <template #right-icon>
              <span class="amount-unit">元</span>
            </template>
          </van-field>
          
          <van-cell title="统计范围" />
          <van-radio-group v-model="localFormData.amount_limit_scope" :disabled="loading">
            <van-cell clickable @click="localFormData.amount_limit_scope = 0">
              <template #title>
                <van-radio :name="0">当前餐厅</van-radio>
              </template>
            </van-cell>
            <van-cell clickable @click="localFormData.amount_limit_scope = 1">
              <template #title>
                <van-radio :name="1">全部场所</van-radio>
              </template>
            </van-cell>
          </van-radio-group>
          
          <van-field
            v-model="periodText"
            label="统计周期"
            placeholder="请选择统计周期"
            readonly
            :disabled="loading"
            @click="showPeriodPicker = true"
          />
        </template>
      </van-cell-group>
    </div>

    <!-- 单次消费加价 -->
    <div class="form-group">
      <van-cell-group inset>
        <van-field
          v-model="localFormData.single_consumption_markup"
          label="单次消费加价"
          placeholder="请输入加价金额"
          type="number"
          required
          :disabled="loading"
          :formatter="formatAmount"
          @blur="handleMarkupBlur"
        >
          <template #right-icon>
            <span class="amount-unit">元</span>
          </template>
        </van-field>
        <van-cell>
          <template #title>
            <span class="desc-text">例：填1元，每次消费加价1元</span>
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 统计周期选择器 -->
    <van-popup v-model:show="showPeriodPicker" position="bottom" :style="{ height: '50vh' }">
      <van-picker
        :columns="periodColumns"
        title="选择统计周期"
        @confirm="handlePeriodConfirm"
        @cancel="showPeriodPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:formData'])

// 本地表单数据
const localFormData = computed({
  get: () => props.formData,
  set: (value) => emit('update:formData', value)
})

// 选择器状态
const showPeriodPicker = ref(false)

// 统计周期选项
const periodColumns = ref([
  { text: '每餐', value: 0 },
  { text: '每天', value: 1 },
  { text: '每周', value: 2 },
  { text: '每月', value: 3 }
])

// 显示文本
const periodText = computed(() => {
  const period = periodColumns.value.find(item => item.value === localFormData.value.amount_limit_period)
  return period ? period.text : '每周'
})

// 格式化金额（保留两位小数）
const formatAmount = (value) => {
  if (!value) return ''
  const num = parseFloat(value)
  return isNaN(num) ? '' : num.toFixed(2)
}

// 处理金额失焦
const handleAmountBlur = (event) => {
  const value = event.target.value
  if (value) {
    const num = parseFloat(value)
    if (!isNaN(num) && num >= 1) {
      localFormData.value.amount_limit = num.toFixed(2)
    } else {
      localFormData.value.amount_limit = '1.00'
    }
  }
}

// 处理加价失焦
const handleMarkupBlur = (event) => {
  const value = event.target.value
  if (value) {
    const num = parseFloat(value)
    if (!isNaN(num) && num >= 0) {
      localFormData.value.single_consumption_markup = num.toFixed(2)
    } else {
      localFormData.value.single_consumption_markup = '0.00'
    }
  }
}

// 处理统计周期确认
const handlePeriodConfirm = ({ selectedOptions }) => {
  const selected = selectedOptions[0]
  localFormData.value.amount_limit_period = selected.value
  showPeriodPicker.value = false
}
</script>

<style lang="scss" scoped>
.consumption-limit-section {
  .form-group {
    margin-bottom: 12px;
  }
  
  .van-cell-group {
    margin: 0 16px;
  }
  
  .amount-unit {
    color: #969799;
    font-size: 14px;
  }
  
  .desc-text {
    color: #969799;
    font-size: 12px;
  }
}
</style>
