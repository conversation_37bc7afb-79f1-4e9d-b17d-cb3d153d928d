<template>
    <van-form @submit="submit">
        <div class="content">
            <van-cell-group inset class="item">
                <!-- 扣除人员 -->
                <div class="distribute-section">
                    <div class="distribute-content">
                        <div class="section-title">扣除人员</div>
                        <div class="radio-group">
                            <van-radio-group v-model="form.personnel_type" direction="horizontal">
                                <van-radio :name="1" class="radio-item">全部人员</van-radio>
                                <van-radio :name="2" class="radio-item">指定人员</van-radio>
                            </van-radio-group>
                        </div>
                    </div>
                </div>

                <!-- 人员/角色选择 -->
                <template v-if="form.personnel_type == 2">
                    <div class="type-section">
                        <div class="type-content">
                            <div class="section-title">人员/角色</div>
                            <div class="type-buttons">
                                <div :class="['type-btn', { 'active': data.type === 0 }]" @click="switchType(0)">
                                    人员
                                </div>
                                <!-- <div :class="['type-btn', { 'active': data.type === 1 }]" @click="switchType(1)">
                                    角色
                                </div> -->
                            </div>
                        </div>
                    </div>

                    <!-- 内部员工 -->
                    <van-cell title="内部员工" :value="getInternalStaffText()" is-link @click="handleInternalStaff"
                        class="staff-cell" />

                    <!-- 外部人员 -->
                    <van-cell title="外部人员" :value="getExternalStaffText()" is-link @click="handleExternalStaff"
                        class="staff-cell" />
                </template>
            </van-cell-group>

            <!-- 扣除类型和金额卡片 -->
            <van-cell-group inset class="item">
                <!-- 扣除类型 -->
                <van-cell title="扣除类型" :value="getDeductTypeText()" is-link @click="showDeductTypeSelector"
                    class="deduct-type-cell" />
                <!-- 扣除金额 - 只在定期扣除时显示 -->
                <div class="amount-section" v-if="form.deduct_type === 1">
                    <div class="section-title">
                        <span class="required-mark">*</span>
                        扣除金额
                    </div>
                    <div class="amount-input">
                        <span class="currency">¥</span>
                        <input v-model="form.amount" type="number" placeholder="100" class="amount-field" required />
                        <van-icon name="cross" class="clear-icon" @click="cleanMoney" v-if="form.amount" />
                    </div>
                </div>
            </van-cell-group>

            <!-- 备注 -->
            <van-cell-group inset :style="{ marginTop: '16px' }">
                <div class="remark-section">
                    <div class="section-title">备注</div>
                    <van-field v-model="form.remark" placeholder="请输入内容" type="textarea" rows="3" maxlength="50"
                        show-word-limit class="remark-field" />
                </div>
            </van-cell-group>
            <div class="sec-title">手工处理记录</div>
            <van-cell-group inset>
                <template v-if="list.length != 0">
                    <template v-for="(el, i) in list" :key="i">
                        <div class="card">
                            <div class="left">
                                <div class="icon"><van-icon name="points" /></div>
                                <div class="desc">
                                    <div class="l1">账户充值</div>
                                    <div class="l2">{{ el.remark }}</div>
                                    <div class="l3">{{ el.created_at }}</div>
                                </div>
                            </div>
                            <div class="right">
                                <div class="num">+{{ el.amount }}</div>
                                <div class="tips">操作成功</div>
                            </div>
                        </div>
                    </template>
                </template>
                <template v-else-if="list.length == 0 && showdescription">
                    <van-empty
                        image="http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/huashan/2023-07-06/ySOHjSamw6AqowTAw6a6vpPY3TmJnBsa.png"
                        image-size="250" :description="description" />
                </template>
            </van-cell-group>
        </div>
        <van-action-bar class="bar">
            <van-button class="submit" type="primary" native-type="submit">
                提交
            </van-button>
        </van-action-bar>
    </van-form>

    <!-- 扣除类型选择器 -->
    <van-popup v-model:show="showDeductTypePicker" position="bottom">
        <van-picker :columns="deductTypeOptions" @confirm="onDeductTypeConfirm"
            @cancel="showDeductTypePicker = false" />
    </van-popup>
</template>

<script setup>
import {
    ref,
    onMounted,
    getCurrentInstance,
} from "vue";
import { useLoginStore } from "@/store/dingLogin";
import {
    showToast,
    showFailToast,
    showDialog,
} from "vant";
import { useRouter } from "vue-router";

// 路由跳转
const router = useRouter();
const app = useLoginStore();
// 接口
const { proxy } = getCurrentInstance();

// 页面首次加载
onMounted(() => {
    // getPermisson();
    if (app.provideSubsidies.userlst) {
        userlst.value = JSON.parse(JSON.stringify(app.provideSubsidies.userlst));
    }
});

// 手工处理记录
const list = ref([]);
// 空数据
let description = ref("暂无内容");
// 是否展示空状态
let showdescription = ref(false);

// 表格数据
let form = ref({
    deduct_type: 1, // 扣除类型：1-定额扣除(默认)，2-全额扣除，3-不够小于等于0
    personnel_type: 2, // 改为字符串，默认指定人员
    amount: app.amount || "",
    remark: app.remark || "",
    user_ids: [],
    dept_ids: [],
});

// 扣除类型相关数据
const showDeductTypePicker = ref(false);
const deductTypeOptions = [
    { text: '定额扣除', value: 1 },
    { text: '全额扣除', value: 2 }
];

// 选择人组件
let data = ref({
    type: app.type || 0,
});
let userlst = ref({
    users: [],
    departments: [],
});
// 初始化扣除补贴的独立状态
app.deductSubsidies.visiterList = app.deductSubsidies.visiterList || [];
app.deductSubsidies.externalUserList = app.deductSubsidies.externalUserList || [];
let rolelst = app.deductSubsidies.visiterList;
const headbutton = () => {
    if (data.value.type) {
        app.type = data.value.type;
        app.amount = form.value.amount;
        app.remark = form.value.remark;
        router.push("/selectRole");
        // proxy.$_dd.biz.contact.rolesPicker({
        //   corpId: app.corpId,
        //   title: "请选择角色",
        //   limitTips: "最多可选择1000个角色",
        //   maxRoles: 1000,
        //   onSuccess: (result) => {
        //     console.log(66666, result);
        //     rolelst.value = result.roles.map((el) => ({
        //       id: el.roleId,
        //       name: el.name,
        //     }));
        //   },
        //   onFail: function (err) {},
        // });
    } else {
        if (app.browserEnv == "wx") {
            proxy.$_dd.selectEnterpriseContact({
                fromDepartmentId: 0,
                mode: "multi",
                type: ["user"],
                selectedDepartmentIds: userlst.value.departments.map(
                    (el) => el.dept_id
                ),
                selectedUserIds: userlst.value.users.map((el) => el.userid),
                success(res) {
                    res = res.result;
                    userlst.value.users = res.userList.map((it) => {
                        return {
                            userid: it.id,
                            name: "",
                            avatar: "",
                        };
                    });
                    userlst.value.departments = res.departmentList.map((it) => {
                        return {
                            dept_id: it.id,
                            name: "",
                        };
                    });
                },
            });
        } else {
            proxy.$_dd.complexChoose({
                showOrgEcological: true,
                showLabelPick: true,
                rootPage: "CommonOrgContact",
                title: "选择负责人",
                corpId: app.corpId,
                multiple: true,
                maxUsers: 10000,
                disabledUsers: [], //不可选用户
                disabledDepartments: [], //不可选部门
                requiredUsers: [], //必选用户（不可取消选中状态）
                requiredDepartments: [], //必选部门（不可取消选中状态）
                pickedUsers: userlst.value.users.map((el) => el.userid), //已选用户
                pickedDepartments: userlst.value.departments.map((el) => el.dept_id), //已选部门
                appId: app.appid, //微应用id
                onSuccess: function (res) {
                    // 选择联系人或部门成功后的回调函数
                    console.log(res);
                    userlst.value.users = res.users.map((it) => {
                        return {
                            userid: it.emplId,
                            name: it.name,
                            avatar: it.avatar,
                        };
                    });
                    userlst.value.departments = res.departments.map((it) => {
                        return {
                            dept_id: it.id,
                            name: it.name,
                        };
                    });
                },
                onFail: function (err) {
                    // 选择联系人或部门失败后的回调函数
                },
            });
        }
    }
};

// 切换选择类型
const switchType = (type) => {
    data.value.type = type;
};

// 获取内部员工显示文本
const getInternalStaffText = () => {
    if (data.value.type === 0) {
        const userCount = userlst.value.users?.length || 0;
        const deptCount = userlst.value.departments?.length || 0;
        const total = userCount + deptCount;
        if (total > 0) {
            if (deptCount > 0 && userCount > 0) {
                return `已选择${deptCount}个部门，${userCount}个成员`;
            } else if (deptCount > 0) {
                return `已选择${deptCount}个部门`;
            } else {
                return `已选择${userCount}个成员`;
            }
        }
    } else {
        const roleCount = rolelst.length || 0;
        if (roleCount > 0) {
            return `已选择${roleCount}个角色`;
        }
    }
    return '';
};

// 获取外部人员显示文本
const getExternalStaffText = () => {
    if (app.deductSubsidies.externalUserList && app.deductSubsidies.externalUserList.length > 0) {
        return `已选择${app.deductSubsidies.externalUserList.length}个人员`;
    }
    return '已选择0个人员';
};

// 获取扣除类型显示文本
const getDeductTypeText = () => {
    const selectedOption = deductTypeOptions.find(option => option.value === form.value.deduct_type);
    return selectedOption ? selectedOption.text : '定额扣除';
};

// 显示扣除类型选择器
const showDeductTypeSelector = () => {
    showDeductTypePicker.value = true;
};

// 确认选择扣除类型
const onDeductTypeConfirm = ({ selectedOptions }) => {
    form.value.deduct_type = selectedOptions[0].value;

    // 如果选择全额扣除，清空金额
    if (selectedOptions[0].value === '2') {
        form.value.amount = '';
    }

    showDeductTypePicker.value = false;
};

// 处理结果数据
const processResultData = (res) => {
    const results = [];

    // 处理成功的人员
    if (res.success_list && res.success_list.length > 0) {
        res.success_list.forEach(item => {
            results.push({
                name: item.name || item.username || '未知用户',
                success: true,
                errorMsg: ''
            });
        });
    }

    // 处理失败的人员
    if (res.failed_list && res.failed_list.length > 0) {
        res.failed_list.forEach(item => {
            results.push({
                name: item.name || item.username || '未知用户',
                success: false,
                errorMsg: item.error || '发放失败，用户不存在'
            });
        });
    }

    // 如果没有详细数据，根据返回的总数生成模拟数据
    if (results.length === 0 && res.result) {
        const totalCount = parseInt(res.result);
        for (let i = 0; i < totalCount; i++) {
            results.push({
                name: `用户${i + 1}`,
                success: true,
                errorMsg: ''
            });
        }
    }

    return results;
};

// 生成测试结果数据
const generateTestResultData = () => {
    const results = [];

    // 根据选择的人员生成测试数据
    if (data.value.type === 0) {
        // 人员模式
        const users = userlst.value.users || [];
        const departments = userlst.value.departments || [];

        // 添加用户数据
        users.forEach((user, index) => {
            results.push({
                name: user.name || `用户${index + 1}`,
                success: Math.random() > 0.3, // 70%成功率
                errorMsg: Math.random() > 0.3 ? '' : '发放失败，用户不存在'
            });
        });

        // 添加部门数据（模拟部门下的用户）
        departments.forEach((dept, index) => {
            const userCount = Math.floor(Math.random() * 5) + 1; // 1-5个用户
            for (let i = 0; i < userCount; i++) {
                results.push({
                    name: `${dept.name || `部门${index + 1}`}-用户${i + 1}`,
                    success: Math.random() > 0.3,
                    errorMsg: Math.random() > 0.3 ? '' : '发放失败，用户不存在'
                });
            }
        });
    } else {
        // 角色模式
        rolelst.forEach((role, index) => {
            const userCount = Math.floor(Math.random() * 8) + 2; // 2-9个用户
            for (let i = 0; i < userCount; i++) {
                results.push({
                    name: `${role.name || `角色${index + 1}`}-用户${i + 1}`,
                    success: Math.random() > 0.3,
                    errorMsg: Math.random() > 0.3 ? '' : '发放失败，用户不存在'
                });
            }
        });
    }

    // 添加外部人员数据
    if (app.deductSubsidies.externalUserList && app.deductSubsidies.externalUserList.length > 0) {
        app.deductSubsidies.externalUserList.forEach((user, index) => {
            results.push({
                name: user.name || `外部用户${index + 1}`,
                success: Math.random() > 0.4, // 外部用户成功率稍低
                errorMsg: Math.random() > 0.4 ? '' : '扣除失败，外部用户验证失败'
            });
        });
    }

    // 如果没有选择任何人员，生成默认测试数据
    if (results.length === 0) {
        results.push(
            { name: '张三', success: true, errorMsg: '' },
            { name: '李四', success: true, errorMsg: '' },
            { name: '王五', success: false, errorMsg: '发放失败，用户不存在' },
            { name: '赵六', success: false, errorMsg: '发放失败，账户异常' }
        );
    }

    return results;
};



// 处理内部员工点击
const handleInternalStaff = () => {
    if (data.value.type === 1) {
        // 角色类型，跳转到选择角色页面
        app.type = data.value.type;
        app.amount = form.value.amount;
        app.remark = form.value.remark;
        router.push('/provideSubsidies/crud/selectRole');
    } else {
        // 人员类型，调用原来的选择人员逻辑
        headbutton();
    }
};

// 处理外部人员点击
const handleExternalStaff = () => {
    // 保存当前表单数据
    app.deductSubsidies.type = data.value.type;
    app.deductSubsidies.amount = form.value.amount;
    app.deductSubsidies.remark = form.value.remark;
    app.provideSubsidies.userlst = JSON.parse(JSON.stringify(userlst.value));
    // 跳转到扣除补贴的外部人员选择页面
    router.push('/deductSubsidies/crud/selectExternalUser');
};

// 删除选人
const delUser = (index) => {
    userlst.value.users.splice(index, 1);
};

const delrolelst = (index) => {
    rolelst.splice(index, 1);
};

// 删除部门
const delDep = (index) => {
    userlst.value.departments.splice(index, 1);
};

const getPermisson = () => {
    proxy
        .$get("account_log/get_ls", {
            page: 1,
            per_page: 4,
            change_type: 0,
        })
        .then((res) => {
            //   console.log(res);
            list.value = res.result.data;
            showdescription.value = true;
        })
        .catch((err) => {
            console.log(err);
        });
};

// 清除余额
const cleanMoney = () => {
    form.value.amount = null;
};

// 提交任务
const submit = () => {
    // 根据扣除类型进行不同的验证
    if (form.value.deduct_type === 1) {
        // 定期扣除需要验证金额不能小于或等于0
        if (!form.value.amount || form.value.amount <= 0) {
            showToast({
                message: '扣除金额不能小于或等于0',
                type: 'text',
                duration: 2000
            });
            // 聚焦到金额输入框
            setTimeout(() => {
                const amountInput = document.querySelector('.amount-field');
                if (amountInput) {
                    amountInput.focus();
                }
            }, 100);
            return;
        }
    } else if (form.value.deduct_type === 2) {
        // 全额扣除不需要金额，清空金额字段
        form.value.amount = '';
    }

    // 验证人员选择（仅在指定人员时验证）
    if (form.value.personnel_type === 2) {
        let hasInternalStaff = false;
        let hasExternalStaff = false;
        let internalCount = 0;
        let externalCount = 0;

        // 检查内部员工数据
        if (data.value.type === 0) {
            // 人员类型：检查用户和部门
            const userCount = userlst.value.users ? userlst.value.users.length : 0;
            const deptCount = userlst.value.departments ? userlst.value.departments.length : 0;
            internalCount = userCount + deptCount;
            hasInternalStaff = internalCount > 0;
        } else {
            // 角色类型：检查角色
            internalCount = rolelst.length;
            hasInternalStaff = internalCount > 0;
        }

        // 检查外部人员数据
        externalCount = app.deductSubsidies.externalUserList ? app.deductSubsidies.externalUserList.length : 0;
        hasExternalStaff = externalCount > 0;

        // 如果内部员工和外部人员都没有数据，提示用户
        if (!hasInternalStaff && !hasExternalStaff) {
            showToast({
                message: '请选择内部员工或外部人员',
                type: 'text',
                duration: 2000
            });
            return;
        }

        // 打印选择的人员信息用于调试
        console.log('内部员工数量:', internalCount);
        console.log('外部人员数量:', externalCount);
        console.log('选择类型:', data.value.type === 0 ? '人员' : '角色');
    }

    const selectedUsers = userlst.value.users;         // 选中的人员集合
    const selectedDepartments = userlst.value.departments; // 选中的部门集合
    const externalUserList = app.deductSubsidies.externalUserList; // 选中的部门集合

    // 你可以在这里分别处理
    console.log('选中的人员:', selectedUsers);
    console.log('选中的部门:', selectedDepartments);
    console.log('选中的外部:', externalUserList);

    // 处理选人
    if (data.value.type) {
        // form.value.rolelst = JSON.stringify(rolelst.value);
        form.value.rolelst = rolelst;
    } else {
        form.value.userlst = JSON.stringify(userlst.value);
    }
    form.value.user_ids = []
    if (selectedUsers.length > 0) {
        selectedUsers.forEach(user => {
            form.value.user_ids.push(user.userid.toString());
        });
    }
    if (externalUserList.length > 0) {
        externalUserList.forEach(user => {
            form.value.user_ids.push(user.id.toString());
        });
    }
    form.value.dept_ids = []
    if (selectedDepartments.length > 0) {
        selectedDepartments.forEach(dept => {
            form.value.dept_ids.push(dept.dept_id.toString());
        });
    }
    // 打印
    console.log("提交form数据 --->", form.value);
    proxy.$post("/transaction_flow/deduct_subsidy", form.value).then((res) => {
        if (res.code == 200) {
            // 直接跳转到结果页面
            router.push({
                path: '/deductSubsidies/result',
                query: {
                    results: JSON.stringify(res.data.results)
                }
            });

        } else {
            showFailToast({
                message: res.msg || '提交失败，请稍后再试',
                duration: 2000
            });
            return;
        }
    }).catch((err) => {
        showFailToast({
            message: err.msg || '提交失败，请稍后再试',
            duration: 2000
        });
    });
    // 暂时不调用API，直接使用测试数据跳转
    console.log('使用测试数据，准备跳转到结果页面');
    // 生成测试结果数据
    // const testResultData = generateTestResultData();
    // console.log('生成的测试数据:', testResultData);


    console.log('跳转命令已执行');
};
</script>

<style lang="scss" scoped>
// 防止横向滚动
* {
    box-sizing: border-box;
    max-width: 100%;
}

// 防止Toast影响页面布局
:global(.van-toast) {
    position: fixed !important;
    z-index: 9999 !important;
}

.content {
    min-height: 100vh;
    padding: 16px 16px 100px 16px;
    background-color: #f2f3f4;
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;

    // 确保所有cell-group统一边距
    :deep(.van-cell-group) {
        position: relative;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        overflow-x: hidden;
        margin: 0 0 16px 0;
    }

    :deep(.van-cell-group--inset) {
        margin: 0 0 16px 0;
        border-radius: 8px;
    }

    :deep(.van-cell) {
        padding-left: 16px;
        padding-right: 16px;
    }
}

// 扣除类型cell样式
.deduct-type-cell {
    height: 54px;
    align-items: center;

    :deep(.van-cell) {
        min-height: 54px;
        height: 54px;
    }

    :deep(.van-cell__title) {
        font-size: 16px;
        color: #323233;
        font-weight: 500;
    }

    :deep(.van-cell__value) {
        font-size: 15px;
        color: #646566;
    }
}


// 扣除人员样式
.distribute-section {
    height: 54px;
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;

    // 添加与van-cell一致的分割线
    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 16px;
        right: 16px;
        height: 1px;
        background: #ebedf0;
    }

    .distribute-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 16px;
        width: 100%;
        height: 100%;

        .section-title {
            font-size: 16px;
            color: #323233;
            font-weight: 500;
        }

        .radio-group {
            :deep(.van-radio-group) {
                display: flex;
                gap: 20px;
            }

            :deep(.van-radio) {
                .van-radio__icon {
                    .van-icon {
                        border: 1px solid #c8c9cc;
                        border-radius: 50%;
                        width: 16px;
                        height: 16px;
                        background: #fff;
                    }

                    &--checked .van-icon {
                        background: #fff;
                        border-color: #1989fa;
                        color: #1989fa;
                        position: relative;

                        &::before {
                            content: '';
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            width: 8px;
                            height: 8px;
                            background: #1989fa;
                            border-radius: 50%;
                        }
                    }
                }

                .van-radio__label {
                    font-size: 14px;
                    color: #323233;
                    margin-left: 6px;
                }
            }
        }
    }
}

// 人员/角色选择样式
.type-section {
    height: 54px;
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;

    // 添加与van-cell一致的分割线
    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 16px;
        right: 16px;
        height: 1px;
        background: #ebedf0;
    }

    .type-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 16px;
        width: 100%;
        height: 100%;

        .section-title {
            font-size: 16px;
            color: #323233;
            font-weight: 500;
        }

        .type-buttons {
            display: flex;
            background: #f5f5f5;
            border-radius: 6px;
            padding: 2px;

            .type-btn {
                padding: 8px 20px;
                font-size: 14px;
                color: #646566;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 60px;
                text-align: center;

                &.active {
                    background: #fff;
                    color: #323233;
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                }
            }
        }
    }
}

// 员工选择样式
.staff-cell {
    width: 100%;
    max-width: 100%;
    height: 54px;
    font-size: 16px;
        line-height: 22px;
        color: #323233;
        font-weight: 500;
    box-sizing: border-box;
    overflow-x: hidden;
    align-items: center;

    :deep(.van-cell) {
        padding: 16px;
        min-height: 54px;
        height: 54px;
    }

    :deep(.van-cell__title) {
        font-size: 16px;
        color: #323233;
    }

    :deep(.van-cell__value) {
        font-size: 15px;
        color: #646566;
        word-wrap: break-word;
        word-break: break-all;
    }
}

// 扣除金额样式
.amount-section {
    padding: 16px 0;
    border-bottom: 1px solid #ebedf0;
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;

    .section-title {
        font-size: 16px;
        color: #323233;
        margin-bottom: 16px;
        font-weight: 500;
        padding: 0 16px;

        .required-mark {
            color: #ee0a24;
            margin-left: 2px;
        }
    }

    .amount-input {
        display: flex;
        align-items: center;
        position: relative;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        padding: 0 16px;

        .currency {
            font-size: 32px;
            color: #323233;
            margin-right: 8px;
            font-weight: 500;
        }

        .amount-field {
            flex: 1;
            border: none;
            outline: none;
            font-size: 32px;
            color: #323233;
            background: transparent;
            font-weight: 500;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;

            &::placeholder {
                color: #c8c9cc;
            }
        }

        .clear-icon {
            color: #171A1D;
        font-size: 10px;
        border: 2px solid #171A1D;
        padding: 5px;
        font-weight:bold;
        border-radius: 50%;

            &:hover {
                color: #969799;
            }
        }
    }


}

// 备注样式
.remark-section {
    padding: 16px 0;
    position: relative;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;

    .section-title {
        font-size: 16px;
        color: #323233;
        margin-bottom: 12px;
        font-weight: 500;
        padding: 0 16px;
    }

    .remark-field {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        padding: 0 16px;

        :deep(.van-field__control) {
            font-size: 14px;
            line-height: 1.5;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
            word-wrap: break-word;
        }

        :deep(.van-field__word-limit) {
            color: #c8c9cc;
            font-size: 12px;
        }
    }
}


.item {
    margin-top: 16px;
}

.deduction {
    font-size: 22px;
}



.sec-title {
    margin-left: 5%;
    color: #caccce;
    line-height: 40px;
    font-size: 13px;
}

.card {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    font-size: 12px;
    border-bottom: 1px solid #f2f3f4;

    .left {
        display: flex;

        .icon {
            margin-right: 10px;
            width: 20px;
            height: 20px;
            background-color: #ff5219;
            color: #fff;
            line-height: 20px;
            border-radius: 50%;
            text-align: center;
        }

        .desc {
            color: #caccce;
            line-height: 22px;
        }

        .l1 {
            margin-bottom: 6px;
            color: #000;
            font-size: 14px;
        }
    }

    .right {
        color: #caccce;

        .num {
            margin-bottom: 10px;
            font-size: 16px;
            color: #ff5219;
        }
    }
}

.card:last-child {
    border-bottom: none;
}

.van-popup {
    .header {
        margin-top: 14px;
        text-align: center;
        font-size: 17px;
        line-height: 25px;
    }

    .p {
        display: flex;
        justify-content: space-between;
        width: 86%;
        margin-left: 7%;

        img {
            width: 14px;
            height: 10px;
        }
    }
}

.bar {
    height: 60px;

    .submit {
        margin-top: 12px;
        margin-bottom: 5px;
        margin-left: 5%;
        width: 90%;
        border-radius: 20px;
    }
}
</style>