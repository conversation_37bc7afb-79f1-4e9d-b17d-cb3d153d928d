<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="detail"
      :editRedirectConfig="editRedirectConfig"
      @onSubmit="onBasicSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'
const { proxy } = getCurrentInstance();

const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    id: route.query.id // 从路由获取id参数
  },
  curl: {
    info: '/dishes_category/get_info', // 获取详情接口
    del: '/dishes_category/post_del' // 删除接口
  },
  groupForm: [
    [0, 1],
    [1, 4]
  ],
  form: [
    {
      label: "档口名称",
      key: "title",
      component: "yhc-input",
      // 不允许输入
      disabled: true,
      type: "text",
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请填写用户名" }],
    },
  ]
}

// 修改按钮跳转配置
const editRedirectConfig = {
  path: '/dishSortAdd', // 跳转到新增页面进行编辑
  query: {
    id: route.query.id, // 传递id参数
    from: 'detail' // 标识来源
  }
}

// 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title:'分类详情',
  });
};
setRightA()
setRight()
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
