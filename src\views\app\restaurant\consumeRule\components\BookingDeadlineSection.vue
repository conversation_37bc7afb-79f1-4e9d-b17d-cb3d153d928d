<template>
  <section class="booking-deadline-section">
    <!-- 预定截止时间 - 包含子表单的统一卡片 -->
    <van-cell-group inset>
      <van-field
        v-model="formData.booking_deadline_type_text"
        name="booking_deadline_type"
        label="预定截止时间"
        placeholder="请选择预定截止时间"
        readonly
        :is-link="!props.disabled"
        :disabled="props.disabled"
        @click="props.disabled ? null : $emit('showBookingDeadlineSelector')"
      />

      <!-- 自定义截止子表单 - 嵌套在同一卡片内 -->
      <div v-if="formData.booking_deadline_type === 2" class="conditional-form">
      <van-field name="booking_deadline_time_type" label="截止时间" :disabled="props.disabled">
        <template #input>
          <van-radio-group v-model="formData.booking_deadline_time_type" direction="horizontal" :disabled="props.disabled">
            <van-radio :name="0"  >统一截止</van-radio>
            <van-radio :name="1"  >分餐截止</van-radio>
          </van-radio-group>
        </template>
      </van-field>



      <!-- 统一截止子表单 -->
      <div v-if="formData.booking_deadline_time_type === 0" class="unified-deadline-form">
        <van-field
          v-model="formData.unified_deadline_time_text"
          name="unified_deadline_time"
          label="时间"
          placeholder="请选择截止时间"
          readonly
          :is-link="!props.disabled"
          :disabled="props.disabled"
          @click="handleUnifiedTimeClick"
        />
        <van-field name="booking_deadline_advance_days" labelWidth="130px" :disabled="props.disabled" label="截止时间提前/天">
          <template #input>
            <van-stepper v-model="formData.booking_deadline_advance_days" min="0" max="99" :disabled="props.disabled" />
          </template>
        </van-field>
      </div>

      <!-- 分餐截止子表单 -->
      <div v-if="formData.booking_deadline_time_type === 1" class="separate-deadline-form">

        <!-- 餐时列表 -->
        <div class="meal-times-list">
          <div
            v-for="mealtime in formData.mealtimes_list"
            :key="mealtime.id"
            class="meal-time-item"
          >
            <van-field
              :model-value="getMealtimeDeadlineTime(mealtime.id, 'booking')"
              :name="`booking_meal_time_${mealtime.id}`"
              :label="mealtime.title"
              placeholder="请选择时间"
              readonly
              :is-link="!props.disabled"
              :disabled="props.disabled"
              @click="props.disabled ? null : handleMealtimeClick(mealtime.id, 'booking')"
            />
          </div>
           <van-field name="booking_deadline_advance_days" labelWidth="130px" :disabled="props.disabled" label="截止时间提前/天">
            <template #input>
              <van-stepper v-model="formData.booking_deadline_advance_days" min="0" max="99" :disabled="props.disabled" />
            </template>
          </van-field>
        </div>
      </div>
      </div>
    </van-cell-group>

    <!-- 统一截止提前天数说明文字 -->
    <div v-if="formData.booking_deadline_type === 2 && formData.booking_deadline_time_type === 0" class="advance-days-description">
      例：填1天，提前1天截止时间前可进行预定
    </div>

    <!-- 分餐截止提前天数说明文字 -->
    <div v-if="formData.booking_deadline_type === 2 && formData.booking_deadline_time_type === 1" class="advance-days-description">
      例：填1天，提前1天截止时间前可进行预定
    </div>

    <!-- 提前预定上限/天 -->
    <van-cell-group inset>
      <van-field name="booking_advance_limit_days" labelWidth="130px" label="提前预定上限/天" :disabled="props.disabled">
        <template #input>
          <van-stepper v-model="formData.booking_advance_limit_days" min="0" max="7" :disabled="props.disabled" />
        </template>
      </van-field>
    </van-cell-group>

    <!-- 提前预定上限说明文字 -->
    <div class="advance-days-description">
      例：填7天，最多提前7天预定；填0天，仅支持当日预定
    </div>
  </section>
</template>

<script setup>
import { watch } from 'vue'

// Props
const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// 🔧 调试：监听formData变化
watch(() => props.formData.unified_deadline_time_text, (newVal, oldVal) => {
  console.log('🔧 BookingDeadlineSection - unified_deadline_time_text变化:', { oldVal, newVal })
}, { immediate: true })

// Emits
const emit = defineEmits([
  'showBookingDeadlineSelector',
  'showUnifiedTimeSelector',
  'showMealTimeSelector'
])

// 处理统一时间点击事件
const handleUnifiedTimeClick = () => {
  if (props.disabled) return
  console.log('🔧 BookingDeadlineSection - 点击统一时间选择器')
  console.log('🔧 当前unified_deadline_time_text值:', props.formData.unified_deadline_time_text)
  emit('showUnifiedTimeSelector')
}

// 处理餐时点击事件
const handleMealtimeClick = (mealtimeId, type) => {
  emit('showMealTimeSelector', mealtimeId, type)
}

// 获取餐时截止时间的方法
const getMealtimeDeadlineTime = (mealtimeId, type) => {
  const deadline = props.formData.mealtime_deadlines?.find(d => d.mealtime_id === mealtimeId)
  if (!deadline) return ''

  return type === 'booking' ? deadline.booking_deadline_time : deadline.cancel_deadline_time
}
</script>

<style lang="scss" scoped>
@import './common-styles.scss';

// 预定截止时间样式 - 继承通用样式
.custom-deadline-options,
.unified-deadline-form,
.separate-deadline-form {
  @extend .conditional-form;
}
.booking-deadline-section{
  .van-field{
    padding:16px;
    font-size:16px;
  }
}
</style>
