<template>
  <div class="box" ref="content">
    <div class="card">
      <div class="top">
        <div class="title">付款码</div>
        <div class="code" id="code">
          <!-- <vue-qr
            :bgSrc="src"
            :logoSrc="src2"
            text="Hello world!"
            :size="200"
          ></vue-qr> -->
          
          <vue-qr
            :text="url"
            margin="0"
            logoSrc="/icons/logo.png"
            qid="testid"
            :logoScale="logoScale"
            :logoMargin="2"
          ></vue-qr>
        </div>

        <!-- <div class="tips">提示:长按二维码保存到相册</div> -->
      </div>
      <div class="line"></div>
      <div class="bottom">
        <div class="l1">付款码</div>
        <div class="l2">{{ url }}</div>
        <div class="but" @click="copyToClipboard">复制</div>
      </div>
    </div>
  </div>
</template>
  
  <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useRouter } from "vue-router";
import { showImagePreview, showSuccessToast } from "vant";

// vue3.0
import vueQr from "vue-qr/src/packages/vue-qr.vue";

// 接口
const { proxy } = getCurrentInstance();

const viewportHeight = ref(0);
const content = ref(null);

// 网址
let url = ref('');
let logoScale = ref(0.13);

onMounted(() => {
  // const temviewportHeight = document.documentElement.scrollHeight;
  // viewportHeight.value = temviewportHeight;
  // content.value.style.height = `${viewportHeight.value}px`;

  // 获取网址
  proxy
    .$get("/payment_code/get_info")
    .then((res) => {
      url.value = res.result;
    })
    .catch((err) => {
      console.log(err);
    });
});

const copyToClipboard = () => {
  navigator.clipboard
    .writeText(url.value)
    .then(() => {
      showSuccessToast("内容已复制");
    })
    .catch((err) => {
      console.error("无法复制文本: ", err);
    });
};
</script>
  
<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100vh;
  background-color: #1678ff;
  overflow: hidden;

  .card {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    margin-left: 5%;
    margin-top: 24px;
    width: 90%;
    height: 500px;
    background-color: #fff;
    border-radius: 15px;
    overflow: hidden;
    text-align: center;

    .top {
      margin-top: 26px;
      font-size: 17px;
      .code {
        position: relative;
        margin-top: 34px;
        width: 200px;
        height: 200px;

        .qrcode {
          width: 100%;
          height: 100%;
        }

        .logo {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 35px;
          height: 35px;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .tips {
        margin-top: 26px;
        font-size: 12px;
        color: #747677;
      }
    }
    .line {
      margin-top: 36px;
      background-color: #c8c8c9;
      width: 80%;
      height: 1px;
    }

    .bottom {
      margin-top: 26px;
      font-size: 14px;

      .l1 {
        color: #a2a3a5;
      }

      .l2 {
        margin-top: 6px;
        color: #1678ff;
      }

      .but {
        margin-top: 16px;
        display: inline-block;
        padding: 6px 12px;
        border-radius: 4px;
        color: #1678ff;
        background-color: #dcebff;
      }
    }
  }

  .card::before {
    position: absolute;
    left: -8px;
    top: 347px;
    display: block;
    content: "";
    background-color: #1678ff;
    width: 16px;
    height: 16px;
    border-radius: 50%;
  }
  .card::after {
    position: absolute;
    right: -8px;
    top: 347px;
    display: block;
    content: "";
    background-color: #1678ff;
    width: 16px;
    height: 16px;
    border-radius: 50%;
  }
}
</style>
    