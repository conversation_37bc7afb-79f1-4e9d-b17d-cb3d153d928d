<template>
  <van-popup
    :show="show"
    @update:show="$emit('update:show', $event)"
    position="bottom"
    :style="{ height: '60%', zIndex: 9999 }"
    round
    :closeable="false"
  >
    <div class="dining-type-drawer">
      <div class="top-block">
        <span @click="$emit('cancel')" class="cancel-btn">取消</span>
        <span class="title-text">就餐方式</span>
        <span @click="$emit('confirm')" class="confirm-btn">确认</span>
      </div>
      
      <div class="list-content">
        <div class="multi-select-list">
          <div
            v-for="(option, index) in options"
            :key="option.value"
            class="multi-select-item"
            :class="{ disabled: option.disabled }"
            @click="handleItemClick(option)"
          >

            <div class="item-content">
             <!-- 多选框状态图标 -->
              <div class="check-icon-wrapper">
                <!-- 选中状态：显示蓝色圆形对勾图标 -->
                <div
                  v-if="selectedTypes.includes(option.value)"
                  class="check-icon selected"
                  :class="{ disabled: option.disabled }"
                >
                  <van-icon
                    name="success"
                    size="12"
                    color="#fff"
                  />
                </div>
                <!-- 未选中状态：显示灰色圆环 -->
                <div
                  v-else
                  class="check-icon unselected"
                ></div>
              </div>
              <div class="item-main">

                <span class="item-text">{{ option.text }}</span>
                <span class="item-detail">{{ option.detail }}</span>
              </div>

             
            </div>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
// Props
defineProps({
  show: {
    type: Boolean,
    default: false
  },
  options: {
    type: Array,
    required: true
  },
  selectedTypes: {
    type: Array,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:show', 'toggle', 'cancel', 'confirm'])

// 处理选项点击
const handleItemClick = (option) => {
  // 如果选项被禁用（如"堂食"），则不触发切换
  if (option.disabled) {
    return
  }
  // 只有非禁用选项才能切换
  emit('toggle', option)
}
</script>

<style lang="scss" scoped>
// yhc-picker 多选抽屉样式
.dining-type-drawer {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;

  .top-block {
    flex-shrink: 0;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebedf0;
    background: #fff;

    .cancel-btn {
      color: #969799;
      font-size: 14px;
      font-weight: 400;
      cursor: pointer;
      padding: 4px 0;

      &:active {
        opacity: 0.6;
      }
    }

    .confirm-btn {
      color: #007fff;
      font-size: 14px;
      font-weight: 400;
      cursor: pointer;
      padding: 4px 0;

      &:active {
        opacity: 0.6;
      }
    }

    .title-text {
      color: #323233;
      font-weight: 500;
      font-size: 17px;
      line-height: 24px;
    }
  }

  .list-content {
    flex: 1;
    overflow-y: auto;
    background: #fff;

    .multi-select-list {
      .multi-select-item {
        position: relative;
        padding: 0 20px;
        min-height: 60px;
        display: flex;
        align-items: center;
        cursor: pointer;
        background: #fff;

        &:not(:last-child) {
          border-bottom: 1px solid #ebedf0;
        }

        &.disabled {
          cursor: default;

          .item-text {
            color: #969799;
          }

          .item-detail {
            color: #c8c9cc;
          }
        }

        &:not(.disabled):active {
          background-color: #f2f3f5;
        }

        .item-content {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          min-height: 60px;

          .item-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding-right: 16px;

            .item-text {
              color: #323233;
              font-size: 16px;
              font-weight: 400;
              line-height: 24px;
              margin: 0;
            }

            .item-detail {
              color: #969799;
              font-size: 13px;
              font-weight: 400;
              line-height: 18px;
              margin: 0;
            }
          }

          .check-icon-wrapper {
            flex-shrink: 0;
            width: 20px;
            height: 20px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;

            .check-icon {
              width: 20px;
              height: 20px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              box-sizing: border-box;

              &.selected {
                background: #007fff;
                border: none;

                &.disabled {
                  background: #c8c9cc;
                }
              }

              &.unselected {
                background: transparent;
                border: 1.5px solid #dcdee0;
              }
            }
          }
        }
      }
    }
  }
}

// 弹窗容器样式
:deep(.van-popup) {
  background: #fff;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
}
</style>
