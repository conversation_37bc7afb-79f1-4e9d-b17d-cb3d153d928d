<template>
  <div class="voice-booking-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h3>预定核销语音播报</h3>
      <p>设置预定核销时的语音播报内容</p>
    </div>

    <!-- 配置表单 -->
    <div class="config-form">
      <!-- 语音播报预览 -->
      <div class="preview-section">
        <div class="section-title">语音播报预览</div>
        <div class="preview-card">
          <div class="voice-icon">
            <van-icon name="volume-o" size="40" color="#1989fa" />
          </div>
          <div class="voice-text">
            "{{ generateVoiceText() }}"
          </div>
          <div class="voice-controls">
            <van-button size="small" type="primary" @click="playVoice" :loading="playing">
              <van-icon name="play" />
              试听语音
            </van-button>
            <van-button size="small" @click="stopVoice" style="margin-left: 8px;">
              <van-icon name="pause" />
              停止
            </van-button>
          </div>
        </div>
      </div>

      <!-- 可用标签 -->
      <div class="form-section">
        <div class="section-title">
          可用标签
          <van-button size="mini" type="primary" plain @click="showTagHelper = true" style="float: right;">
            查看帮助
          </van-button>
        </div>
        <div class="tags-container">
          <div class="tags-description">
            <p>点击标签可插入到语音内容中，系统会自动替换为实际值</p>
          </div>
          <div class="available-tags">
            <van-tag
              v-for="tag in availableTags"
              :key="tag.key"
              type="primary"
              size="medium"
              @click="insertTag(tag.key)"
              class="tag-item"
            >
              {{ tag.label }}
            </van-tag>
          </div>
        </div>
      </div>

      <!-- 语音内容配置 -->
      <div class="form-section">
        <div class="section-title">语音内容配置</div>
        <van-cell-group inset>
          <van-field
            v-model="form.voiceTemplate"
            label="语音播报模板"
            type="textarea"
            placeholder="请输入语音播报模板，可使用上方标签"
            rows="3"
            autosize
            maxlength="200"
            show-word-limit
          />
        </van-cell-group>
      </div>

      <!-- 示例数据 -->
      <div class="form-section">
        <div class="section-title">示例数据</div>
        <van-cell-group inset>
          <van-field
            v-model="form.sampleName"
            label="示例姓名"
            placeholder="请输入示例姓名"
            maxlength="20"
          />
          <van-field
            v-model="form.sampleAmount"
            label="示例金额"
            placeholder="请输入示例金额"
            type="number"
          >
            <template #left-icon>
              <span class="amount-prefix">¥</span>
            </template>
          </van-field>
          <van-field
            v-model="form.sampleRestaurant"
            label="示例餐厅"
            placeholder="请输入示例餐厅名称"
            maxlength="30"
          />
        </van-cell-group>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <van-button type="primary" block @click="saveConfig" :loading="saving">
        保存配置
      </van-button>
      <van-button block @click="resetForm" style="margin-top: 12px;">
        重置
      </van-button>
    </div>

    <!-- 标签帮助弹窗 -->
    <van-popup v-model:show="showTagHelper" position="bottom">
      <div class="tag-helper-container">
        <div class="helper-header">
          <h3>标签使用说明</h3>
          <van-icon name="cross" @click="showTagHelper = false" />
        </div>
        <div class="helper-content">
          <div class="helper-item" v-for="tag in availableTags" :key="tag.key">
            <div class="tag-info">
              <van-tag type="primary" size="medium">{{ tag.key }}</van-tag>
              <span class="tag-desc">{{ tag.description }}</span>
            </div>
            <van-button size="mini" @click="insertTag(tag.key); showTagHelper = false">
              插入
            </van-button>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted, nextTick } from 'vue'
import { showToast, showSuccessToast } from 'vant'

const router = useRouter()
const { proxy } = getCurrentInstance()

// 响应式数据
const showTagHelper = ref(false)
const saving = ref(false)
const playing = ref(false)

// 表单数据
const form = reactive({
  voiceTemplate: '预定核销成功，{name}，您在{restaurant}的消费金额{amount}元已确认',
  sampleAmount: '158.00',
  sampleName: '李女士',
  sampleRestaurant: '云一餐厅'
})

// 可用标签（只保留三个核心标签）
const availableTags = [
  { key: '{name}', label: '姓名', description: '用户姓名' },
  { key: '{amount}', label: '金额', description: '核销金额' },
  { key: '{restaurant}', label: '餐厅', description: '餐厅名称' }
]

// 生成语音文本
const generateVoiceText = () => {
  let text = form.voiceTemplate || '预定核销成功'

  // 替换三个核心标签
  text = text.replace(/{name}/g, form.sampleName || '客户')
  text = text.replace(/{amount}/g, form.sampleAmount || '0')
  text = text.replace(/{restaurant}/g, form.sampleRestaurant || '本餐厅')

  return text
}

// 插入标签
const insertTag = (tag) => {
  const textarea = document.querySelector('textarea')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const text = form.voiceTemplate
    form.voiceTemplate = text.substring(0, start) + tag + text.substring(end)
    
    // 设置光标位置
    nextTick(() => {
      textarea.focus()
      textarea.setSelectionRange(start + tag.length, start + tag.length)
    })
  } else {
    form.voiceTemplate += tag
  }
  showToast(`已插入标签：${tag}`)
}

// 播放语音
const playVoice = () => {
  playing.value = true
  showToast('开始播放语音')
  
  setTimeout(() => {
    playing.value = false
    showToast('语音播放完成')
  }, 3000)
}

// 停止语音
const stopVoice = () => {
  playing.value = false
  showToast('语音播放已停止')
}

// 保存配置
const saveConfig = async () => {
  if (!form.voiceTemplate.trim()) {
    showToast('请输入语音播报模板')
    return
  }

  saving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    showSuccessToast('预定核销语音配置保存成功')
    router.back()
  } catch (error) {
    showToast('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    voiceTemplate: '预定核销成功，{name}，您在{restaurant}的消费金额{amount}元已确认',
    sampleAmount: '158.00',
    sampleName: '李女士',
    sampleRestaurant: '云一餐厅'
  })
  showToast('表单已重置')
}

// 设置页面标题
const setPageTitle = () => {
  if (proxy && proxy.$_dd) {
    proxy.$_dd.biz.navigation.setTitle({
      title: '预定核销语音播报',
    })
  }
}

onMounted(() => {
  setPageTitle()
})
</script>

<style lang="scss" scoped>
.voice-booking-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 20px; /* 减少底部间距，因为按钮不再固定 */
}

.page-header {
  background: white;
  padding: 20px 16px;
  border-bottom: 1px solid #ebedf0;

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #323233;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #969799;
  }
}

.config-form {
  padding: 16px;
}

.form-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 12px;
    padding-left: 4px;
  }
}

.preview-section {
  margin-bottom: 20px;

  .preview-card {
    background: white;
    border-radius: 12px;
    padding: 24px 16px;
    text-align: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .voice-icon {
      margin-bottom: 16px;
    }

    .voice-text {
      background: #f7f8fa;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      font-size: 14px;
      color: #323233;
      line-height: 1.5;
      text-align: left;
      border-left: 4px solid #1989fa;
    }

    .voice-controls {
      display: flex;
      justify-content: center;
      gap: 8px;
    }
  }
}

.amount-prefix {
  color: #969799;
  margin-right: 4px;
}

.action-buttons {
  margin: 20px 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

// 标签相关样式
.tags-container {
  background: white;
  border-radius: 8px;
  padding: 16px;

  .tags-description {
    margin-bottom: 12px;
    
    p {
      margin: 0;
      font-size: 12px;
      color: #969799;
      line-height: 1.4;
    }
  }

  .available-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .tag-item {
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }
    }
  }
}

.tag-helper-container {
  background: white;
  max-height: 60vh;
  
  .helper-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #ebedf0;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #323233;
    }

    .van-icon {
      font-size: 18px;
      color: #969799;
      cursor: pointer;
    }
  }

  .helper-content {
    padding: 16px 20px;
    max-height: 40vh;
    overflow-y: auto;

    .helper-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f2f3f5;

      &:last-child {
        border-bottom: none;
      }

      .tag-info {
        display: flex;
        align-items: center;
        flex: 1;

        .tag-desc {
          margin-left: 12px;
          font-size: 14px;
          color: #646566;
        }
      }
    }
  }
}

:deep(.van-cell-group--inset) {
  margin: 0;
}
</style>
