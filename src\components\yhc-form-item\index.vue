<template>
  <div class="wrapper" v-if="initStatus">
    <div :key="config.key">
      <component :key="config.key" :is="config.component" :config="config" :form="form" />
      <div v-if="config.desc" :style="`padding:${height}px;font-size:${fontSize}px;`" class="desc">
        {{ config.desc }}
      </div>
      <template v-if="
        config.child &&
        (!!props.form[config.key] === config.child.showMode ||
          config.child.showMode === 'show')
      ">
        <div class="child-wraper" v-for="(childItem, i) in config.child.form" :key="childItem.key + i">
          <yhc-form-item :key="childItem.key" :config="childItem" :form="form" />
        </div>
      </template>
      <template v-else-if="
        config.child &&
        (!!props.form[config.key] !== config.child.showMode ||
          config.child.showMode === 'hide')
      ">
        <div class="child-wraper" v-for="(childItem, i) in config.child.formChild" :key="childItem.key + i">
          <yhc-form-item :key="childItem.key" :config="childItem" :form="form" />
        </div>
      </template>

      <!-- <template
        v-else
      >
        <div
          class="child-wraper"
          v-for="(childItem, i) in config.child.formChild"
          :key="childItem.key + i"
        >
          <yhc-form-item
            :key="childItem.key"
            :config="childItem"
            :form="form"
          />
        </div>
      </template> -->
    </div>
  </div>
</template>
<script setup>
import {
  reactive,
  ref,
  getCurrentInstance,
  watch,
  shallowReactive,
  shallowRef,
  nextTick,
} from "vue";
import { deepAssign } from "@/untils";
const { proxy } = getCurrentInstance();

// 表单项包装器配置 - 用于包装和渲染各种表单组件
let config = {};

// Props定义
const props = defineProps({
  config: Object,          // 表单项配置 (对象) - 包含表单项的所有配置信息
  form: Object,            // 表单数据 (对象) - 整个表单的数据对象
});

// 合并配置
props.config && deepAssign(config, props.config);

// 内部状态
let initStatus = ref(false);  // 初始化状态 (布尔值) - 标记组件是否已初始化

// 描述样式配置
let height = props.config.descHeight || 8;      // 描述区域高度 (数字) - 描述文字的行高，默认8px
let fontSize = props.config.descFontSize || 13; // 描述字体大小 (数字) - 描述文字的字体大小，默认13px

// 子表单配置说明:
// - config.child.map: 映射模式，根据主字段值动态显示不同的子表单
// - config.child.mapChild: 子映射模式，用于更复杂的嵌套表单场景
// - config.child.form: 主子表单字段数组
// - config.child.formChild: 备用子表单字段数组
// - config.child.showMode: 显示模式，控制何时显示子表单
if (props.config.child && props.config.child.map) {
  // console.log("map------》", props.config.child);
  // 确保 child.form 存在且是数组，然后才创建响应式
  if (!props.config.child.form || !Array.isArray(props.config.child.form)) {
    props.config.child.form = [];
  }
  props.config.child.form = shallowReactive(props.config.child.form);

  watch(
    () => props.form[config.key],
    (v, o) => {
      // console.log("watch----1---》", v, o,props.config.child);
      if (v != undefined) {
        let child = props.config.child;
        let form = props.form;
        if (child && child.map && child.form && Array.isArray(child.form)) {
          child.form.splice(0);
          if (child.map[form[config.key]]) {
            child.form.push(...child.map[form[config.key]]);

            // // 初始化新添加的子字段的默认值
            // child.map[form[config.key]].forEach(childField => {
            //   if (childField.default !== undefined && form[childField.key] === undefined) {
            //     form[childField.key] = childField.default;
            //   }
            // });
          }
          nextTick(proxy.$forceUpdate)
          // triggerRef( child.form);
        }
      }
    },
    {
      immediate: true,
    }
  );
} else if (props.config.child && props.config.child.mapChild) {
  // 确保 child.formChild 存在且是数组，然后才创建响应式
  if (!props.config.child.formChild || !Array.isArray(props.config.child.formChild)) {
    props.config.child.formChild = [];
  }
  props.config.child.formChild = shallowReactive(props.config.child.formChild);

  watch(
    () => props.form[config.key],
    (v, o) => {
      // console.log("watch----2---》", v, o,props.config.child);
      if (v != undefined) {
        let child = props.config.child;
        let form = props.form;
        if (child && child.mapChild && child.formChild && Array.isArray(child.formChild)) {
          child.formChild.splice(0);
          if (child.mapChild[form[config.key]]) {
            child.formChild.push(...child.mapChild[form[config.key]]);

            // // 初始化新添加的子字段的默认值
            // child.mapChild[form[config.key]].forEach(childField => {
            //   if (childField.default !== undefined && form[childField.key] === undefined) {
            //     form[childField.key] = childField.default;
            //   }
            // });
          }
          nextTick(proxy.$forceUpdate)
          // triggerRef( child.formChild);
        }
      }
    }
  )
}
initStatus = true
</script>
<style lang="scss" scoped>
.wrapper {
  .child-wraper {
    background: #fff;
    position: relative;
    // border-radius: 5px;
  }

  .child-wraper::before {
    // content: " ";
    // width: 8px;
    // height: 8px;
    border: 2px solid rgba(23, 26, 29, 0.24);
    // position: absolute;
    // left: 20px;
    // top: 15px;
  }

  .desc {
    color: rgba(0, 0, 0, 0.5);
    background-color: #F2F3F4;
    margin-left: -32px;
    text-indent: 40px;
  }
}
</style>
