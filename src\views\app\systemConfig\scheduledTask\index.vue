<template>
  <div class="scheduled-task-container">
    <!-- 搜索框 -->
    <van-search v-model="searchValue" placeholder="搜索" :clearable="true" @search="onSearch" @clear="onSearch" />

    <!-- 新增按钮 -->
    <div class="add-button-container">
      <div class="add-task-button" @click="onAddTask">
        <van-icon name="add-o" class="add-icon" />
        <span class="add-text">新增计划任务</span>
      </div>
    </div>

    <!-- 骨架屏显示 -->
    <div v-if="showSkeleton" class="skeleton-container">
      <van-skeleton v-for="n in 10" :key="n" :row="3" :row-width="['100%', '80%', '60%']" :loading="true"
        :avatar="false" :title="true" :title-width="'70%'" class="skeleton-item" />
    </div>

    <!-- 计划任务列表 -->
    <div v-else class="task-list">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoadMore">
        <div v-for="(item, index) in filteredData" :key="item.id" class="task-item" @click="onTaskClick(item)">
          <div class="task-content">
            <!-- 左侧内容区域 -->
            <div class="task-left">
              <!-- 任务名称 -->
              <div class="task-name">{{ item.title || item.taskName }}</div>

              <!-- 任务详情 -->
              <div class="task-details">
                <div class="task-detail-row">
                  <span class="detail-label">任务类型：</span>
                  <span class="detail-value">{{ getTaskTypeName(item.cron_type) || item.taskType }}</span>
                </div>

                <!-- 终止时间 -->
                <div class="task-detail-row">
                  <span class="detail-label">终止时间：</span>
                  <span class="detail-value">{{ formatDateTime(item.end_time) || '-' }}</span>
                </div>

                <!-- 下次执行时间（仅在有值时显示） -->
                <div v-if="item.next_time" class="task-detail-row">
                  <span class="detail-label">下次执行时间：</span>
                  <span class="detail-value">{{ formatDateTime(item.next_time) }}</span>
                </div>
              </div>
            </div>

            <!-- 右侧状态标签 -->
            <div class="task-right">
              <van-tag :type="getStatusType(item.status, item)" size="medium" class="status-tag">
                {{ getStatusName(item.status, item) }}
              </van-tag>
            </div>
          </div>
        </div>
      </van-list>
    </div>

    <!-- 空状态 -->
    <van-empty v-if="!showSkeleton && filteredData.length === 0" description="暂无计划任务" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()
const { proxy } = getCurrentInstance()

// 响应式数据
const taskList = ref([]) // API数据

// 响应式数据
const searchValue = ref('')
const showSkeleton = ref(true)

// 分页相关状态
const loading = ref(false)      // 加载更多状态
const finished = ref(false)     // 是否已加载完所有数据

// 搜索参数
const searchParams = ref({
  page: 1,           // 页码
  perPage: 10,       // 每页数量
  title: '',         // 计划名称模糊搜索
  cron_type: null    // 计划任务类型
})

// 获取任务类型名称
const getTaskTypeName = (cronType) => {
  const typeMap = {
    0: '餐食预定',
    1: '扣除补贴',
    2: '清空欠款',
    3: '发放补贴',
    4: '数据统计通知',
    5: '餐时预定开始通知'
  }
  return typeMap[cronType] || ''
}

// 获取状态名称 - 根据设计图和业务逻辑
const getStatusName = (status, item) => {
  // 根据API数据和业务逻辑判断状态
  if (typeof status === 'number') {
    // API数据：0可用，1过期
    if (status === 1) {
      return '已终止' // 过期状态显示为已终止
    }

    // status为0时，需要根据其他字段判断是执行中还是未开始
    if (item) {
      // 如果有下次执行时间，说明是执行中
      if (item.next_time) {
        return '执行中'
      }
      // 如果没有下次执行时间但有开始时间，可能是未开始
      return '未开始'
    }

    return '执行中' // 默认可用状态为执行中
  }

  // 兼容模拟数据的字符串状态
  return status || ''
}

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-'
  try {
    const date = new Date(dateTimeStr)
    if (isNaN(date.getTime())) return dateTimeStr

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    return dateTimeStr
  }
}

// 获取计划任务列表
const getScheduleTaskList = async (isLoadMore = false) => {
  try {
    console.log('开始调用计划任务列表接口: /schedule_task/get_ls')

    // 如果不是加载更多，显示骨架屏
    if (!isLoadMore) {
      showSkeleton.value = true
      searchParams.value.page = 1 // 重置页码
      taskList.value = [] // 清空现有数据
      finished.value = false // 重置完成状态，允许后续加载更多
    }

    // 合并搜索参数
    const requestParams = {
      page: searchParams.value.page,
      perPage: searchParams.value.perPage
    }

    // 只传递有值的搜索参数
    if (searchParams.value.title) {
      requestParams.title = searchParams.value.title
    }
    if (searchParams.value.cron_type !== null && searchParams.value.cron_type !== undefined) {
      requestParams.cron_type = searchParams.value.cron_type
    }

    console.log('请求参数:', requestParams)

    const response = await proxy.$get('/schedule_task/get_ls', requestParams)

    console.log('计划任务列表接口响应:', response)

    if (response && response.code === 200) {
      // 处理API返回的数据结构
      const apiData = response.data?.items || response.data || []
      const total = response.data?.total || 0

      console.log('本次获取数据条数:', apiData.length)
      console.log('总数据条数:', total)

      if (isLoadMore) {
        // 加载更多：追加数据
        taskList.value = [...taskList.value, ...apiData]
      } else {
        // 首次加载或刷新：替换数据
        taskList.value = apiData
      }

      // 简化finished状态判断逻辑 - 仅基于返回数据量判断
      // 如果本次返回的数据少于每页数量，说明已经是最后一页
      finished.value = apiData.length < searchParams.value.perPage

      console.log('=== 分页状态检查 ===')
      console.log('  - 本次获取条数:', apiData.length)
      console.log('  - 每页数量:', searchParams.value.perPage)
      console.log('  - 是否加载完成:', finished.value)
      console.log('  - 判断依据: 本次数据量 < 每页数量 =', apiData.length < searchParams.value.perPage)
      console.log('  - 当前总数据量:', taskList.value.length)
      console.log('  - API返回总数:', total)

      // 首次加载完成
      if (!isLoadMore) {
        console.log('页面初始化完成')
      }

      // 调试：打印第一条数据的结构
      if (apiData.length > 0 && !isLoadMore) {
        console.log('第一条数据结构:', apiData[0])
        console.log('任务类型映射:', getTaskTypeName(apiData[0].cron_type))
        console.log('状态映射:', getStatusName(apiData[0].status, apiData[0]))
        console.log('状态标签类型:', getStatusType(apiData[0].status, apiData[0]))
      }

    } else {
      console.error('接口返回错误:', response)
      showToast(response?.msg || '')
      if (!isLoadMore) {
        taskList.value = []
      }
      finished.value = true // 错误时设置为完成状态，避免继续加载
    }
  } catch (error) {
    console.error('计划任务列表接口调用失败:', error)
    if (!isLoadMore) {
      taskList.value = []
    }
    finished.value = true // 错误时设置为完成状态，避免继续加载
  } finally {
    showSkeleton.value = false
    loading.value = false
    console.log('=== getScheduleTaskList 完成 ===')
    console.log('最终状态 - loading:', loading.value, 'finished:', finished.value)
    console.log('数据总量:', taskList.value.length)
  }
}

// 过滤后的数据（现在直接使用API数据，搜索通过API参数实现）
const filteredData = computed(() => {
  return taskList.value || []
})

// 获取状态标签类型 - 根据设计图设置颜色
const getStatusType = (status, item) => {
  const statusName = getStatusName(status, item)

  // 根据设计图设置状态颜色
  switch (statusName) {
    case '执行中':
      return 'success'  // 绿色标签
    case '未开始':
      return 'default'  // 灰色标签
    case '已终止':
      return 'warning'  // 橙色标签
    default:
      return 'default'
  }
}

// 新增任务
const onAddTask = () => {
  console.log('新增计划任务');
  router.push('/systemConfig/scheduledTask/add');
}

// 点击任务项
const onTaskClick = (item) => {
  console.log('查看计划任务详情:', item);

  // 显示任务详细信息
  const taskInfo = {
    id: item.id,
    title: item.title || item.taskName,
    type: getTaskTypeName(item.cron_type) || item.taskType,
    status: getStatusName(item.status) || item.status,
    startTime: item.start_time,
    endTime: item.end_time,
    nextTime: item.next_time,
    staffName: item.staff_name,
    money: item.money,
    dininghallTitle: item.dininghall_title,
    repastTitle: item.repast_title,
    windowTitle: item.window_title
  }

  console.log('格式化后的任务信息:', taskInfo);

  // 跳转到详情页面
  router.push({
    path: '/systemConfig/scheduledTask/detail',
    query: { id: item.id }
  });
}



// 加载更多数据
const onLoadMore = async () => {
  console.log('=== onLoadMore 触发 ===')
  console.log('当前状态 - loading:', loading.value, 'finished:', finished.value)
  console.log('当前页码:', searchParams.value.page)
  console.log('当前数据量:', taskList.value.length)

  // 如果已经完成加载，直接返回
  if (finished.value) {
    console.log('已完成所有数据加载，跳过')
    loading.value = false
    return
  }

  // 增加页码
  searchParams.value.page += 1
  console.log('页码递增至:', searchParams.value.page)

  try {
    await getScheduleTaskList(true)
    console.log('加载更多完成，最终状态 - loading:', loading.value, 'finished:', finished.value)
  } catch (error) {
    console.error('加载更多失败:', error)
    // 发生错误时回退页码并重置状态
    searchParams.value.page -= 1
    loading.value = false
    finished.value = true // 错误时停止后续加载
    console.log('错误恢复 - 页码回退至:', searchParams.value.page, 'loading:', loading.value, 'finished:', finished.value)
  }
}



// 搜索框输入事件
const onSearch = () => {
  console.log('=== 搜索触发 ===')
  console.log('搜索关键词:', searchValue.value)

  // 更新搜索参数
  searchParams.value.title = searchValue.value
  searchParams.value.page = 1 // 重置到第一页

  console.log('搜索参数更新:', searchParams.value)

  // 重置状态并重新获取数据
  resetLoadingState()
  getScheduleTaskList(false)
}

// 状态重置机制
const resetLoadingState = () => {
  console.log('=== 重置加载状态 ===')
  loading.value = false
  finished.value = false
  console.log('状态重置完成 - loading:', loading.value, 'finished:', finished.value)
}

// 页面挂载时获取数据
onMounted(() => {
  console.log('=== 计划任务页面已挂载 ===')

  // 滚动到页面顶部
  nextTick(() => {
    window.scrollTo(0, 0)
    document.documentElement.scrollTop = 0
    document.body.scrollTop = 0
  })

  // 初始化状态
  resetLoadingState()

  // 首次加载数据
  getScheduleTaskList(false)
})
</script>

<style lang="scss" scoped>
.scheduled-task-container {
  min-height: 100%;
  background: #f7f8fa;
  padding-bottom: 24px;
}

// 新增按钮容器
.add-button-container {
  padding: 16px;
  padding-top: 8px;

  .add-task-button {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    cursor: pointer;
    transition: all 0.3s ease;

    &:active {
      background: #f2f3f5;
      transform: scale(0.98);
    }

    .add-icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      font-size: 24px;
      color: #323233;
      background: #fff;
    }

    .add-text {
      font-size: 16px;
      color: #323233;
      font-weight: 500;
    }
  }
}

// 骨架屏容器
.skeleton-container {
  padding: 16px;

  .skeleton-item {
    margin-bottom: 16px;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 任务列表容器
.task-list {
  padding: 16px;
  padding-top: 0;
}

// 任务项样式
.task-item {
  padding: 16px;
  margin-bottom: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    background: #f2f3f5;
    transform: scale(0.98);
  }

  .task-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    // 左侧内容区域
    .task-left {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;

      .task-name {
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        color: #323233;
        margin-bottom: 12px;
        word-wrap: break-word;
        word-break: break-all;
      }

      .task-details {
        flex: 1;
        display: flex;
        flex-direction: column;

        .task-detail-row {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          font-size: 14px;
          line-height: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .detail-label {
            color: #969799;
            font-weight: normal;
            flex-shrink: 0;
            min-width: auto;
            margin-right: 4px;
            white-space: nowrap;
          }

          .detail-value {
            color: #969799;
            font-weight: normal;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    // 右侧状态标签
    .task-right {
      flex-shrink: 0;
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;
      max-width: 80px; // 限制最大宽度

      .status-tag {
        border-radius: 12px;
        padding: 4px 8px; // 减少内边距
        font-size: 12px;
        font-weight: 500;
        white-space: nowrap; // 防止文字换行
        max-width: 100%; // 确保不超出父容器
        overflow: hidden;
        text-overflow: ellipsis;

        // 执行中状态 - 绿色
        &.van-tag--success {
          background: rgba(7, 193, 96, 0.1);
          color: #07C160;
          border: none;
        }

        // 未开始状态 - 灰色
        &.van-tag--default {
          background: #F2F3F5;
          color: #969799;
          border: none;
        }

        // 已终止状态 - 橙色
        &.van-tag--warning {
          background: rgba(237, 106, 12, 0.1);
          color: #ED6A0C;
          border: none;
        }
      }
    }
  }
}

// 搜索框样式调整
:deep(.van-search) {
  background: #fff;
  padding: 12px 16px;
  margin-bottom: 8px;

  .van-search__content {
    border-radius: 8px;
    background: #f7f8fa;
  }
}
</style>