import { defineAsyncComponent } from "vue";
export const components = {
  install: function (vm) {
    const requireModules = import.meta.glob("../components/*/*.vue");
    for (const path in requireModules) {
      const result = path.match(/(y.*)\//);
      const modulesConent = requireModules[path];
      if(result){
        vm.component(result[1], defineAsyncComponent(modulesConent));
      }
    }
  },
};
