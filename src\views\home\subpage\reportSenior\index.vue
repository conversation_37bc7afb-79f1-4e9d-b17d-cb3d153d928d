<template>
  <div class="wraper-order-confirm">
    <div class="top-bg"></div>
    <div class="address-info meal-type">
      <div class="address-title">
        <van-cell title="选择用餐方式" :border="false" />
      </div>
      <div class="time-bottom address-title">
        <!-- <div class="title" style="width: 100px">用餐方式</div> -->
        <van-cell
          title="用餐方式"
          :value="typeList[data.dine_type]"
          is-link
          :border="false"
          @click="selectType"
        />
      </div>
    </div>

    <div class="address-info dish-list-block">
      <div class="dish-title">
        <div class="tip">订单信息</div>
        <!-- <van-button plain type="primary" size="small" round @click="goOrder"
          >继续点单</van-button
        > -->
      </div>
      <div :class="`dish-list ${data.isShowAll && 'show-all'}`">
        <div class="dish-item" v-for="(item, i) in data.selectList" :key="i">
          <div class="left">
            <van-image
              v-if="item.image"
              width="56"
              height="56"
              radius="4"
              :src="
                item.image ||
                'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
              "
            />
            <div
              v-else
              style="
                width: 56px;
                height: 56px;
                border-radius: 4px;
                background: #007fff;
                color: #fff;
                font-size: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              {{ item.title[0] }}
            </div>
          </div>
          <div class="right">
            <div>{{ item.title }}</div>
            <div class="dish-info">
              <div>{{ item.desc }}</div>
              <div>X{{ item.count || 1 }}</div>
              <div>￥{{ item.price * 1 * (item.count || 1) }}</div>
            </div>
          </div>
        </div>
      </div>
      <div style="text-align: center">
        <div class="show-all-button" @click="data.isShowAll = !data.isShowAll">
          <van-cell
            :title="data.isShowAll ? '收起' : '展示全部'"
            is-link
            :arrow-direction="data.isShowAll ? 'up' : 'down'"
          />
        </div>
      </div>
    </div>
    <div class="address-info" style="margin-top: 16px">
      <div style="margin: 16px 16px 0">订单备注</div>
      <van-field
        v-model="data.desc"
        rows="2"
        autosize
        label=""
        type="textarea"
        maxlength="50"
        placeholder="口味、偏好等"
        show-word-limit
        :disabled="!!data.orderId"
      />
    </div>
    <div class="goods-bar" v-if="!data.orderId">
      <div class="total">
        <!-- <van-icon
          size="34"
          :name="getImageUrl('/images/common/goodscar.png')"
          color="#1989fa"
          :badge="data.selectList.size"
          @click="data.showBottom = true"
        /> -->
        <span style="margin-left: 16px; font-size: 12px">实付金额￥</span>
        <span style="font-size: 17px">{{ total }}</span>
      </div>
      <van-button round type="primary" @click="onSettlementClick"
        >确定报餐</van-button
      >
    </div>
    <div v-else style="margin: 30px 16px" @click="onBackClick">
      <van-button style="dishplay: block; width: 100%" type="primary"
        >返回订单列表</van-button
      >
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, computed } from "vue";
import { showToast, closeToast, showLoadingToast } from "vant";
import dayjs from "dayjs";

import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
let typeList = ["堂食", "留餐", "打包"];
let data = reactive({
  desc: "",
  dine_type: 0,
  isShowAll: false,
  selectList: app.select_order_list || [],
});
const init = () => {
  if (!data.selectList.length) {
    data.selectList = [
      {
        title: app.report_data.repast_title,
        price: app.report_data.price,
        count: app.report_data.count || 1,
      },
    ];
  } else {
    if (app.report_data.count) {
      data.selectList.forEach((el) => {
        el.count = app.report_data.count;
      });
    }
  }
};
init();
let total = computed(() => {
  let total = 0;
  for (let el of data.selectList) {
    total += el.price * 1 * (el.count ? el.count * 1 : 1);
  }
  return total.toFixed(2);
});

const goOrder = () => {
  router.back();
};
const selectType = () => {
  proxy.$_dd.device.notification.actionSheet({
    title: "报餐", //标题
    cancelButton: "取消", //取消按钮文本
    otherButtons: typeList,
    onSuccess: (result) => {
      if (result.buttonIndex === -1) {
        return;
      }
      data.dine_type = result.buttonIndex;
      // post_reapst(postUrl, report_data);
    },
    onFail(err) {},
  });
};
function getImageUrl(name) {
  return new URL(name, import.meta.url).href;
}
function onBackClick() {
  router.push({
    path: "/bill",
  });
}
const onSettlementClick = () => {
  showLoadingToast({
    message: "报餐中...",
    forbidClick: true,
  });
  let postData = {
    dishess: JSON.stringify(
      Array.from(data.selectList).map((el) => ({
        id: el.id,
        title: el.title,
        count: el.count,
      }))
    ),
    repast_id: app.report_data.repast_id,
    window_id: app.report_data.window_id,
    repast_title: app.report_data.repast_title,
    window_title: app.report_data.window_title,
    date: app.report_data.date,
    desc: data.desc,
    //就餐类型 0 堂食 1 留餐 2 打包 3 外送
    dine_type: data.dine_type,
    count: app.report_data.count,
  };
  proxy
    .$post("apply/post_apply_add", postData)
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        showToast("报餐成功~");
        data.orderId = res.bill_id;
        setTimeout(() => {
          app.report_data=null
          closeToast();
        }, 3000);
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
      setTimeout(() => {
        closeToast();
      }, 3000);
    })
    .finally(() => {
      // setTimeout(() => {
      //   data.orderId = 6666;
      //   closeToast();
      // }, 3000);
    });
};
</script>

<style lang="scss">
.wraper-order-confirm {
  padding-bottom: 120px;
  .top-bg {
    background: linear-gradient(#007fff, #e4edf6);
    height: 250px;
  }

  .address-info {
    margin: 0 16px;
    // margin-top: -228px;
    border-radius: 8px;
    background: #ffffff;
    overflow: hidden;
    .address-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .van-cell__title {
        font-size: 15px;
      }
      .type {
        width: 150px;
        font-size: 14px;
        background: rgba(0, 0, 0, 0.16);
        display: flex;
        justify-content: space-between;
        padding: 1px;
        margin-right: 16px;
        border-radius: 4px;
        div {
          padding: 5px 8px;
          border-radius: 4px;
        }
      }
      .active {
        background: #fff;
      }
    }
    .time-bottom {
      .title {
        font-size: 14px;
        margin-left: 16px;
      }
      .van-cell__title {
        // text-align: right;
        font-size: 14px;
      }
    }
  }
  .dish-list-block {
    margin-top: 16px;

    .dish-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
    }
    .dish-list {
      max-height: 300px;
      .dish-item {
        padding: 12px;
        display: flex;
        .right {
          flex: 1;
          font-size: 14px;
          margin-left: 10px;
          .dish-info {
            margin-top: 4px;
            display: flex;
            justify-content: space-between;
            color: rgba(23, 26, 29, 0.4);
            font-size: 10px;
            div:nth-of-type(1) {
              width: 100px;
            }
            div:nth-of-type(3) {
              font-size: 14px;
              color: #171a1d;
            }
          }
        }
      }
    }
    .show-all {
      height: auto;
      max-height: none;
    }
    .show-all-button {
      background: #f2f2f6;
      border-radius: 50px;
      width: 90px;
      overflow: hidden;
      display: inline-block;
      .van-cell {
        background: #f2f2f6;
        padding: 2px 8px;
        color: rgba(23, 26, 29, 0.4);
        font-size: 10px;
      }
    }
  }
  .meal-type {
    margin-top: -228px;
  }
}
.goods-bar {
  padding: 0 12px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 58px;
  opacity: 1;
  background: #ffffff;
  box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
  display: flex;
  align-items: center;
  justify-content: space-between;
  .total {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
