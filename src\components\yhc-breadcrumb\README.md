# yhc-breadcrumb 面包屑组件

一个功能丰富的面包屑导航组件，支持手动配置和自动生成，提供完整的导航功能。

## 功能特性

- ✅ 支持手动配置面包屑项
- ✅ 支持根据路由自动生成
- ✅ 支持图标显示
- ✅ 支持自定义分隔符
- ✅ 支持点击导航
- ✅ 支持最大显示数量限制
- ✅ 支持首页显示
- ✅ 支持多种尺寸
- ✅ 支持禁用状态
- ✅ 完整的事件支持

## 基础用法

### 简单用法
```javascript
// 基础配置
const config = {
  key: "breadcrumb",
  items: [
    { text: "项目", path: "/project" },
    { text: "项目", path: "/project/list" },
    { text: "项目", path: "/project/detail" },
    { text: "项目" }, // 当前页面，不可点击
    { text: "当前页" }
  ]
};
```

### 自动生成
```javascript
const config = {
  key: "breadcrumb",
  autoGenerate: true, // 根据路由自动生成
  showHome: true // 显示首页
};
```

### 带图标
```javascript
const config = {
  key: "breadcrumb",
  items: [
    { text: "首页", path: "/", icon: "home-o" },
    { text: "项目管理", path: "/project", icon: "setting-o" },
    { text: "项目详情", path: "/project/detail", icon: "records-o" },
    { text: "当前页面" }
  ]
};
```

### 自定义分隔符
```javascript
const config = {
  key: "breadcrumb",
  separator: "/", // 文本分隔符
  // 或使用图标分隔符
  separatorIcon: "arrow-right",
  items: [...]
};
```

## 配置参数

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| label | 标题文本 | String | - |
| key | 表单字段key | String | - |
| disabled | 是否禁用 | Boolean | false |
| block | 是否为块级元素 | Boolean | false |
| items | 面包屑数据 | Array | [] |
| textKey | 文本字段key | String | "text" |
| valueKey | 值字段key | String | "value" |
| pathKey | 路径字段key | String | "path" |
| iconKey | 图标字段key | String | "icon" |
| clickableKey | 可点击字段key | String | "clickable" |
| separator | 分隔符文本 | String | ">" |
| separatorIcon | 分隔符图标 | String | - |
| separatorSize | 分隔符尺寸 | String | "12px" |
| separatorColor | 分隔符颜色 | String | "#c8c9cc" |
| size | 尺寸 | String | "normal" |
| iconSize | 图标尺寸 | String | "14px" |
| activeColor | 激活颜色 | String | "#323233" |
| inactiveColor | 非激活颜色 | String | "#969799" |
| linkColor | 链接颜色 | String | "#1989fa" |
| onClick | 点击回调 | Function | null |
| description | 描述文本 | String | - |
| maxItems | 最大显示项数 | Number | 0 |
| showHome | 是否显示首页 | Boolean | true |
| homeText | 首页文本 | String | "首页" |
| homePath | 首页路径 | String | "/" |
| autoGenerate | 是否自动生成 | Boolean | false |

## 尺寸类型

### size 尺寸
- `small`: 小尺寸
- `normal`: 正常尺寸（默认）
- `large`: 大尺寸

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| click | 点击面包屑项时触发 | (value, item, index) |

## 使用示例

### 在表单中使用
```vue
<template>
  <yhc-form :config="formConfig" :form="form" />
</template>

<script setup>
const form = reactive({
  breadcrumb: ""
});

const formConfig = {
  form: [
    {
      type: "yhc-breadcrumb",
      key: "breadcrumb",
      label: "当前位置",
      items: [
        { text: "首页", path: "/" },
        { text: "项目管理", path: "/project" },
        { text: "项目详情" }
      ]
    }
  ]
};
</script>
```

### 直接使用
```vue
<template>
  <yhc-breadcrumb :config="config" :form="form" @click="handleClick" />
</template>

<script setup>
const form = reactive({
  breadcrumb: ""
});

const config = {
  key: "breadcrumb",
  label: "基本用法 (basic.tsx)",
  items: [
    { text: "项目", path: "/project", clickable: true },
    { text: "项目", path: "/project/list", clickable: true },
    { text: "项目", path: "/project/detail", clickable: true },
    { text: "项目" },
    { text: "当前页" }
  ],
  onClick: (value, item, index) => {
    console.log("面包屑点击:", value, item, index);
  }
};

const handleClick = (value, item, index) => {
  console.log("事件回调:", value, item, index);
};
</script>
```

## 高级用法

### 限制显示数量
```javascript
const config = {
  key: "breadcrumb",
  maxItems: 4, // 最多显示4项，超出会显示省略号
  items: [
    { text: "首页", path: "/" },
    { text: "一级页面", path: "/level1" },
    { text: "二级页面", path: "/level1/level2" },
    { text: "三级页面", path: "/level1/level2/level3" },
    { text: "四级页面", path: "/level1/level2/level3/level4" },
    { text: "当前页面" }
  ]
};
// 显示为: 首页 > ... > 三级页面 > 四级页面 > 当前页面
```

### 自动根据路由生成
```javascript
const config = {
  key: "breadcrumb",
  autoGenerate: true,
  showHome: true,
  homeText: "控制台",
  homePath: "/dashboard"
};
// 会根据当前路由路径自动生成面包屑
```

### 自定义样式
```javascript
const config = {
  key: "breadcrumb",
  size: "large",
  separatorIcon: "arrow-right",
  separatorColor: "#1989fa",
  linkColor: "#07c160",
  items: [...]
};
```

### 禁用某些项
```javascript
const config = {
  key: "breadcrumb",
  items: [
    { text: "首页", path: "/", clickable: true },
    { text: "项目管理", clickable: false }, // 不可点击
    { text: "项目详情", path: "/project/detail", clickable: true },
    { text: "当前页面" } // 最后一项默认不可点击
  ]
};
```

## 注意事项

1. 组件会自动注册到全局，可以直接在模板中使用
2. 最后一项默认为当前页面，不可点击
3. 支持路由自动跳转，需要配置 `pathKey` 字段
4. 自动生成模式会根据当前路由路径生成面包屑
5. 超出最大显示数量时会自动添加省略号
6. 组件完全响应式，支持动态更新配置和数据
