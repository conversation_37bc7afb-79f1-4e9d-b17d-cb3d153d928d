<template>
  <div class="basic-info-section">
    <!-- 规则名称 -->
    <div class="form-group">
      <van-cell-group inset>
        <van-field
          v-model="localFormData.title"
          label="规则名称"
          placeholder="请填写消费规则名称"
          required
          :disabled="loading"
          label-width="120px"
        />
      </van-cell-group>
    </div>

    <!-- 临时方案 -->
    <div class="form-group">
      <van-cell-group inset>
        <van-cell title="临时方案" :value="localFormData.is_temporary ? '是' : '否'">
          <template #right-icon>
            <van-switch 
              v-model="localFormData.is_temporary" 
              :disabled="loading"
              @change="handleTemporaryChange"
            />
          </template>
        </van-cell>
        
        <!-- 失效时间 - 当开启临时方案时显示 -->
        <van-field
          v-if="localFormData.is_temporary"
          v-model="localFormData.expire_time"
          label="失效时间"
          placeholder="请选择失效时间"
          type="datetime-local"
          required
          :disabled="loading"
        />
      </van-cell-group>
    </div>


  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:formData'])

// 本地表单数据
const localFormData = computed({
  get: () => props.formData,
  set: (value) => emit('update:formData', value)
})

// 处理临时方案切换
const handleTemporaryChange = (value) => {
  if (!value) {
    // 关闭临时方案时清空失效时间
    localFormData.value.expire_time = ''
  }
}
</script>

<style lang="scss" scoped>
.basic-info-section {
  .form-group {
    margin-bottom: 12px;
  }
  
  .van-cell-group {
    margin: 0 16px;
  }
}
</style>
