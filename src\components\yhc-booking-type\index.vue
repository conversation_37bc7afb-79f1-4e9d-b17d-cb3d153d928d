<template>
  <div class="wrapper-booking-type" :class="{ 'required-field': config.required }">
    <!-- 预定方式下拉选择器 -->
    <van-field
      v-model="displayValue"
      is-link
      :name="config.key"
      :label="config.label"
      :type="config.type"
      :placeholder="config.placeholder + config.label"
      :required="config.required"
      :rules="config.rules"
      :disabled="config.disabled"
      :label-width="config.labelWidth"
      :border="config.border"
      readonly
      input-align="left"
      @click="onClick"
    />

    <!-- 下拉选择弹窗 -->
    <van-popup
      v-model:show="showPopup"
      :round="config.popup.round"
      :position="config.popup.position"
      :style="config.popup.style"
      :closeable="config.popup.closeable"
    >
      <!-- 弹窗标题栏 -->
      <div class="top-block single-title">
        <span></span>
        <span class="title-text">{{ config.label }}</span>
        <van-icon @click="showPopup = false" name="cross" size="18" color="#969799" />
      </div>

      <!-- 选项列表 -->
      <div class="list-content">
        <div
          v-for="(option, index) in config.options"
          :key="'option-' + index"
          class="list-item single-mode"
          @click="onOptionClick(option)"
        >
          <div class="item-content">
            <div class="item-title">{{ option.label }}</div>
          </div>
          <div class="check-icon">
            <van-icon
              v-if="props.form[config.key] === option.value"
              name="success"
              size="16"
              color="#007fff"
            />
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 条件显示的子组件 -->
    <!-- 待他人预定 - 显示选人组件 -->
    <div v-if="props.form[config.key] === '待他人预定'" class="sub-component">
      <yhc-select-user 
        :config="selectUserConfig" 
        :form="props.form" 
        @change="onSelectUserChange"
      />
    </div>

    <!-- 预定多份 - 显示份数输入 -->
    <div v-if="props.form[config.key] === '预定多份'">
      <van-field
        v-model="props.form[config.quantityKey]"
        :name="config.quantityKey"
        label="份数"
        type="number"
        placeholder="请输入份数"
        :rules="quantityRules"
        :disabled="config.disabled"
      >
        <template #right-icon>
          <span class="unit-text">份</span>
        </template>
      </van-field>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { deepAssign } from "@/untils";

// 默认配置
let config = {
  // 基础配置
  label: "预定方式",         // 字段标签 (字符串) - 显示在输入框左侧的标签文字
  type: "text",              // 字段类型 (字符串) - 表单项类型，通常为"text"
  key: "booking_type",       // 字段名 (字符串) - 表单数据中的字段名
  quantityKey: "quantity",   // 份数字段名 (字符串) - 预定多份时的数量字段名
  userKey: "selected_user",  // 用户字段名 (字符串) - 待他人预定时的用户字段名
  placeholder: "请选择",      // 占位符 (字符串) - 未选择时显示的提示文字
  required: false,           // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,           // 是否禁用 (布尔值) - true: 禁用选择, false: 可正常选择
  rules: [],                 // 验证规则 (数组) - 表单验证规则配置
  border: true,              // 是否显示边框 (布尔值) - true: 显示下边框, false: 无边框
  labelWidth: "",            // 标签宽度 (字符串) - 如"100px"

  // 弹窗配置
  popup: {
    round: true,             // 是否圆角弹窗 (布尔值) - true: 圆角样式, false: 直角样式
    position: "bottom",      // 弹窗位置 (字符串) - "bottom": 底部弹出, "center": 居中
    style: { height: "50vh", overflow: "hidden" }, // 弹窗样式 (对象) - CSS样式对象
    closeable: false,        // 是否显示关闭按钮 (布尔值) - true: 显示X按钮, false: 不显示
  },

  // 选项配置
  options: [
    { value: "个人预定", label: "个人预定", disabled: false },
    { value: "预定多份", label: "预定多份", disabled: false },
    { value: "待他人预定", label: "待他人预定", disabled: false }
  ],

  // 默认值
  defaultValue: "个人预定",   // 默认选中值 (字符串) - 组件初始化时的默认选中项

  // 选人组件配置
  selectUserConfig: {
    label: "人员",
    placeholder: "请选择人员",
    multiple: false,         // 单选模式
    required: true,
    rules: [{ required: true, message: "请选择人员" }]
  },

  // 份数输入配置
  quantityConfig: {
    min: 1,                  // 最小份数
    max: 99,                 // 最大份数
    defaultQuantity: 1,      // 默认份数
    required: true,
    rules: [
      { required: true, message: "请输入份数" },
      { pattern: /^[1-9]\d*$/, message: "请输入有效的份数" }
    ]
  }
};

// 定义props
const props = defineProps({
  config: Object,
  form: Object,
});

// 定义事件
const emits = defineEmits(["change"]);

// 合并配置
props.config && deepAssign(config, props.config);

// 弹窗显示状态
const showPopup = ref(false);

// 初始化表单值
if (props.form[config.key] === undefined || props.form[config.key] === null || props.form[config.key] === '') {
  props.form[config.key] = config.defaultValue;
}

// 显示值计算属性
const displayValue = computed(() => {
  const selectedOption = config.options.find(option => option.value === props.form[config.key]);
  return selectedOption ? selectedOption.label : '';
});

// 选人组件配置
const selectUserConfig = computed(() => ({
  ...config.selectUserConfig,
  key: config.userKey
}));

// 份数验证规则
const quantityRules = computed(() => [
  ...config.quantityConfig.rules,
  {
    validator: (value) => {
      const num = parseInt(value);
      if (num < config.quantityConfig.min || num > config.quantityConfig.max) {
        return `份数应在${config.quantityConfig.min}-${config.quantityConfig.max}之间`;
      }
      return true;
    }
  }
]);

// 点击输入框打开弹窗
const onClick = () => {
  if (!config.disabled) {
    showPopup.value = true;
  }
};

// 选择选项
const onOptionClick = (option) => {
  if (!option.disabled && !config.disabled) {
    props.form[config.key] = option.value;

    // 关闭弹窗
    showPopup.value = false;

    // 触发变化事件
    emits("change", {
      component: "yhc-booking-type",
      key: config.key,
      value: option.value,
      form: props.form
    });

    console.log("预定方式变化---->", option.value, props.form);
  }
};

// 监听预定方式变化，初始化相关字段
watch(() => props.form[config.key], (newValue, oldValue) => {
  // 清理之前的数据
  if (oldValue === '待他人预定') {
    props.form[config.userKey] = null;
  }
  if (oldValue === '预定多份') {
    props.form[config.quantityKey] = null;
  }

  // 初始化新选项的默认值
  if (newValue === '预定多份') {
    props.form[config.quantityKey] = config.quantityConfig.defaultQuantity;
  }
  if (newValue === '待他人预定') {
    props.form[config.userKey] = null;
  }
}, { immediate: true });

// 选人组件变化事件
const onSelectUserChange = (data) => {
  console.log("选择人员变化---->", data);

  emits("change", {
    component: "yhc-booking-type",
    key: config.userKey,
    value: data.form[config.userKey],
    form: props.form,
    subComponent: "yhc-select-user"
  });
};
</script>

<style lang="scss" scoped>
.wrapper-booking-type {
  &.required-field {
    .van-field {
      --van-field-label-margin-right: 20px;

      .van-field__label {
        margin-left: -8px !important;
      }
    }
  }

  .van-field {
    font-size: 16px;
    padding: 16px;
  }

  .top-block {
    padding: 16px 32px;
    display: flex;
    justify-content: space-between;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;

    &.single-title {
      .title-text {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }
  }

  .list-content {
    height: calc(100% - 50px);
    margin-top: 50px;
    overflow: scroll;

    .list-item {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #f5f5f5;
      cursor: pointer;

      &:last-child {
        border-bottom: none;
      }

      // 单选模式样式
      &.single-mode {
        justify-content: space-between;

        .item-content {
          flex: 1;

          .item-title {
            font-size: 16px;
            color: #333;
            line-height: 1.4;
          }
        }

        .check-icon {
          margin-left: 12px;
          width: 20px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}

.sub-component {
  margin-top: 8px;
  margin-bottom: 8px;
  //border-left: 2px solid #f0f0f0;
}

.unit-text {
  color: #969799;
  font-size: 14px;
}
</style>
