<template>
  <div class="wrapper">
    <yhc-form :config="config" />
  </div>
</template>
<script setup>
import { ref } from "vue";
let config = {
  curl: {
    add: "/plan/post_add",
    info: "/plan/get_info",
    edit: "/plan/post_modify",
  },
  postData: {},
  search: {},

  groupForm: [
    [0, 1],
    [1, 4],
    [4, 6],
    [6, 10],
    [10, 20],
  ],
  form: [
    {
      disabled: true,
      label: "方案名称",
      key: "title",
      component: "yhc-input",
      disabled: true,
      rules: [{ required: true, message: "请填写方案名称" }],
      desc:"方案范围"
    },
    {
      disabled: true,
      label: "临时方案",
      key: "temporary",
      component: "yhc-switch",
      disabled: true,
      rules: [{ required: false, message: "临时方案" }],
      child: {
        showMode: true,
        form: [
           {
            disabled: true,
            label: "截止时间",
            type: "datetime",
            key: "end_time",
            component: "yhc-picker-date",
            disabled: true,
            rules: [{ required: true, message: "请填写截止时间" }],
          },
        ],
      },
    },
    {
      disabled: true,
      label: "全部人员",
      key: "all_user",
      component: "yhc-switch",
      disabled: true,
      rules: [{ required: false, message: "请选择用户" }],
      child: {
        showMode: false,
        form: [
          {
            disabled: true,
            label: "部分人员",
            key: "userlst",
            component: "yhc-select-user",
            disabled: true,
            rules: [{ required: true, message: "请选择用户" }],
          },
        ],
      },
    },
    {
      disabled: true,
      label: "全部餐时",
      key: "all_repast",
      component: "yhc-switch",
      rules: [{ required: false, message: "请选择餐时" }],
      child: {
        showMode: false,
        form: [
          {
            disabled: true,
            label: "餐时",
            key: "repasts",
            component: "yhc-picker",
            rules: [{ required: true, message: "请选择餐时" }],
            opts: {
              url: "/repast/get_all",
              postData: {},
              merge: false,
              multiple: true,
              text_key: "title",
              contrast_key: "id",
              keyMap: [{ title: "title", id: "id" }],
              defaultList: [],
            },
            card: {
              slotMap: {
                desc: [
                  {
                    title: "开始时间：",
                    key: "create_time",
                  },
                  {
                    title: "持续时间：",
                    key: "continued_hour",
                  },
                ],
              },
              title: "title",
            },
          },
        ],
      },
    },
    {
      disabled: true,
      label: "全部窗口",
      key: "all_window",
      component: "yhc-switch",
      rules: [{ required: false, message: "请选择窗口" }],
      child: {
        showMode: false,
        form: [
          {
            disabled: true,
            label: "窗口",
            key: "windows",
            component: "yhc-picker",
            rules: [{ required: true, message: "请选择窗口" }],
            opts: {
              url: "/window/get_all",
              postData: {},
              merge: false,
              multiple: true,
              text_key: "title",
              contrast_key: "id",
              keyMap: [{ title: "title", id: "id" }],
              defaultList: [],
            },
            card: {
              // slotMap: {
              //   desc: [
              //     {
              //       title: "收银机：",
              //       key: "device_cashier_titles",
              //     },
              //     {
              //       title: "消费设备：",
              //       key: "device_consumption_titles",
              //     },
              //     {
              //       title: "打印机：",
              //       key: "device_printer_titles",
              //     },
              //   ],
              // },
              desc: "device_cashier_titles",
              title: "title",
              // thumb: "https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg",
            },
          },
        ],
      },
    },

    {
      disabled: true,
      label: "就餐模式",
      key: "model",
      component: "yhc-picker",
      rules: [{ required: true, message: "请选择就餐模式" }],
      opts: {
        url: "",
        postData: {},
        merge: true,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: "id",
        defaultList: [
          {
            title: "固定金额",
            id: 0,
            detail: "个人报餐就餐",
          },
          {
            title: "自定义付款",
            id: 1,
            detail: "员工输入金额付款",
          },
          {
            title: "自定义收款",
            id: 2,
            detail: "厨师输入金额，员工付款",
          },
          {
            title: "点餐",
            id: 3,
            detail: "自定义点餐",
          },
        ],
      },
      card: {
        slotMap: {
          desc: [
            {
              title: "说明：",
              key: "detail",
            },
            // {
            //   title: "适用：",
            //   key: "scoped",
            // },
          ],
        },
        title: "title",
        // thumb: "https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg",
      },
    },

    {
      disabled: true,
      label: "就餐类型",
      key: "type",
      component: "yhc-picker",
      rules: [{ required: true, message: "请选择就餐类型" }],
      opts: {
        url: "",
        postData: {},
        merge: true,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: "id",
        defaultList: [
          {
            title: "普通就餐",
            id: 0,
            detail: "该就餐人员无需报餐，刷脸即可就餐",
          },
          {
            title: "报餐就餐",
            id: 1,
            detail: "人员需要提前报餐，并刷脸就餐",
          },
          {
            title: "考勤就餐",
            id: 2,
            detail: "只有考勤人员才可就餐",
          },
        ],
      },
      card: {
        slotMap: {
          desc: [
            {
              title: "说明：",
              key: "detail",
            },
          ],
        },
        title: "title",
      },
    },

    {
      disabled: true,
      label: "餐补方案",
      key: "subsidys",
      component: "yhc-picker",
      rules: [{ required: false, message: "请选择餐补方案" }],
      opts: {
        url: "/subsidy/get_all",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: [
          {
            title: "title",
            id: "id",
          },
        ],
        defaultList: [],
      },
      card: {
        slotMap: {
          desc: [
            {
              title: "开始时间：",
              key: "starttime",
            },
            {
              title: "持续时间：",
              key: "endtime",
            },
          ],
        },
        title: "title",
      },
    },
    {
      disabled: true,
      label: "考勤就餐",
      key: "attendances",
      component: "yhc-picker",
      rules: [{ required: false, message: "请选择考勤就餐" }],
      opts: {
        url: "/attendance/get_all",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: [
          {
            title: "title",
            id: "id",
          },
        ],
        defaultList: [],
      },
      card: {
        slotMap: {
          desc: [
            // {
            //   title: "考勤类型：",
            //   key: "shift_titles",
            // },
            {
              title: "开始时间：",
              key: "starttime",
            },
            {
              title: "结束时间：",
              key: "endtime",
            },
          ],
        },
        title: "title",
      },
    },
    {
      disabled: true,
      label: "报餐方案",
      key: "applys",
      component: "yhc-picker",
      rules: [{ required: false, message: "请选择报餐方案" }],
      opts: {
        url: "/apply/get_all",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: [
          {
            title: "title",
            id: "id",
          },
        ],
        defaultList: [],
      },
      card: {
        // slotMap: {
        //   desc: [
        //     {
        //       title: "关联计划：",
        //       key: "associate_plan_titles",
        //     },
        //   ],
        // },
        title: "title",
      },
    },
    {
      disabled: true,
      label: "点餐方案",
      key: "orders",
      component: "yhc-picker",
      rules: [{ required: false, message: "请选择点餐方案" }],
      opts: {
        url: "/order/get_all",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: [
          {
            title: "title",
            id: "id",
          },
        ],
        defaultList: [],
      },
      card: {
        // num: "2",
        // price: "2.00",
        // desc: "associate_plan_titles",
        title: "title",
        // thumb: "https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg",
      },
    },
    {
      disabled: true,
      label: "多次付款",
      key: "repeatedly",
      component: "yhc-picker",
      rules: [{ required: true, message: "请选择点餐方案" }],
      opts: {
        url: "",
        postData: {},
        merge: true,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: "id",
        defaultList: [
          {
            title: "否",
            id: 0,
            detail: "不开启",
            scoped: "所有人员",
          },
          {
            title: "开启",
            id: 1,
            detail: "一次报餐，多次付款就餐",
            scoped: "所有人员",
          },
          {
            title: "自定义付款",
            id: 2,
            detail: "员工输入金额付款",
            scoped: "所有人员",
          },
        ],
      },
      card: {
        slotMap: {
          desc: [
            {
              title: "说明：",
              key: "detail",
            },
            {
              title: "适用：",
              key: "scoped",
            },
          ],
        },
        title: "title",
        // thumb: "https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg",
      },
      child: {
        showMode: true,
        form: [
          {
            disabled: true,
            label: "次数上限",
            type: "number",
            "right-icon":"/次",
            key: "repeatedly_max",
            component: "yhc-input",
            rules: [{ required: false, message: "请填写次数上限" }],
          },
        ],
      },
    },
    {
      disabled: true,
      label: "额外加价",
      type: "number",
      key: "markup",
      "right-icon":"/元",
      component: "yhc-input",
      rules: [{ required: false, message: "请填写额外加价" }],
    },
    {
      disabled: true,
      label: "计费模式",
      key: "billing",
      component: "yhc-picker",
      rules: [{ required: false, message: "请选择计费模式" }],
      opts: {
        url: "",
        postData: {},
        merge: true,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: "id",
        defaultList: [
          {
            title: "普通计费",
            id: 0,
          },
          {
            title: "不计费",
            id: 1,
          },
          {
            title: "月底结算",
            id: 2,
          },
        ],
      },
      card: {
        // num: "2",
        // price: "2.00",
        // desc: "detail",
        title: "title",
        // thumb: "https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg",
      },
    },
    {
      disabled: true,
      label: "消费上限",
      type: "number",
      key: "consume_max",
      "right-icon":"/元",
      component: "yhc-input",
      rules: [{ required: false, message: "请填写消费上限" }],
      desc:"餐时实际消费上限，0为不限制",
        child: {
        showMode: true,
        form: [
          {
            disabled: true,
            label: "周期",
            key: "consume_max_cycle",
            component: "yhc-picker",
            rules: [{ required: true, message: "请选择周期" }],
            opts: {
              url: "",
              postData: {},
              merge: false,
              multiple: false,
              text_key: "title",
              contrast_key: "id",
              keyMap: "id",
              defaultList: [
                {
                  title: "每餐",
                  id: 0,
                },
                {
                  title: "每天",
                  id: 1,
                },
                {
                  title: "每周",
                  id: 2,
                },
                {
                  title: "每月",
                  id: 3,
                },
              ],
            },
            card: {
              // slotMap: {
              //   desc: [
              //     {
              //       title: "收银机：",
              //       key: "device_cashier_titles",
              //     },
              //     {
              //       title: "消费设备：",
              //       key: "device_consumption_titles",
              //     },
              //     {
              //       title: "打印机：",
              //       key: "device_printer_titles",
              //     },
              //   ],
              // },
              desc: "device_cashier_titles",
              title: "title",
              // thumb: "https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg",
            },
          },
          {
            disabled: true,
            label: "范围",
            key: "consume_max_range",
            component: "yhc-picker",
            rules: [{ required: true, message: "请选择范围" }],
            opts: {
              url: "",
              postData: {},
              merge: false,
              multiple: false,
              text_key: "title",
              contrast_key: "id",
              keyMap: "id",
              defaultList: [
                {
                  title: "餐厅内",
                  id: 0,
                },
                {
                  title: "企业内",
                  id: 1,
                },
              ],
            },
            card: {
              // slotMap: {
              //   desc: [
              //     {
              //       title: "收银机：",
              //       key: "device_cashier_titles",
              //     },
              //     {
              //       title: "消费设备：",
              //       key: "device_consumption_titles",
              //     },
              //     {
              //       title: "打印机：",
              //       key: "device_printer_titles",
              //     },
              //   ],
              // },
              desc: "device_cashier_titles",
              title: "title",
              // thumb: "https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg",
            },
          },
        ],
      },
    },

  ],
  button: {
    isShow:false
  },
};
</script>
<style lang="scss" scoped>
.wrapper {
  width: 100%;
  min-height: calc(100vh - 1px);
  border-top: 1px solid #f2f3f4;
}
</style>
