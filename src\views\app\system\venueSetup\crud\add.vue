<template>
    <div class="home-container">
        <!-- 修改按钮区域 -->
        <div v-if="route.query.id" class="edit-button-container">
            <!-- <van-button
                type="primary"
                size="small"
                @click="onEditClick"
                class="edit-button"
            >
                修改
            </van-button> -->
        </div>

        <!-- yhc-form 组件展示 -->
        <yhc-form :config="basicFormConfig"    pageType="add" @onSubmit="onBasicSubmit" />
    </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { showToast } from 'vant'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

console.log('route:', route)
const basicFormConfig = {
    button: {
        isShow: true,
    },
    postData: {
        // 如果有id参数，则为编辑模式
        ...(route.query.id ? { id: route.query.id } : {})
    },
    curl: {
        del:'/dininghall/post_del',
        add: '/dininghall/post_add', // 新增接口
        edit: '/dininghall/post_modify', // 编辑接口
        info: '/dininghall/get_info' // 获取详情接口（编辑时需要）
    },
    groupForm: [
        [0, 1],
        [1, 2],
        [2, 3],
        [3, 4],
        [4, 5],
        [5, 6],
        [6, 7],
    ],
    form: [
        {
            label: "场所名称",
            key: "title",
            component: "yhc-input",
            type: "text",
            placeholder: "请输入",
            required: true,
            rules: [{ required: true, message: "请填写场所名称" }],
        },
        {
            label: "档口合并",
            key: "merge_stall",
            component: "yhc-switch",
            required: false,
        },
        {
            label: "说明：开启后，所有档口合并展示，支持统一发布菜品、统一预定入口",
            component: "yhc-desc"
        },
        {
            label: "菜品免发布",
            key: "exempt_dishes_release",
            component: "yhc-switch",
            required: false,
        },
        {
            label: "说明：开启后，菜品无需手动发布，即可支持固定金额与自定义金额两种消费方式",
            component: "yhc-desc"
        },
        {
          label: "地址",
          key: "address",
          component: "yhc-input",
          type: "textarea",
          placeholder: "请输入",
          autosize: true,
          "show-word-limit": true,
          maxlength: 30,
        //   desc: "支持多行文本输入，带字数限制"
        },
        {
            label: "支持多行文本输入，带字数限制",
            component: "yhc-desc"
        },
    ]
}

// 修改按钮点击事件
const onEditClick = () => {
    // 这里可以切换到编辑模式或者跳转到编辑页面
    showToast('进入修改模式')
    // 如果需要跳转到编辑页面，可以使用：
    // router.push({ path: '/system/venueSetup/edit', query: { id: route.query.id } })
}

// 设置导航栏标题
const setTitle = () => {
    if (proxy && proxy.$_dd && proxy.$_dd.biz && proxy.$_dd.biz.navigation) {
        proxy.$_dd.biz.navigation.setTitle({
            title: route.query.id ? 'D3组' : '新增场所',
        })
    }
}

// 移除右侧按钮
const setRight = () => {
    if (proxy && proxy.$_dd && proxy.$_dd.biz && proxy.$_dd.biz.navigation) {
        proxy.$_dd.biz.navigation.setRight({
            text: '',
            control: true,
            onSuccess: function () {},
            onFail: function () {}
        })
    }
}

// 页面初始化时设置导航栏
setTitle()
setRight()

// // 表单提交处理函数
const onBasicSubmit = (data) => {
    console.log('基础表单提交:', data)
    showToast('基础表单提交成功')
}
</script>

<style lang="scss" scoped>
.home-container {
    padding: 0;
    min-height: 100vh;
    position: relative;
    // background: #f7f8fa;
}

// 修改按钮容器样式
.edit-button-container {
    position: absolute;
    top: 60px;
    right: 16px;
    z-index: 10;

    .edit-button {
        min-width: 60px;
        height: 28px;
        border-radius: 14px;
        font-size: 12px;
        padding: 0 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:active {
            opacity: 0.8;
        }
    }
}
</style>
