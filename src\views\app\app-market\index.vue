<template>
  <div class="app-market-container">
    <!-- Banner轮播区域 -->
    <div class="banner-section">
      <van-swipe :autoplay="3000" indicator-color="white" :show-indicators="true" :height="120">
        <van-swipe-item v-for="(banner, index) in bannerList" :key="index">
          <div class="banner-item" :style="{ backgroundColor: banner.color }">
            <div class="banner-content">
              <h3>{{ banner.title }}</h3>
              <p>{{ banner.subtitle }}</p>
            </div>
          </div>
        </van-swipe-item>
      </van-swipe>
    </div>

    <!-- 应用列表区域 -->
    <div class="list-section">
      <yhc-list :config="listConfig" ref="listRef" @onClickTab="onClickTab">
        <!-- 自定义应用卡片模板 -->
        <template #default="{ item, index }">
          <div class="app-card" @click="handleAppClick(item)">
            <!-- 左侧图标区域 -->
            <div class="app-icon">
              <div class="icon-placeholder">
                <span>主图</span>
              </div>
            </div>

            <!-- 右侧信息区域 -->
            <div class="app-info">
              <div class="app-title">{{ item.title }}</div>
              <div class="app-desc">{{ item.description }}</div>
              <div class="app-price">{{ item.price }}</div>
            </div>
          </div>
        </template>
      </yhc-list>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { showToast } from 'vant'

// Banner数据
const bannerList = ref([
  {
    title: 'Banner 1',
    subtitle: '应用市场精选推荐',
    color: '#1989fa'
  },
  {
    title: 'Banner 2',
    subtitle: '企业级解决方案',
    color: '#07c160'
  },
  {
    title: 'Banner 3',
    subtitle: '高效办公工具',
    color: '#ff976a'
  }
])

// 列表组件引用
const listRef = ref(null)

// yhc-list配置
const listConfig = reactive({
  // 搜索配置
  search: {
    isShow: true,
    isShowPopup: false, // 禁用筛选弹窗
    placeholder: '搜索应用...',
    key: 'search' // 搜索字段名
  },

  // 分类标签配置
  tabs: {
    isShow: true,
    sticky: true,
    swipeable: false,
    list: [
      { text: '行业专属', key: 'industry' },
      { text: '进销存ERP', key: 'erp' },
      { text: '信息模块', key: 'info' },
      { text: '其他', key: 'other' }
    ]
  },

  // 数据配置
  curl: {
    ls: '' // 使用模拟数据
  },
  mockData: [], // 将在下面动态设置

  // 骨架屏配置
  skeleton: {
    isShow: true,
    count: 6,
    row: 3,
    rowWidth: ['60%', '100%', '40%'],
    avatar: true,
    duration: 1500
  },

  // 按钮配置
  button: {
    isShow: false
  },

  // 弹窗配置 (禁用筛选功能)
  popup: {
    round: true,
    position: "bottom",
    style: { height: "50vh" },
    closeable: false
  },

  // 筛选配置 (禁用)
  filter: {
    isShow: false
  }
})

// 模拟应用数据
const mockAppData = {
  industry: [
    {
      id: 1,
      title: '进销存管理全链条流程',
      description: '货物 库存 发票销售',
      price: '18.98起',
      category: 'industry'
    },
    {
      id: 2,
      title: '会议群餐',
      description: '会议餐',
      price: '18.98起',
      category: 'industry'
    },
    {
      id: 3,
      title: '进销存管理全链条流程',
      description: '货物 库存 发票销售',
      price: '18.98起',
      category: 'industry'
    }
  ],
  erp: [
    {
      id: 4,
      title: 'ERP管理系统',
      description: '企业资源规划管理',
      price: '28.98起',
      category: 'erp'
    },
    {
      id: 5,
      title: '库存管理系统',
      description: '智能库存管理',
      price: '19.98起',
      category: 'erp'
    }
  ],
  info: [
    {
      id: 6,
      title: '信息发布平台',
      description: '企业信息发布管理',
      price: '15.98起',
      category: 'info'
    },
    {
      id: 7,
      title: '数据统计分析',
      description: '业务数据分析工具',
      price: '25.98起',
      category: 'info'
    }
  ],
  other: [
    {
      id: 8,
      title: '办公工具集',
      description: '日常办公辅助工具',
      price: '12.98起',
      category: 'other'
    }
  ]
}

// 处理应用点击
const handleAppClick = (item) => {
  showToast(`点击了应用: ${item.title}`)
  // 这里可以跳转到应用详情页
  // router.push(`/app-detail/${item.id}`)
}

// 处理分类切换事件
const onClickTab = (tabData) => {
  console.log('切换分类:', tabData)
  const tabKey = listConfig.tabs.list[tabData.name]?.key || 'industry'
  setMockDataForCurrentTab(tabKey)
}

// 设置当前分类的模拟数据
const setMockDataForCurrentTab = (tabKey = 'industry') => {
  const currentData = mockAppData[tabKey] || []
  listConfig.mockData = currentData

  // 重新加载列表数据
  if (listRef.value && listRef.value.refresh) {
    listRef.value.refresh()
  }
}

// 初始化时设置默认数据
setMockDataForCurrentTab('industry')

// 页面挂载后的处理
onMounted(() => {
  console.log('应用市场页面已挂载')
})
</script>

<style lang="scss" scoped>
.app-market-container {
  min-height: 100%;
  background: #ffffff;
  padding-bottom: 24px; // 为底部导航留出空间
}

// Banner区域样式
.banner-section {
  margin: 0;
  padding-top: 16px;

  .banner-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 120px;
    position: relative;

    .banner-content {
      text-align: center;
      color: white;

      h3 {
        font-size: 18px;
        font-weight: 500;
        margin: 0 0 8px 0;
      }

      p {
        font-size: 14px;
        margin: 0;
        opacity: 0.9;
      }
    }
  }
}

// 列表区域样式
.list-section {
  margin-top: 0;

  // 重写yhc-list的样式
  :deep(.yhc-list) {
    background: transparent;

    // 搜索栏样式
    .van-search {
      background: white;
      padding: 12px 16px;
      margin: 0 16px 16px 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    // 隐藏筛选按钮
    .filter-button,
    .search-filter-btn {
      display: none !important;
    }

    // 分类标签样式
    .van-tabs {
      background: white;
      margin: 0 16px 16px 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .van-tabs__wrap {
        border-radius: 8px 8px 0 0;
      }

      .van-tab {
        font-size: 14px;
        font-weight: 500;
      }

      .van-tabs__line {
        background: #1989fa;
        border-radius: 2px;
      }
    }

    // 列表容器样式
    .list-container {
      padding: 0 16px;
      background: #ffffff;
      margin: 0 16px;
      border-radius: 12px 12px 0 0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      overflow: hidden;
    }
  }
}

// 应用卡片样式
.app-card {
  display: flex;
  align-items: center;
  padding: 16px;
  margin-bottom: 1px;
  background: white;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background: #f8f9fa;
  }

  &:active {
    background: #f0f0f0;
  }

  &:last-child {
    border-bottom: none;
  }

  .app-icon {
    margin-right: 16px;
    flex-shrink: 0;

    .icon-placeholder {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
    }
  }

  .app-info {
    flex: 1;
    min-width: 0; // 防止文字溢出

    .app-title {
      font-size: 16px;
      font-weight: 500;
      color: #323233;
      margin-bottom: 4px;
      line-height: 1.4;
      // 文字超出显示省略号
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .app-desc {
      font-size: 13px;
      color: #969799;
      margin-bottom: 8px;
      line-height: 1.3;
      // 最多显示两行
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .app-price {
      font-size: 16px;
      font-weight: 600;
      color: #ee0a24;

      &::before {
        content: '¥';
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .app-card {
    padding: 12px;

    .app-icon .icon-placeholder {
      width: 50px;
      height: 50px;
      font-size: 11px;
    }

    .app-info {
      .app-title {
        font-size: 15px;
      }

      .app-desc {
        font-size: 12px;
      }

      .app-price {
        font-size: 15px;
      }
    }
  }
}

// 深度选择器优化
:deep(.van-swipe) {
  .van-swipe__indicator {
    opacity: 0.6;

    &--active {
      opacity: 1;
    }
  }
}
</style>
