<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="add"
      @onSubmit="onBasicSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    dininghall_id:localStorage.getItem('dininghall'),
  },
  curl: {
    add: '/form_field/post_add', // 新增接口
    edit: '/form_field/post_modify', // 编辑接口
    info: '/form_field/get_info' // 获取详情接口（编辑时需要）
  },
  groupForm: [
    [0, 2],
    [2, 3],
    [3, 4],
    [4, 5],
  ],
  form: [
     {
      label: "标题",
      key: "field_title",
      component: "yhc-input",
      required: true,
      rules: [{ required: true, message: "请填写标题" }],
    },
     {
      label: "提示文字",
      key: "placeholder",
      component: "yhc-input",
    },
    {
      label: "默认值",
      key: "default_value",
      component: "yhc-input",
    },
    {
      label: "必填",
      key: "is_required",
      component: "yhc-switch",
    },
  ]
}

const { proxy } = getCurrentInstance();
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: route.query.id ? '修改设计' : '新增设计',
  });
};
setRightA()
// // // 表单提交处理函数
// const onBasicSubmit = (data) => {
//   console.log('基础表单提交:', data)
//   showToast('基础表单提交成功')
// }
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
