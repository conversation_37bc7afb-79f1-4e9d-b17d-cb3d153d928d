<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="add"
      @onSubmit="onBasicSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'
const { proxy } = getCurrentInstance();

const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
        dininghall_id:localStorage.getItem('dininghall'),
    // 如果有id参数，则为编辑模式
    // ...(route.query.id ? { id: route.query.id } : {})
  },
  curl: {
    add: '/dishes_category/post_add', // 新增接口
    edit: '/dishes_category/post_modify', // 编辑接口
    info: '/dishes_category/get_info' // 获取详情接口（编辑时需要）
  },
  groupForm: [
    [0, 1],
    [1, 4]
  ],
  form: [
    {
      label: "分类名称",
      key: "title",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请填写分类名称" }],
    },
  ]
}


// // 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
// 恢复默认的proxy.$_dd.biz.navigation
const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: route.query.id ? '修改分类' : '新增分类',
  });
};
setRightA()
setRight()
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
