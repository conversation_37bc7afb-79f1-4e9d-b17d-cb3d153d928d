<template>
  <div class="wrapper">
    <van-field :name="config.key" :label="config.label" :required="config.required" :rules="config.rules"
      :disabled="config.disabled" :label-width="config.labelWidth" :border="config.border">
      <template #input>
        <van-checkbox-group v-model="props.form[config.key]" :direction="config.direction" :disabled="config.disabled"
          @change="onChange">
          <van-checkbox v-for="option in processedOptions" :key="option.value" :name="option.value"
            :disabled="option.disabled || config.disabled">
            {{ option.label }}
          </van-checkbox>
        </van-checkbox-group>
      </template>
    </van-field>
  </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, watch, computed } from "vue";
import { deepAssign } from "@/untils";
import { showToast } from "vant";

const { proxy } = getCurrentInstance();

// 默认配置
let config = {
  // 基础配置
  label: "复选框组",       // 字段标签 (字符串) - 显示在复选框组上方的标签文字
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"hobbies", "permissions"
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用所有选项, false: 可正常选择
  rules: [],               // 验证规则 (数组) - 表单验证规则配置
  defaultValue: '',        // 默认值 (字符串/数组) - 默认选中的选项值，支持单个值或数组

  // 样式配置
  border: true,            // 是否显示边框 (布尔值) - true: 显示边框, false: 无边框
  labelWidth: '',          // 标签宽度 (字符串) - 标签区域的宽度，如"80px", "100px"
  direction: "horizontal", // 排列方向 (字符串) - "horizontal": 水平排列, "vertical": 垂直排列
  shape: "square",         // 复选框形状 (字符串) - "square": 方形, "round": 圆形

  // 选项配置
  options: [],             // 选项数组 (数组) - 复选框选项，格式: [{ value: "1", label: "选项1", disabled: false }]
};

const props = defineProps({
  config: Object,
  form: Object,
});

// 合并配置
props.config && deepAssign(config, props.config);

// 初始化表单值和默认值处理
if (!props.form[config.key]) {
  props.form[config.key] = [];
}

// 处理默认值 - 确保默认值被选中
if (config.defaultValue) {
  const defaultValues = Array.isArray(config.defaultValue) ? config.defaultValue : [config.defaultValue];
  // 将默认值添加到表单值中（如果还没有的话）
  defaultValues.forEach(defaultVal => {
    if (!props.form[config.key].includes(defaultVal)) {
      props.form[config.key].push(defaultVal);
    }
  });
}

// 定义事件
const emit = defineEmits(["change"]);

// 计算处理后的选项（添加默认值不可取消选中的逻辑）
const processedOptions = computed(() => {
  if (!config.defaultValue) {
    return config.options;
  }

  const defaultValues = Array.isArray(config.defaultValue) ? config.defaultValue : [config.defaultValue];

  return config.options.map(option => {
    // 如果是默认值且已被选中，则在用户尝试取消选中时保持选中状态
    // 但不直接禁用，而是通过onChange事件处理来阻止取消选中
    return {
      ...option,
      // 保持原有的禁用状态，不额外禁用默认值选项
      // 因为我们通过onChange事件来处理默认值不可取消选中的逻辑
    };
  });
});

// 复选框组变化事件
const onChange = (value) => {
  // 如果有默认值，确保默认值不能被取消选中
  if (config.defaultValue) {
    const defaultValues = Array.isArray(config.defaultValue) ? config.defaultValue : [config.defaultValue];

    // 检查是否有默认值被取消选中
    const missingDefaults = defaultValues.filter(defaultVal => !value.includes(defaultVal));

    // 如果有默认值被取消选中，重新添加回去
    if (missingDefaults.length > 0) {
      missingDefaults.forEach(defaultVal => {
        if (!value.includes(defaultVal)) {
          value.push(defaultVal);
        }
      });
      // 更新表单值
      props.form[config.key] = [...value];
    }
  }

  emit("change", {
    component: "yhc-checkbox-group",
    key: config.key,
    value: value,
  });
};

// 监听表单值变化，确保数据同步和默认值保持
watch(() => props.form[config.key], (newValue) => {
  if (!Array.isArray(newValue)) {
    props.form[config.key] = [];
  }

  // 确保默认值始终存在
  if (config.defaultValue) {
    const defaultValues = Array.isArray(config.defaultValue) ? config.defaultValue : [config.defaultValue];
    let needsUpdate = false;

    defaultValues.forEach(defaultVal => {
      if (!props.form[config.key].includes(defaultVal)) {
        props.form[config.key].push(defaultVal);
        needsUpdate = true;
      }
    });

    // 如果有更新，触发重新渲染
    if (needsUpdate) {
      props.form[config.key] = [...props.form[config.key]];
    }
  }
}, { immediate: true });
</script>

<style lang="scss" scoped>
// .wrapper {
//   :deep(.van-checkbox-group) {
//     &--horizontal {
//       .van-checkbox {
//         margin-right: 16px;
//         margin-bottom: 8px;

//         &:last-child {
//           margin-right: 0;
//         }
//       }
//     }

//     &--vertical {
//       .van-checkbox {
//         margin-bottom: 12px;
//         display: block;

//         &:last-child {
//           margin-bottom: 0;
//         }
//       }
//     }
//   }

//   :deep(.van-checkbox__label) {
//     color: #323233;
//     font-size: 14px;
//     line-height: 20px;
//   }

//   :deep(.van-field__control) {
//     padding-top: 8px;
//     padding-bottom: 8px;
//   }
// }</style>
