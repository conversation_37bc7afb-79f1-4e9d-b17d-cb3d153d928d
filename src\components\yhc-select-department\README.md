# yhc-select-department 部门选择组件

基于 `yhc-select-user` 组件设计的部门选择组件，支持钉钉和企业微信环境下的部门选择功能。

## 功能特性

- ✅ 支持钉钉和企业微信环境
- ✅ 支持单选和多选模式
- ✅ 支持部门层级结构展示
- ✅ 集成表单验证
- ✅ 遵循项目 yhc- 组件规范

## 使用方式

### 1. 在表单中使用

```vue
<template>
  <yhc-form :config="formConfig" :form="formData" @onSubmit="handleSubmit" />
</template>

<script setup>
import { reactive } from 'vue';

const formData = reactive({
  department: null, // 部门选择结果
});

const formConfig = {
  form: [
    {
      component: "yhc-select-department",
      key: "department",
      label: "选择部门",
      placeholder: "请选择",
      multiple: true, // 支持多选
      maxDepartments: 10, // 最大选择数量
      rules: [{ required: true, message: '请选择部门' }]
    }
  ]
};

const handleSubmit = (data) => {
  console.log('提交的数据:', data);
  // data.department.departments 包含选中的部门信息
};
</script>
```

### 2. 直接使用组件

```vue
<template>
  <yhc-select-department 
    :config="departmentConfig" 
    :form="formData"
    @change="handleDepartmentChange"
  />
</template>

<script setup>
import { reactive } from 'vue';

const formData = reactive({
  selectedDepartment: null
});

const departmentConfig = {
  key: "selectedDepartment",
  label: "选择部门",
  placeholder: "请选择部门",
  multiple: false, // 单选模式
  border: true,
  disabled: false,
  rules: [{ required: true, message: '请选择部门' }]
};

const handleDepartmentChange = (form) => {
  console.log('部门选择变更:', form.selectedDepartment);
};
</script>
```

## 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| label | String | "选择部门" | 字段标签 |
| key | String | "" | 表单字段名 |
| placeholder | String | "请选择" | 占位符文本 |
| multiple | Boolean | true | 是否支持多选 |
| maxDepartments | Number | 100 | 最大选择部门数量 |
| border | Boolean | true | 是否显示边框 |
| disabled | Boolean | false | 是否禁用 |
| rules | Array | [] | 表单验证规则 |
| showOrgEcological | Boolean | true | 是否显示组织生态 |

## 数据结构

### 输入数据格式
```javascript
{
  departments: [
    {
      dept_id: "123456",
      name: "技术部",
      order: 1,
      parentId: "0"
    }
  ]
}
```

### 输出数据格式
```javascript
{
  departments: [
    {
      dept_id: "123456", // 部门ID
      name: "技术部",    // 部门名称
      order: 1,          // 排序
      parentId: "0"      // 父部门ID
    }
  ]
}
```

## 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| change | 部门选择变更时触发 | (form) 完整的表单数据 |

## 注意事项

1. 组件依赖钉钉 JSAPI，需要在钉钉或企业微信环境中使用
2. 需要配置正确的 corpId 和 appId
3. 组件会自动根据环境选择对应的API调用方式
4. 支持与项目现有的表单系统无缝集成

## 技术实现

- 基于 Vant UI 的 `van-field` 组件
- 使用钉钉 `complexChoose` API 进行部门选择
- 支持企业微信 `selectEnterpriseContact` API
- 遵循项目的组件设计模式和命名规范
