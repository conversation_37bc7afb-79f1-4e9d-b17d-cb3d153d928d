export function deepAssign() {
  let name, options, src, copy;
  let length = arguments.length;
  // 记录要复制的对象的下标
  let i = 1;
  // target默认是第一个参数
  let target = arguments[0] || {};
  // 如果target不是对象，我们是无法进行复制的，所以设为{}
  if (typeof target !== "object") {
    target = {};
  }
  // 循环遍历要复制的对象
  for (; i < length; i++) {
    // 获取当前对象
    options = arguments[i];
    // 要求不能为空 避免extend(a,,b)这种情况
    if (options != null) {
      for (name in options) {
        // 目标属性值
        src = target[name];
        // 要复制的对象的属性值
        copy = options[name];

        if (copy && typeof copy == "object" && !Array.isArray(copy)) {
          // 递归调用
          target[name] = deepAssign(src, copy);
        } else if (copy !== undefined) {
          target[name] = copy;
        }
      }
    }
  }

  return target;
}

export function handleJson(data, flag) {
  let keys = Object.keys(data);
  if (!flag) {
    keys.forEach((key) => {
      if (data[key] && typeof data[key] == "string") {
        if (data[key].includes("[") || data[key].includes("{")) {
          try {
            data[key] = JSON.parse(data[key]);
          } catch (e) {
            console.log(e);
          }
        }
      }
    });
  } else {
    //处理提交json格式
    keys.forEach((key) => {
      if (typeof data[key] == "object") {
        try {
          data[key] = JSON.stringify(data[key]);
        } catch (e) {
          console.log(e);
        }
      }
    });
  }
}
export function handleFormData(config, data, flag, form) {
  // console.log("表单处理前----1-config, data, flag, form--->", config, data, flag,form);
  //最后处理完成的结果数据res_obj
  data = { [config.key]: data };
  let res_obj = {};

  //处理表单参数
  try {
    handle_config(config);
  } catch (e) {
    console.log(e);
  }
  // console.log(
  //   "表单处理后----2--res_obj-->",config.key,
  //   res_obj,
  // );

  return res_obj[config.key];
  function handle_config(el) {
    // console.log("key_map类型----------》", typeof el.opts.keyMap);
    if (el.opts) {
      if (typeof el.opts.keyMap == "string") {
        handle_string(data, el, flag);
      } else if (Array.isArray(el.opts.keyMap)) {
        handle_array(data, el, flag);
      } else if (typeof el.opts.keyMap == "object") {
        handle_object(data, el, flag);
      } else {
        console.log("没有处理keyMap的类型-------->", typeof el.opts.keyMap);
      }
    } else {
      res_obj[el.key] = data[el.key];
    }
  }

  function handle_object(data, el, flag) {
    let res = {};
    let opts = el.opts;
    //处理合并   合并都为单选
    // console.log("数据处理------》", opts, flag);
    if (opts.merge) {
      if (flag) {
        //提交模块
        //提交数据都为数组
        let obj = handle_item_object(
          (data[el.key] && data[el.key][0]) || data[el.key],
          el,
          flag,
          opts.keyMap
        );
        Object.assign(res_obj, obj);
        Object.assign(form, obj);
      } else {
        //详情模块
        let obj = handle_item_object(form, el, flag, opts.keyMap);
        res_obj[el.key] = [obj];
        // res_obj[el.key] = obj
      }
    } else {
      //处理非合并  此函数处理的都是单选
      if (flag) {
        //提交模块
        //提交数据都为数组
        let obj = handle_item_object(data[el.key][0], el, flag, opts.keyMap);
        res_obj[el.key] = obj;
      } else {
        //详情模块
        let obj = handle_item_object(data[el.key], el, flag, opts.keyMap);
        res_obj[el.key] = [obj];
      }
    }
  }

  function handle_array(data, el, flag) {
    let map_item = el.opts.keyMap[0];
    if (typeof map_item == "string") {
      if (flag) {
        //提交模块
        res_obj[el.key] =
          data[el.key] && data[el.key].map((el) => el[map_item]);
      } else {
        //详情模块
        res_obj[el.key] =
          data[el.key] &&
          data[el.key].map((el) => ({
            [map_item]: el[map_item],
          }));
      }
    } else if (typeof map_item == "object") {
      if (data[el.key]) {
        res_obj[el.key] = data[el.key].map((it) =>
          handle_item_object(it, el, flag, map_item)
        )
      }
    } else {
      console.log("handle_array没有处理的类型", typeof map_item);
    }
  }

  function handle_string(data, el, flag) {
    let map_item = el.opts.keyMap;

    if (flag) {
      //提交模块
      res_obj[el.key] =
        data[el.key] && data[el.key][0]
          ? data[el.key][0][map_item]
          : data[map_item];
    } else {
      //详情模块
      res_obj[el.key] = [
        {
          [map_item]: data[el.key],
        },
      ];
      // Object.assign(form, res_obj);
    }
  }

  function handle_item_object(data_obj, el, flag, keyMap) {
    // console.log("------handle_item_object-------",data_obj, el, flag, keyMap);
    let res = {};
    let opts = el.opts;
    let k_v = Object.entries(keyMap);
    if (flag) {
      //提交模块
      k_v.forEach((ekey) => {
        //提交  =  详情
        if (data_obj) {
          res[ekey[0]] = data_obj[ekey[1]];
        }
      });
    } else {
      //详情模块
      k_v.forEach((ekey) => {
        //详情  =  提交
        res[ekey[1]] = data_obj[ekey[0]];
      });
    }
    return res;
  }
}
export function checkEnv(){
  function detectBrowserInfo() {
    const userAgent = navigator.userAgent.toLowerCase();
   console.log("userAgent----------》",userAgent);
    // 判断是否为微信浏览器
    const isWechat = /micromessenger/i.test(userAgent);
    // 判断是否为企业微信（即微信工作版）
    const isWechatWork = /wxwork/i.test(userAgent);
    // 判断是否为钉钉内置浏览器
    const isDingTalk = /dingtalk/i.test(userAgent);
    // 飞书内置浏览器可能包含 "lark" 关键字，但请核实最新版本 UA 以确保准确性
    const isFeishu = /lark/i.test(userAgent);
    return {
      isWechat,
      isWechatWork,
      isDingTalk,
      isFeishu
    };
  } 
  const browserInfo = detectBrowserInfo();
  if (browserInfo.isWechat) {
    console.log('当前环境是微信内置浏览器');
    return "wx"
  } else if (browserInfo.isWechatWork) {
    console.log('当前环境是企业微信内置浏览器');
    return "wx"
  } else if (browserInfo.isDingTalk) {
    console.log('当前环境是钉钉内置浏览器');
    return "dd"
  } else if (browserInfo.isFeishu) {
    console.log('当前环境可能是飞书内置浏览器');
  } else {
    console.log('当前环境不是以上提及的内置浏览器');
  }
}