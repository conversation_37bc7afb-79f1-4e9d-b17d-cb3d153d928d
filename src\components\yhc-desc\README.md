# yhc-desc 提示性文字组件

## 介绍

yhc-desc 是一个基于 Vant4 的提示性文字组件，支持多种样式类型、图标、链接等功能，适用于各种提示、说明、警告等场景。

## 引入

```js
import yhcDesc from '@/components/yhc-desc'
```

## 代码演示

### 基础用法

```vue
<template>
  <yhc-desc :config="descConfig" />
</template>

<script setup>
const descConfig = {
  label: "说明：前往钉钉管理后台(https://oa.dingtalk.com)->工作台->应用管理->OA审批->员工->一般审批->五一请假->回到页面设计"
}
</script>
```

### 带图标的提示

```vue
<template>
  <yhc-desc :config="iconConfig" />
</template>

<script setup>
const iconConfig = {
  label: "这是一个重要提示信息",
  icon: "info-o",
  type: "info"
}
</script>
```

### 带标题的提示

```vue
<template>
  <yhc-desc :config="titleConfig" />
</template>

<script setup>
const titleConfig = {
  title: "操作说明",
  label: "请按照以下步骤进行操作，确保数据的准确性和完整性。",
  type: "info"
}
</script>
```

### 可点击的提示

```vue
<template>
  <yhc-desc :config="clickableConfig" />
</template>

<script setup>
const clickableConfig = {
  label: "点击查看详细说明",
  clickable: true,
  showArrow: true,
  onClick: () => {
    console.log('提示被点击了')
  }
}
</script>
```

### 带链接的提示

```vue
<template>
  <yhc-desc :config="linkConfig" />
</template>

<script setup>
const linkConfig = {
  label: "如果遇到问题，请联系技术支持。",
  linkText: "点击这里获取帮助",
  onLinkClick: () => {
    window.open('https://help.example.com')
  }
}
</script>
```

### 不同类型的提示

```vue
<template>
  <div>
    <!-- 信息提示 -->
    <yhc-desc :config="infoConfig" />

    <!-- 警告提示 -->
    <yhc-desc :config="warningConfig" />

    <!-- 错误提示 -->
    <yhc-desc :config="errorConfig" />

    <!-- 成功提示 -->
    <yhc-desc :config="successConfig" />
  </div>
</template>

<script setup>
const infoConfig = {
  label: "这是一个信息提示",
  type: "info",
  icon: "info-o"
}

const warningConfig = {
  label: "这是一个警告提示",
  type: "warning",
  icon: "warning-o"
}

const errorConfig = {
  label: "这是一个错误提示",
  type: "error",
  icon: "cross"
}

const successConfig = {
  label: "这是一个成功提示",
  type: "success",
  icon: "success"
}
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| config | 组件配置对象 | Object | - |
| form | 表单数据对象 | Object | - |

### Config 配置

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| label | 主要文本内容 | String | "提示文本" |
| title | 标题文本 | String | "" |
| type | 提示类型，可选值为 `default` `info` `warning` `error` `success` | String | "default" |
| icon | 图标名称，支持 Vant 图标 | String | "" |
| iconSize | 图标大小 | String | "16px" |
| iconColor | 图标颜色，为空时使用主题色 | String | "" |
| linkText | 链接文本 | String | "" |
| clickable | 是否可点击 | Boolean | false |
| showArrow | 是否显示右侧箭头 | Boolean | false |
| padding | 内边距 | String | "12px 16px" |
| margin | 外边距 | String | "0" |
| backgroundColor | 背景色 | String | "#F2F3F4" |
| textColor | 文字颜色 | String | "rgba(0, 0, 0, 0.5)" |
| fontSize | 字体大小 | String | "14px" |
| lineHeight | 行高 | String | "1.5" |
| borderRadius | 圆角 | String | "0" |
| onClick | 点击回调函数 | Function | null |
| onLinkClick | 链接点击回调函数 | Function | null |

## 样式定制

### 自定义样式

```scss
// 自定义背景色和文字颜色
.custom-desc {
  :deep(.yhc-desc) {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
  }
}
```

### 响应式设计

组件支持响应式设计，在小屏幕设备上会自动调整内边距和字体大小：

- **移动端** (≤375px): 内边距 `10px 12px`，字体大小 `13px`

## 注意事项

1. 组件依赖 Vant4 的 `van-icon` 组件
2. 支持所有 Vant 图标库中的图标
3. 不同类型的提示会有不同的颜色和左边框样式
4. 可点击状态下会有悬停和激活效果
5. 组件名称和所有文本内容使用中文，符合项目规范
6. 与 yhc-form 组件体系完全兼容，支持自动注册和使用
