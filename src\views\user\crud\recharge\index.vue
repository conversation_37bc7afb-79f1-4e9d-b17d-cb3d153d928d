<template>
  <div class="wrapper-recharge">
    <van-space fill direction="vertical">
      <van-cell-group inset>
        <van-field
          v-model="money"
          type="number"
          name="money"
          label="充值金额"
          placeholder="充值金额"
          label-align="top"
          clearable
          clear-trigger="always"
          :rules="[{ required: true, message: '请填写充值金额' }]"
        />
      </van-cell-group>
      <span class="icon">￥:</span>
      <van-checkbox-group v-model="checked">
        <van-cell-group inset>
          <van-cell
            v-for="(item, index) in list"
            clickable
            :key="index"
            :title="item.title"
            :icon="item.icon"
          >
            <template #right-icon>
              <van-checkbox
                :name="item.title"
                label-disabled
                @click.stop="checkedboxClick"
                :ref="(el) => (checkboxRefs[index] = el)"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
      <div style="margin: 16px">
        <van-button
          :loading="loading"
          :size="20"
          block
          type="primary"
          @click="onSubmit"
        >
          充值
        </van-button>
      </div>
    </van-space>
  </div>
</template>
<script setup>
import { ref, nextTick } from "vue";
const { proxy } = getCurrentInstance();
import { showToast, showDialog } from "vant";
const money = ref("");
const checked = ref([]);
const checkedbox = ref(true);
const loading = ref(false);
const checkboxRefs = ref([]);
const list = [
  {
    title: "支付宝支付",
    icon: "alipay",
  },
  // {
  //   title: "微信支付",
  //   icon: "wechat",
  // }
];
const checkedboxClick = (val) => {
  checked.value = ["支付宝支付"];
};
const onSubmit = (values) => {
  showDialog({
    title: "充值",
    message: `充值金额：${money.value}元\n您将要跳转至支付宝进行充值支付，是否继续？`,
  })
    .then(() => {
      post();
    })
    .catch(() => {
      // on cancel
    });
};
const toggle = (index) => {
  checkboxRefs.value[0].toggle();
};
nextTick(toggle);
const post = () => {
  loading.value = true;
  proxy
    .$post(import.meta.env.VITE_APP_USER_API + "recharge/post_add", {
      money: money.value,
      pay_type: 0,
    })
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        window.location.href = res.qr_code;
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    })
    .finally(() => {
      loading.value = false;
    });
};
</script>
<style lang="scss">
.wrapper-recharge {
  width: 100%;
  min-height: 100vh;
  padding-top: 16px;
  position: relative;
  .van-field__control {
    font-size: 24px;
    margin-left: 22px;
  }
  .icon {
    position: absolute;
    left: 33px;
    top: 63px;
    font-size: 14px;
  }
  .van-icon {
    margin-right: 8px;
  }
}
</style>
