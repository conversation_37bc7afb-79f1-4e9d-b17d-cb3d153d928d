# yhc-picker-date 组件

一个支持多种时间选择类型的日期时间选择器组件。

## 功能特性

- 支持多种时间格式类型
- 自动设置当前时间为默认值
- 基于 Vant UI 的 Picker 组件
- 支持移动端友好的交互体验

## 支持的时间类型

### 1. 完整日期时间 (datetime)
- 格式：`YYYY-MM-DD HH:mm:ss`
- 示例：`2023-12-25 14:30:45`
- 包含：年、月、日、时、分、秒

### 2. 仅日期 (date)
- 格式：`YYYY-MM-DD`
- 示例：`2023-12-25`
- 包含：年、月、日

### 3. 完整时间 (time-full 或 time)
- 格式：`HH:mm:ss`
- 示例：`14:30:45`
- 包含：时、分、秒

### 4. 简化时间 (time-short)
- 格式：`HH:mm`
- 示例：`14:30`
- 包含：时、分

## 使用方法

### 基本用法

```vue
<template>
  <yhc-picker-date
    :config="config"
    :form="form"
  />
</template>

<script setup>
import { reactive } from 'vue'
import yhcPickerDate from '@/components/yhc-picker-date/index.vue'

const form = reactive({
  datetime_field: '',
  date_field: '',
  time_field: '',
  time_short_field: ''
})

// 完整日期时间配置
const datetimeConfig = {
  label: "选择日期时间",
  key: "datetime_field",
  type: "datetime",
  placeholder: "请选择",
  required: true
}
```

### 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| label | String | "日期选择" | 显示标签 |
| key | String | "" | 表单字段名 |
| type | String | "datetime" | 时间类型，可选值：`datetime`、`date`、`time`、`time-full`、`time-short` |
| placeholder | String | "请选择" | 占位符文本 |
| required | Boolean | false | 是否必填 |
| disabled | Boolean | false | 是否禁用 |
| default | Boolean | false | 是否使用当前时间作为默认值 |

### 默认值行为

- 只有当 `default` 设置为 `true` 且表单字段没有初始值时，组件才会自动设置当前时间作为默认值
- 默认值格式会根据 `type` 参数自动调整：
  - `datetime`: 当前完整日期时间 (YYYY-MM-DD HH:mm:ss)
  - `date`: 当前日期 (YYYY-MM-DD)
  - `time`/`time-full`: 当前时间（含秒）(HH:mm:ss)
  - `time-short`: 当前时间（不含秒）(HH:mm)
- 如果 `default` 为 `false`（默认值），组件将保持原有行为，不会自动设置默认值

### 表单集成示例

```javascript
// 表单配置示例
const formConfig = [
  {
    label: "开始时间",
    key: "start_time",
    component: "yhc-picker-date",
    type: "datetime",
    required: true,
    default: true // 使用当前时间作为默认值
  },
  {
    label: "结束日期",
    key: "end_date",
    component: "yhc-picker-date",
    type: "date",
    default: false // 不使用默认值
  },
  {
    label: "超速持续时间",
    key: "speed_duration",
    component: "yhc-picker-date",
    type: "time-short",
    default: true // 使用当前时间作为默认值
  }
]
```

## 更新日志

### v1.0.3
- 新增 `time-short` 类型支持（仅时分）
- 新增 `time-full` 类型别名
- 新增 `default` 参数，可控制是否使用当前时间作为默认值
- 改进默认值逻辑，只有在明确配置时才设置默认值
- 优化时间格式输出逻辑
- 保持向后兼容，原有组件行为不变
