# yhc-checkbox-group 复选框组组件

一个基于 Vant UI 的可复用复选框组组件，封装了 van-field 和 van-checkbox-group 的组合。

## 功能特性

- 支持双向数据绑定（v-model）
- 可配置复选框选项和标签文本
- 支持水平和垂直布局方向
- 支持单个选项禁用和整体禁用
- 支持表单验证规则
- 保持与现有 yhc 组件风格一致

## 配置参数

### 基础配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| label | String | "复选框组" | 字段标签文本 |
| key | String | "" | 字段名称，用于表单数据绑定 |
| required | Boolean | false | 是否必填 |
| disabled | Boolean | false | 是否禁用整个组件 |
| border | Boolean | true | 是否显示边框 |
| labelWidth | String | "" | 标签宽度 |
| rules | Array | [] | 表单验证规则 |

### 复选框组配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| direction | String | "horizontal" | 布局方向，可选值：horizontal、vertical |
| shape | String | "square" | 复选框形状，可选值：square、round |
| options | Array | [] | 选项数组，格式见下方说明 |

### options 选项数组格式

```javascript
[
  {
    value: "option1",    // 选项值（必填）
    label: "选项1",      // 选项显示文本（必填）
    disabled: false      // 是否禁用此选项（可选，默认false）
  },
  // ... 更多选项
]
```

## 使用示例

### 基本用法

```vue
<template>
  <yhc-form :config="formConfig" @onSubmit="onSubmit" />
</template>

<script setup>
import { reactive } from 'vue'

const formConfig = {
  form: [
    {
      label: "兴趣爱好",
      key: "hobbies",
      component: "yhc-checkbox-group",
      required: true,
      rules: [{ required: true, message: "请选择兴趣爱好" }],
      options: [
        { value: "reading", label: "阅读" },
        { value: "music", label: "音乐" },
        { value: "sports", label: "运动" },
        { value: "travel", label: "旅行" }
      ]
    }
  ]
}

const onSubmit = (data) => {
  console.log('表单数据:', data)
  // data.hobbies 将是一个数组，如：["reading", "music"]
}
</script>
```

### 垂直布局

```javascript
{
  label: "技能标签",
  key: "skills",
  component: "yhc-checkbox-group",
  direction: "vertical", // 垂直布局
  options: [
    { value: "javascript", label: "JavaScript" },
    { value: "vue", label: "Vue.js" },
    { value: "react", label: "React" },
    { value: "nodejs", label: "Node.js" }
  ]
}
```

### 带禁用选项

```javascript
{
  label: "权限选择",
  key: "permissions",
  component: "yhc-checkbox-group",
  options: [
    { value: "read", label: "读取权限" },
    { value: "write", label: "写入权限" },
    { value: "delete", label: "删除权限", disabled: true }, // 禁用此选项
    { value: "admin", label: "管理员权限" }
  ]
}
```

### 圆形复选框

```javascript
{
  label: "选择项目",
  key: "projects",
  component: "yhc-checkbox-group",
  shape: "round", // 圆形复选框
  options: [
    { value: "project1", label: "项目一" },
    { value: "project2", label: "项目二" }
  ]
}
```

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 复选框组值变化时触发 | { component: "yhc-checkbox-group", key: 字段名, value: 选中值数组 } |

## 数据格式

组件的值是一个数组，包含所有选中项的 value 值：

```javascript
// 示例数据
{
  hobbies: ["reading", "music", "sports"] // 选中了阅读、音乐、运动三个选项
}
```

## 样式定制

组件支持通过 CSS 变量或深度选择器自定义样式：

```scss
// 自定义复选框间距
:deep(.van-checkbox-group--horizontal .van-checkbox) {
  margin-right: 20px; // 水平间距
}

:deep(.van-checkbox-group--vertical .van-checkbox) {
  margin-bottom: 16px; // 垂直间距
}

// 自定义标签颜色
:deep(.van-checkbox__label) {
  color: #1989fa;
}
```

## 注意事项

1. `options` 数组中每个选项的 `value` 必须唯一
2. 组件会自动初始化表单值为空数组 `[]`
3. 支持表单验证，可配置 `required` 和 `rules`
4. 禁用状态下用户无法进行任何操作
5. 组件名称和所有文本内容使用中文，符合项目规范
