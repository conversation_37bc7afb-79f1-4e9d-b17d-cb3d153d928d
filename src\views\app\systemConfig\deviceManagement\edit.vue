<template>
    <div class="device-detail-container">
        <!-- 设备详情信息 -->
        <div v-if="editForm" class="device-info">
            <!-- 设备名称 - 单独卡片 -->
            <van-cell-group inset class="device-name-card">
                <van-field v-model="editForm.title" label="设备名称" placeholder="请输入设备名称"
                    :rules="[{ required: true, message: '请输入设备名称' }]" class="device-name-field" />
            </van-cell-group>

            <!-- 基本信息 -->
            <van-cell-group inset>
                <!-- 型号 - 可编辑 -->
                <van-field v-model="editForm.model" label="型号" placeholder="请输入型号" />

                <!-- SN - 只读显示 -->
                <van-field v-model="editForm.deviceSn" label="SN" disabled />

                <!-- <van-cell title="SN" :value="deviceInfo.deviceSn" /> -->
            </van-cell-group>

            <!-- 网络状态 -->
            <van-cell-group inset>
                <van-cell title="网络状态" is-link @click="goNetworkState">
                    <template #value>
                        <span class="network-status-text" :class="{
                            'status-offline': editForm.networkStatus == 1,
                            'status-online': editForm.networkStatus != 1
                        }">
                            {{ getNetworkStatusText(editForm.network_status) }}
                        </span>
                    </template>
                </van-cell>
            </van-cell-group>

            <!-- 欢迎屏幕下的刷脸/刷卡/输码/扫码 -->
            <van-cell-group v-if="deviceType == 1" inset>
                <van-cell>
                    <template #title>
                        欢迎屏幕
                    </template>
                    <template #value>
                        <van-switch v-model="editForm.show_index" :active-value="1" :inactive-value="0" />
                    </template>
                </van-cell>
                <template v-if="editForm.show_index != 0">
                    <van-cell title="刷脸">
                        <template #value>
                            <van-switch v-model="editForm.face" :active-value="1" :inactive-value="0" />
                        </template>
                    </van-cell>
                    <van-cell title="刷卡">
                        <template #value>
                            <van-switch v-model="editForm.swipe_card" :active-value="1" :inactive-value="0" />
                        </template>
                    </van-cell>
                    <van-cell title="输码">
                        <template #value>
                            <van-switch v-model="editForm.meal_code" :active-value="1" :inactive-value="0" />
                        </template>
                    </van-cell>
                    <van-cell title="扫码">
                        <template #value>
                            <van-switch v-model="editForm.scan_code" :active-value="1" :inactive-value="0" />
                        </template>
                    </van-cell>
                </template>
            </van-cell-group>
            <!-- 收银机特有配置 -->
            <van-cell-group v-if="deviceType == 0" inset>
                <van-cell title="刷脸">
                    <template #value>
                        <van-switch v-model="editForm.face" :active-value="1" :inactive-value="0" />
                    </template>
                </van-cell>
                <van-cell title="刷卡">
                    <template #value>
                        <van-switch v-model="editForm.swipe_card" :active-value="1" :inactive-value="0" />
                    </template>
                </van-cell>
                <van-cell title="输码">
                    <template #value>
                        <van-switch v-model="editForm.meal_code" :active-value="1" :inactive-value="0" />
                    </template>
                </van-cell>
                <van-cell title="扫码">
                    <template #value>
                        <van-switch v-model="editForm.scan_code" :active-value="1" :inactive-value="0" />
                    </template>
                </van-cell>
                <!-- <van-cell title="取餐码">
            <template #value>
              <van-switch v-model="editForm.pickupCode" />
            </template>
          </van-cell> -->
            </van-cell-group>
            <!-- 功能设置+开门延时合并为一个卡片，样式与图片一致 -->
            <van-cell-group v-if="deviceType == 1" inset>
                <!-- 门禁 - 开关组件 -->
                <van-cell title="门禁">
                    <template #value>
                        <van-switch v-model="editForm.is_open_door" :active-value="1" :inactive-value="0" />
                    </template>
                </van-cell>
                <van-cell>
                    <template #title>
                        开门延时(秒)
                    </template>
                    <template #value>
                        <div class="door-delay-row">
                            <van-button size="mini" class="door-delay-btn" @click="decreaseDoorDelay"
                                :disabled="editForm.open_door_time <= 1">-</van-button>
                            <span class="door-delay-value">{{ editForm.open_door_time }}</span>
                            <van-button size="mini" class="door-delay-btn" @click="increaseDoorDelay"
                                :disabled="editForm.open_door_time >= 20">+</van-button>
                        </div>
                    </template>
                </van-cell>
            </van-cell-group>
            <!-- 设备音量 -->
            <!-- <span class="device-volume-label">0</span> -->
            <div class="device-volume-card">
                <div class="device-volume-title" style="margin-bottom: 8px;">设备音量</div>
                <div class="device-volume-slider-row short-slider-row" style="margin-top: 10px;">
                    <van-slider v-model="editForm.volume" :min="0" :max="100" :step="1" bar-height="4px"
                        active-color="#1989fa" inactive-color="#ebedf0" @change="onVolumeChange"
                        class="device-volume-slider custom-volume-slider short-slider">
                        <template #button>
                            <div class="custom-slider-dot-small">{{ editForm.volume }}</div>
                        </template>
                    </van-slider>
                </div>
            </div>
            <!-- <span class="device-volume-label">100</span> -->

            <!-- 系统信息 -->
            <van-cell-group inset>
                <van-cell title="系统信息" is-link @click="showSystemInfo">
                    <template #value>
                        <span class="sysinfo-upgrade-btn" @click.stop="checkUpdate">升级</span>
                        <!-- <van-icon name="arrow" /> -->
                    </template>
                </van-cell>
            </van-cell-group>


        </div>

        <!-- 底部按钮 -->
        <div class="bottom-buttons">
            <!-- <van-button
          class="reset-button"
          block
          @click="resetDevice"
          :loading="resetting"
        >
          解除并重置
        </van-button> -->
            <van-button type="primary" block :loading="saving" @click="saveAllChanges">
                保存
            </van-button>
        </div>

        <!-- 网络状态选择器 -->
        <van-popup v-model:show="showNetworkPicker" position="bottom" round>
            <van-picker :columns="networkOptions" @confirm="onNetworkConfirm" @cancel="showNetworkPicker = false" />
        </van-popup>

        <!-- 图片上传弹窗 -->
        <van-popup v-model:show="showImageUpload" position="bottom" round>
            <div class="upload-popup">
                <div class="popup-header">
                    <h3>{{ uploadType === 'image' ? '上传设备图片' : '上传Logo' }}</h3>
                    <van-icon name="cross" @click="showImageUpload = false" />
                </div>
                <div class="popup-content">
                    <van-uploader v-model="uploadedImages" :max-count="1" @after-read="onImageUpload" />
                    <div class="popup-actions">
                        <van-button type="primary" block @click="confirmImageUpload">确认</van-button>
                    </div>
                </div>
            </div>
        </van-popup>

        <!-- 设备名称修改弹窗 -->
        <!-- <van-dialog v-model:show="showDeviceNameDialog" title="修改设备名称" show-cancel-button @confirm="confirmDeviceName">
            <van-field v-model="tempDeviceName" placeholder="请输入设备名称" style="margin: 16px 0;" />
        </van-dialog> -->

        <!-- 设备序列号修改弹窗 -->
        <!-- <van-dialog v-model:show="showSerialDialog" title="修改设备序列号" show-cancel-button @confirm="confirmSerial">
            <van-field v-model="tempSerial" placeholder="请输入设备序列号" style="margin: 16px 0;" />
        </van-dialog> -->

        <!-- 设备型号修改弹窗 -->
        <!-- <van-dialog v-model:show="showModelDialog" title="修改设备型号" show-cancel-button @confirm="confirmModel">
            <van-field v-model="tempModel" placeholder="请输入设备型号" style="margin: 16px 0;" />
        </van-dialog> -->

        <!-- 只读信息弹窗 -->
        <!-- <van-dialog v-model:show="showReadOnlyDialog" title="只读信息" show-cancel-button confirm-button-text="确定">
            <div class="readonly-info">
                <p>
                    <strong>SN:</strong>
                    <span>{{ deviceInfo?.deviceSn }}</span>
                </p>
                <p>
                    <strong>MAC地址:</strong>
                    <span>{{ deviceInfo?.mac }}</span>
                </p>
                <p>
                    <strong>应用版本:</strong>
                    <span>{{ deviceInfo?.appVersion }}</span>
                </p>
                <div class="readonly-note">以上信息为只读，不可修改</div>
            </div>
        </van-dialog> -->
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'

const { proxy } = getCurrentInstance();

// 路由实例
const route = useRoute()
const router = useRouter()

// 设备类型和ID
const deviceType = ref('')
const deviceId = ref('')
const deviceInfo = ref({
    title: '',
    serialNumber: '',
    model: '',
    ip: '',
    networkStatus: 'offline', // 'wired', 'wireless', 'offline'
    show_index: true,
    lockWelcomeScreen: false,
    accessControl: false,
    antiCard: false, // 新增防卡功能
    open_door_time: 3,
    // 新增刷脸、刷卡、输码、扫码
    face: false,
    swipe_card: false,
    meal_code: false,
    scan_code: false,
    is_open_door: false,
    volume: 50
})

// 编辑表单数据
let editForm = ref({
    title: '',
    serialNumber: '',
    model: '',
    ip: '',
    networkStatus: 'offline', // 'wired', 'wireless', 'offline'
    show_index: 0,
    lockWelcomeScreen: false,
    accessControl: false,
    antiCard: false, // 新增防卡功能
    open_door_time: 3,
    // 新增刷脸、刷卡、输码、扫码
    face: false,
    swipe_card: false,
    meal_code: false,
    scan_code: false,
    is_open_door: false,
    volume: 50
})

// 编辑状态
const isEditing = ref(false)

// 弹窗控制
const showImageUpload = ref(false)
const showNetworkPicker = ref(false)
const uploadType = ref('image') // 'image' 或 'logo'
const uploadedImages = ref([])
const saving = ref(false)
const updateChecking = ref(false)
const resetting = ref(false)

// 网络状态选项
const networkOptions = [
    { text: '在线', value: 1 },
    // { text: '无线', value: 'wireless' },
    { text: '离线', value: 0 }
]

// 修改弹窗控制
const showDeviceNameDialog = ref(false)
const showSerialDialog = ref(false)
const showModelDialog = ref(false)
const showReadOnlyDialog = ref(false)

// 临时编辑变量
const tempDeviceName = ref('')
const tempSerial = ref('')
const tempModel = ref('')

// 模拟设备数据
const detail = ref([])


// 获取网络状态文本
const getNetworkStatusText = (status) => {
    const option = networkOptions.find(opt => opt.value === status)
    return option ? option.text : '未知'
}

// 网络状态选择确认
const onNetworkConfirm = ({ selectedOptions }) => {
    editForm.networkStatus = selectedOptions[0].value
    showNetworkPicker.value = false
}

// 音量滑块变更事件（可选，实际可省略，主要用于后续扩展）
const onVolumeChange = (val) => {
    // 可根据需要做提示或其它处理
    showToast(`音量已设置为 ${val}`)
}

// 增加开门延时
const increaseDoorDelay = () => {
    editForm.open_door_time = Math.min(20, editForm.open_door_time + 1)
}

// 减少开门延时
const decreaseDoorDelay = () => {
    editForm.open_door_time = Math.max(1, editForm.open_door_time - 1)
}

// 显示系统信息
const showSystemInfo = () => {
    showToast('系统信息功能开发中')
}

// 重置设备
const resetDevice = async () => {
    resetting.value = true
    try {
        await new Promise(resolve => setTimeout(resolve, 2000))
        showToast('设备重置成功')
    } catch (error) {
        showToast('设备重置失败，请重试')
    } finally {
        resetting.value = false
    }
}

// 切换编辑状态
const toggleEdit = () => {
    isEditing.value = !isEditing.value
    if (isEditing.value) {
        // initEditForm()
    }
}

// 初始化编辑表单
// const initEditForm = () => {
//   if (!deviceInfo.value) return

//   editForm.name = deviceInfo.value.name
//   editForm.serialNumber = deviceInfo.value.serialNumber || ''
//   editForm.model = deviceInfo.value.model || ''

//   if (deviceType.value ==0) {
//     editForm.ip = deviceInfo.value.ip
//     editForm.networkStatus = deviceInfo.value.networkStatus || 'wired'
//     editForm = {
//       show_index: deviceInfo.value?.show_index || false,
//       lockWelcomeScreen: deviceInfo.value?.lockWelcomeScreen || false,
//       accessControl: deviceInfo.value?.accessControl || false,
//       open_door_time: deviceInfo.value?.open_door_time || 5,
//       // 新增刷脸、刷卡、输码、扫码
//       face: deviceInfo.value?.face || false,
//       swipe_card: deviceInfo.value?.swipe_card || false,
//       meal_code: deviceInfo.value?.meal_code || false,
//       scan_code: deviceInfo.value?.scan_code || false,
//       is_open_door: deviceInfo.value?.is_open_door || false,
//       antiCard: deviceInfo.value?.antiCard || false,
//       // 音量范围调整为0-10
//       volume: typeof deviceInfo.value?.volume === 'number'
//         ? Math.max(0, Math.min(10, Math.round(deviceInfo.value.volume / 10)))
//         : 5
//     }
//   } else if (deviceType.value ==1) {
//     // 音量范围调整为0-10
//     editForm.volume = typeof deviceInfo.value.config.volume === 'number'
//       ? Math.max(0, Math.min(10, Math.round(deviceInfo.value.config.volume / 10)))
//       : 5
//   }

//   // 初始化临时编辑变量
//   tempDeviceName.value = editForm.name
//   tempSerial.value = editForm.serialNumber
//   tempModel.value = editForm.model
// }

// 保存所有修改
const saveAllChanges = async () => {
    saving.value = true
    
    
    console.log(editForm.value,555)
    proxy.$post('/device/post_modify_device',editForm.value).then((res) => {
        if (res.code === 200) {
            showToast("修改成功")
            router.push({ path: '/systemConfig/deviceManagement' })
            // 初始化编辑表单
            // initEditForm()
        } else {
            showToast(res.msg )
        }
        saving.value = false
    }).catch(error => {
        showToast(error.msg )
        saving.value = false
    })


}





// 图片上传处理
const onImageUpload = (file) => {
    console.log('上传图片:', file)
}

// 确认图片上传
const confirmImageUpload = () => {
    if (uploadedImages.value.length > 0) {
        const imageUrl = uploadedImages.value[0].content

        if (uploadType.value === 'image') {
            deviceInfo.value.image = imageUrl
            showToast('设备图片已更新')
        } else if (uploadType.value === 'logo' && deviceType.value == 1) {
            deviceInfo.value.config.logo = imageUrl
            showToast('Logo已更新')
        }

        showImageUpload.value = false
    } else {
        showToast('请先上传图片')
    }
}

// 检测更新
const checkUpdate = async () => {
    updateChecking.value = true
    try {
        // 模拟检测更新过程
        await new Promise(resolve => setTimeout(resolve, 2000))
        showToast('当前已是最新版本')
    } catch (error) {
        showToast('检测更新失败，请重试')
    } finally {
        updateChecking.value = false
    }
}

function goNetworkState(){
  router.push({
    path: "/app/systemConfig/deviceManagement/networkState",
    query: {
      IP: editForm.value.ip,
      MAV: editForm.value.mac_ip
    }
  })
}




function getDetail() {
    proxy.$get('/device/get_device_info', { id: deviceId.value, type: deviceType.value }).then((res) => {
        if (res.code === 200) {
            editForm.value = res.data
            console.log('获取设备详情:', editForm.value)
            editForm.value.type=deviceType.value
            // 初始化编辑表单
            // initEditForm()
        } else {
            showToast(res.msg || '获取设备详情失败，请稍后重试')
        }
    })
}

// 页面加载
onMounted(() => {
    deviceType.value = parseInt(route.query.type)
    deviceId.value = route.query.id // 不转换为整数，保持原始类型
    console.log("route.query:", route.query)
    getDetail()

    // 初始化编辑表单
    // initEditForm()
})

// 监听设备信息变化，重新初始化表单
watch(deviceInfo, () => {
    if (deviceInfo.value) {
        // initEditForm()
    }
}, { deep: true })
</script>

<style lang="scss" scoped>
.device-detail-container {
    min-height: 90vh;
    background: #f7f8fa;
}

.device-info {
    padding: 0px 0 80px 0; // 底部留出保存按钮的空间
}

.network-status {
    display: flex;
    gap: 4px;

    .van-tag {
        font-size: 10px;
    }
}

.placeholder-text {
    color: #969799;
    font-size: 14px;
}

.word-count-tip {
    padding: 8px 16px 0;
    font-size: 12px;
    color: #969799;
    text-align: right;
}

.volume-control-inline {
    display: flex;
    align-items: center;

    .volume-text {
        font-size: 14px;
        color: #1989fa;
        font-weight: 600;
        min-width: 40px;
    }
}

// 底部按钮容器
.bottom-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: white;
    border-top: 1px solid #ebedf0;
    z-index: 100;
    display: flex;
    gap: 12px;

    .reset-button {
        background: white;
        color: #ee0a24;
        border: 1px solid #ee0a24;
        border-radius: 6px;
        flex: 1;
    }

    .van-button--primary {
        border-radius: 6px;
        flex: 1;
    }
}

.upload-popup {
    height: 100%;
    display: flex;
    flex-direction: column;

    .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #ebedf0;
        background: white;

        h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #323233;
        }

        .van-icon {
            font-size: 20px;
            color: #969799;
            cursor: pointer;
        }
    }

    .popup-content {
        flex: 1;
        padding: 16px;
        overflow-y: auto;
    }

    .popup-actions {
        margin-top: 24px;
    }
}

// 设备名称卡片样式
.device-name-card {
    margin: 16px;
    margin-top: 0px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    background: white;

    .device-name-field {
        :deep(.van-cell) {
            height: 54px;
            padding: 0;

            .van-field__body {
                height: 54px;
                display: flex;
                align-items: center;
                padding: 0 16px;
            }

            .van-field__label {
                font-size: 16px;
                line-height: 22px;
                color: #323233;
                flex: 0 0 40%;
                text-align: left;
                white-space: nowrap;
                display: flex;
                align-items: center;
                height: 54px;
            }

            .van-field__control {
                font-size: 16px;
                line-height: 22px;
                color: #323233;
                flex: 1;
                text-align: left;
                display: flex;
                align-items: center;
                height: 54px;
            }
        }
    }
}

// 自定义单元格样式
:deep(.van-cell-group) {
    margin: 16px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

:deep(.van-cell) {
    height: 54px;
    padding: 0 16px;
    display: flex;
    align-items: center;

    &:not(:last-child)::after {
        left: 16px;
        right: 16px;
    }

    // 统一标签宽度和样式
    .van-cell__title {
        flex: 0 0 40%;
        text-align: left;
        white-space: nowrap;
        font-size: 16px;
        line-height: 22px;
        color: #323233;
        display: flex;
        align-items: center;
        height: 54px;
    }

    .van-cell__value {
        flex: 1;
        font-size: 15px;
        line-height: 22px;
        margin-right: 3px;
        color: #323233;
        display: flex;
        align-items: center;
        height: 54px;
        justify-content: flex-end;
    }
}

// 统一字段标签宽度和样式
:deep(.van-field) {
    .van-cell {
        height: 54px;
        padding: 0;

        .van-field__body {
            height: 54px;
            display: flex;
            align-items: center;
            padding: 0 16px;
        }

        .van-field__label {
            flex: 0 0 40%;
            text-align: left;
            font-size: 16px;
            line-height: 22px;
            color: #323233;
            display: flex;
            align-items: center;
            height: 54px;
        }

        .van-field__control {
            flex: 1;
            text-align: left;
            font-size: 16px;
            line-height: 22px;
            color: #323233;
            display: flex;
            align-items: center;
            height: 54px;
        }
    }
}

// 开关和滑块对齐
:deep(.van-cell) {
    .van-cell__value {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        white-space: nowrap; // 防止文字换行
    }

    .van-switch {
        margin: 0;
    }
}

// 音量控制样式优化
.volume-control-inline {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;

    .van-slider {
        width: 120px;
    }

    .volume-text {
        font-size: 14px;
        color: #323233;
        min-width: 35px;
        text-align: right;
        white-space: nowrap; // 防止文字换行
    }
}

// 标签样式
:deep(.van-tag) {
    border-radius: 12px;
    font-size: 12px;
    padding: 2px 8px;
}

// 只读文本样式
.readonly-text {
    color: #969799;
    font-size: 14px;
}

// 只读信息弹窗样式
.readonly-info {
    padding: 20px 24px;

    p {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 12px 0;
        font-size: 14px;
        color: #323233;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-of-type {
            border-bottom: none;
        }

        strong {
            color: #646566;
            font-weight: 500;
            flex-shrink: 0;
            width: 80px;
        }

        span {
            color: #323233;
            text-align: right;
            flex: 1;
            margin-left: 16px;
        }
    }

    .readonly-note {
        margin-top: 20px;
        padding: 12px 16px;
        background: #f7f8fa;
        border-radius: 8px;
        color: #969799;
        font-size: 12px;
        text-align: center;
        line-height: 1.4;
    }
}

// 图片样式
:deep(.van-image) {
    border-radius: 8px;
}

// 上传组件样式
:deep(.van-uploader) {
    .van-uploader__upload {
        border: 2px dashed #dcdee0;
        border-radius: 8px;

        &:hover {
            border-color: #1989fa;
        }
    }
}

// 子选项样式
.sub-option {
    :deep(.van-cell__title) {
        padding-left: 20px;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 12px;
            height: 1px;
            background: #dcdee0;
        }
    }
}

// 单位文本样式
.unit-text {
    color: #969799;
    font-size: 14px;
    margin-left: 8px;
    white-space: nowrap; // 防止文字换行
}

// 音量显示样式
.volume-display {
    color: #1989fa;
    font-weight: 600;
    font-size: 14px;
}

// 检测更新文本样式
.update-text {
    color: #1989fa;
    font-size: 14px;

    &.loading {
        color: #969799;
    }
}

// 音量选择器样式
.volume-selector {
    padding: 20px;

    .selector-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;

        h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #323233;
        }

        .van-icon {
            font-size: 20px;
            color: #969799;
            cursor: pointer;
        }
    }

    .volume-buttons {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
        margin-bottom: 24px;

        .volume-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 12px;
            border: 2px solid #ebedf0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:active {
                transform: scale(0.95);
            }

            &.active {
                border-color: #1989fa;
                background: #f0f8ff;

                .volume-progress {
                    background: #1989fa;
                }

                .volume-label {
                    color: #1989fa;
                    font-weight: 600;
                }
            }

            .volume-bar {
                width: 40px;
                height: 6px;
                background: #ebedf0;
                border-radius: 3px;
                margin-bottom: 8px;
                overflow: hidden;

                .volume-progress {
                    height: 100%;
                    background: #dcdee0;
                    border-radius: 3px;
                    transition: all 0.3s ease;
                }
            }

            .volume-label {
                font-size: 14px;
                color: #323233;
            }
        }
    }

    .volume-actions {
        margin-top: 16px;
    }
}

.volume-slider-wrap {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 200px;

    .volume-value {
        min-width: 24px;
        color: #1989fa;
        font-weight: 600;
        font-size: 15px;
        text-align: right;
    }
}

.volume-block {
    background: #fff;
    border-radius: 12px;
    margin: 16px;
    padding: 18px 18px 10px 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    display: flex;
    flex-direction: column;
    align-items: stretch;

    .volume-title {
        font-size: 15px;
        font-weight: 500;
        color: #323233;
        margin-bottom: 10px;
        text-align: left;
    }

    .van-slider {
        margin: 0 8px;
    }

    .volume-marks {
        display: flex;
        justify-content: space-between;
        margin-top: 6px;
        padding: 0 6px;

        .volume-mark {
            font-size: 12px;
            color: #969799;
            min-width: 12px;
            text-align: center;
            user-select: none;
        }
    }
}

// 音量卡片样式
.device-volume-card {
    background: #fff;
    border-radius: 12px;
    margin: 16px;
    padding: 18px 18px 18px 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

// 步进器按钮样式
.stepper-buttons {
    display: flex;
    gap: 8px;
    align-items: center;

    .van-button {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: bold;
        padding: 0;
        min-width: 32px;
    }
}

.device-volume-title {
    font-size: 15px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 10px;
    text-align: left;
}

.device-volume-slider-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.device-volume-label {
    font-size: 13px;
    color: #323233;
    min-width: 24px;
    text-align: center;
    font-weight: 500;
}

.device-volume-slider {
    flex: 1;
    margin: 0 8px;
}

.device-volume-dot {
    width: 28px;
    height: 28px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    position: relative;
}

.device-volume-dot::before {
    content: '';
    display: block;
    width: 16px;
    height: 4px;
    background: #e5e6eb;
    border-radius: 2px;
    position: absolute;
    left: 6px;
    top: 12px;
}

.device-volume-dot::after {
    content: '';
    display: block;
    width: 4px;
    height: 16px;
    background: #e5e6eb;
    border-radius: 2px;
    position: absolute;
    left: 12px;
    top: 6px;
}

.device-volume-marks {
    position: relative;
    height: 8px;
    margin-top: 2px;
    width: 100%;
}

.device-volume-mark {
    position: absolute;
    top: 50%;
    width: 2px;
    height: 2px;
    background: #1989fa;
    border-radius: 50%;
    transform: translateY(-50%);
}

// 靠左样式
.device-volume-card-left {
    max-width: 400px;
    margin-left: 16px;
    margin-right: 16px;
}

.device-volume-title-left {
    text-align: left;
}

.device-volume-slider-row-left {
    justify-content: flex-start;
    max-width: 320px;
}

// 网络状态文字样式
.network-status-text {
    font-size: 15px;
    font-weight: 500;

    &.status-offline {
        color: #ff6b35; // 离线状态橙色
    }

    &.status-online {
        color: #07c160; // 在线状态绿色
    }
}

.door-delay-row {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    min-width: 120px;
}

.door-delay-btn {
    width: 32px !important;
    height: 32px !important;
    border-radius: 6px !important;
    font-size: 18px !important;
    font-weight: bold !important;
    padding: 0 !important;
    min-width: 32px !important;
    background: #f7f8fa !important;
    color: #323233 !important;
    border: 1px solid #ebedf0 !important;
}

.door-delay-value {
    min-width: 28px;
    text-align: center;
    font-size: 16px;
    color: #323233;
    font-weight: 500;
    background: #fff;
    border-radius: 4px;
    padding: 0 6px;
    display: inline-block;
}

.custom-volume-slider {
    :deep(.van-slider__button) {
        width: 28px !important;
        height: 20px !important;
        background: transparent;
        top: -8px !important;
        left: -14px !important;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: none;
    }
}

.custom-slider-dot-small {
    min-width: 28px;
    height: 20px;
    background: #1989fa;
    color: #fff;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(25, 137, 250, 0.10);
    position: relative;
    top: 0;
    left: 0;
    z-index: 2;
    user-select: none;
    transition: background 0.2s;
    border: none;
    padding: 0 6px;
}

.device-volume-label {
    font-size: 13px;
    color: #323233;
    min-width: 24px;
    text-align: center;
    font-weight: 500;
}

.sysinfo-upgrade-btn {
    display: inline-block;
    background: #f4f7fa;
    color: #1989fa;
    font-size: 14px;
    border-radius: 12px;
    padding: 2px 14px;
    margin-right: 8px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    vertical-align: middle;
    transition: background 0.2s;
    user-select: none;
}
</style>