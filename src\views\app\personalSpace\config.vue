<template>
  <div class="personal-config-container">

    <!-- 配置表单 -->
    <div class="config-form">
      <van-form @submit="onSubmit" ref="formRef">
        <!-- Logo配置 -->
        <van-cell-group inset title="Logo设置">
          <!-- Logo上传组件 -->
          <div class="logo-upload-section">
            <div class="upload-label">上传Logo</div>
            <van-uploader v-model="uploadFiles" :max-count="1" :max-size="2 * 1024 * 1024" accept="image/*"
              @oversize="onOversize" @after-read="onLogoRead" @delete="onLogoDelete" upload-text="选择图片"
              :preview-size="80" />
            <div class="upload-tips">
              <van-notice-bar left-icon="info-o" text="建议上传正方形图片，支持JPG、PNG格式，文件大小不超过2MB" background="#fff7cc"
                color="#ed6a0c" />
            </div>
          </div>

          <!-- Logo预览 -->
          <div v-if="formData.logo" class="logo-preview-section">
            <div class="preview-container">
              <img :src="formData.logo" alt="Logo预览" class="logo-preview" />
              <div class="preview-actions">
                <van-button size="mini" type="danger" plain @click="removeLogo">删除Logo</van-button>
              </div>
            </div>
          </div>
        </van-cell-group>

        <!-- Slogan配置 -->
        <van-cell-group inset title="标语设置">
          <van-field v-model="formData.slogan" name="slogan" label="品牌标语" placeholder="请输入品牌标语（最多12个字符）" maxlength="12"
            show-word-limit :rules="sloganRules" clearable />

          <!-- Slogan预览 -->
          <div v-if="formData.slogan" class="slogan-preview-section">
            <div class="preview-label">标语预览：</div>
            <div class="slogan-preview">"{{ formData.slogan }}"</div>
          </div>
        </van-cell-group>

        <!-- 配置选项 -->
        <van-cell-group inset title="配置选项">
          <van-field name="switch" label="立即启用">
            <template #input>
              <van-switch v-model="formData.isActive" />
            </template>
          </van-field>

          <van-cell title="配置说明" :value="configDescription" />
        </van-cell-group>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <van-button type="default" block @click="saveDraft">
            保存草稿
          </van-button>
          <van-button type="primary" block native-type="submit" :loading="submitting">
            保存并应用
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
const formRef = ref()

// 表单数据
const formData = reactive({
  logo: '/img/default-logo.png', // 示例数据
  slogan: '让生活更美好',
  isActive: true
})

// 上传相关
const uploadFiles = ref([])
const submitting = ref(false)

// 表单验证规则
const sloganRules = [
  {
    validator: (value) => {
      if (!value) return true // 允许为空
      return value.length <= 12
    },
    message: '标语不能超过12个字符'
  }
]

// 方法

const onOversize = () => {
  showToast('文件大小不能超过2MB')
}

const onLogoRead = (file) => {
  console.log('选择的文件:', file)
  // 直接设置Logo
  if (file.content) {
    formData.logo = file.content
  } else if (file.file) {
    formData.logo = URL.createObjectURL(file.file)
  }
  showToast('Logo上传成功')
}

const onLogoDelete = () => {
  uploadFiles.value = []
  formData.logo = ''
  showToast('Logo已删除')
}



const removeLogo = async () => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除当前Logo吗？'
    })

    formData.logo = ''
    showToast('Logo已删除')
  } catch (error) {
    // 用户取消
  }
}

const saveDraft = async () => {
  try {
    await formRef.value.validate()

    //存草稿
    showToast('草稿保存成功')
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

const onSubmit = () => {
  submitting.value = true

  // 模拟保存配置
  setTimeout(() => {
    submitting.value = false
    showToast('配置保存成功')
  }, 1000)
}
</script>

<style lang="scss" scoped>
.personal-config-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.config-form {
  padding: 16px 0;
}

.logo-upload-section {
  padding: 16px;

  .upload-label {
    font-size: 14px;
    color: #646566;
    margin-bottom: 12px;
    font-weight: 500;
  }

  .upload-tips {
    margin-top: 12px;
  }
}

.logo-preview-section {
  padding: 16px;

  .preview-container {
    display: flex;
    align-items: center;
    gap: 16px;

    .logo-preview {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      object-fit: cover;
      border: 1px solid #ebedf0;
    }

    .preview-actions {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }
}

.slogan-preview-section {
  padding: 16px;

  .preview-label {
    font-size: 14px;
    color: #646566;
    margin-bottom: 8px;
  }

  .slogan-preview {
    font-size: 16px;
    color: #323233;
    font-weight: 500;
    font-style: italic;
    padding: 12px;
    background: #f7f8fa;
    border-radius: 8px;
    border-left: 4px solid #1989fa;
    text-align: center;
  }
}

.submit-section {
  padding: 24px 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}



:deep(.van-cell-group__title) {
  font-weight: 500;
  color: #323233;
}

:deep(.van-field__label) {
  color: #646566;
  font-weight: normal;
}

:deep(.van-uploader) {
  flex: 1;
}

:deep(.van-uploader__preview) {
  margin: 0 auto;
}
</style>
