// 选择器弹窗样式
.dining-mode-selector {
  background: #fff;
  
  .top-block {
    padding: 16px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    &.single-title {
      padding: 16px 20px;
      
      .title-text {
        color: #333;
        font-weight: 500;
        font-size: 17px;
      }
    }
  }
  
  .list-content {
    .single-select-list {
      .single-select-item {
        padding: 16px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        
        &:active {
          background-color: #f2f3f5;
        }
        
        .item-text {
          color: #323233;
          font-size: 16px;
        }
      }
    }
  }
}

// 时间选择器样式
.time-picker-container {
  background: #fff;
  
  .top-block {
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .cancel-btn {
      color: #969799;
      font-size: 14px;
      cursor: pointer;
      
      &:active {
        opacity: 0.7;
      }
    }
    
    .confirm-btn {
      color: #007fff;
      font-size: 14px;
      cursor: pointer;
      
      &:active {
        opacity: 0.7;
      }
    }
    
    .title-text {
      color: #333;
      font-weight: 500;
      font-size: 17px;
    }
  }
  
  .time-picker-content {
    padding: 20px 0;
    
    :deep(.van-picker) {
      background: #fff;
    }
    
    :deep(.van-picker__toolbar) {
      display: none; // 隐藏默认的工具栏，使用自定义的顶部操作栏
    }
  }
}
