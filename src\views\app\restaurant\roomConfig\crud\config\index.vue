<template>
    <div class="home-container">
        <yhc-form :config="basicFormConfig" @submit="onBasicSubmit" />
    </div>
</template>

<script setup>
import { showToast } from 'vant'
// 基础表单配置
const basicFormConfig = {
    button: {
        isShow: true,
    },
    postData: {}, // 添加必需的postData
    curl: {},
    groupForm: [
        [0, 2],
        [2, 4],
        [4, 5],
    ],
    form: [
        {
            label: "取消预定时间",
            key: "radio-map",
            component: "yhc-picker",
            default: 1,
            opts: {
                url: "",
                postData: {},
                merge: false,
                multiple: false,
                text_key: "title",
                contrast_key: "id",
                keyMap: "id",
                defaultList: [
                    { id: "1", title: "开餐时截止" },
                    { id: "2", title: "自定义截止" },
                ],
            },
            child: {
                showMode: true,
                map: {
                    "2": [
                        {
                            label: "截止时间",
                            key: "radio-showmode",
                            component: "yhc-radio-group",
                            options: [
                                { value: "all", label: "统一禁止" },
                                { value: "specific", label: "分餐禁止" }
                            ],
                            shape: "dot",
                            direction: "horizontal",
                            defaultValue: "all",
                            child: {
                                map: {
                                    "all": [
                                        {
                                            label: "时间",
                                            key: "speed_duration2",
                                            component: "yhc-picker-date",
                                            type: "time",
                                            required: true,
                                        },
                                    ],
                                    "specific": [
                                        {
                                            label: "早餐",
                                            key: "speed_duration2",
                                            component: "yhc-picker-date",
                                            type: "time",
                                            required: true,
                                        },
                                        {
                                            label: "午餐",
                                            key: "speed_duration2",
                                            component: "yhc-picker-date",
                                            type: "time",
                                            required: true,
                                        },
                                        {
                                            label: "晚餐",
                                            key: "speed_duration2",
                                            component: "yhc-picker-date",
                                            type: "time",
                                            required: true,
                                        },
                                    ]
                                }
                            }
                        },
                    ],
                },
                form: [],
            },
        },
        {
            label: "截止时间提前/天",
            key: "speed",
            component: "yhc-stepper",
            default: 2, // 默认值
            min: 1, // 最小值
            max: 10, // 最大值
            step: 1, // 步长
            theme: "default" // 样式 round:圆角 ，default：方形
        },
        {
            label: "审批",
            key: "speeding_alert",
            component: "yhc-switch",
        },
        {
            label: "说明：前往钉钉管理后台(https://oa.dingtalk.com)->工作台->应用管理->OA审批->进入->表单管理->云一消费-包间预定->编辑,按需进行流程设计",
            component: "yhc-desc",
            key: "approval-desc"
        }
    ]
}


// 表单提交处理函数
const onBasicSubmit = (data) => {
    console.log('基础表单提交:', data)
    console.log('复选框测试数据:', {
        'checkbox-test': data['checkbox-test'],
        'checkbox-vertical': data['checkbox-vertical']
    })
    console.log('单选框组测试数据:', {
        'radio-basic': data['radio-basic'],
        'radio-showmode': data['radio-showmode'],
        'radio-map': data['radio-map']
    })
    console.log('单选框组子表单数据:', {
        'specific-meal-times': data['specific-meal-times'],
        'department-name': data['department-name'],
        'user-name': data['user-name'],
        'user-id': data['user-id']
    })
    console.log('分段器测试数据:', {
        'segmented-test': data['segmented-test'],
        'segmented-showmode': data['segmented-showmode']
    })
    console.log('子表单数据:', {
        'personal-name': data['personal-name'],
        'personal-age': data['personal-age'],
        'work-company': data['work-company'],
        'work-position': data['work-position'],
        'contact-phone': data['contact-phone'],
        'contact-email': data['contact-email'],
        'additional-info': data['additional-info'],
        'remarks': data['remarks']
    })
    showToast('基础表单提交成功')
}
</script>

<style lang="scss" scoped>
.home-container {
    padding: 0;
    min-height: 100vh;
    // background: #f7f8fa;
}
</style>
