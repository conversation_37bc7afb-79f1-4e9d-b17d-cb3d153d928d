<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form :config="basicFormConfig" pageType="add" @onSubmit="onBasicSubmit" />
  </div>
</template>

<script setup>
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    dininghall_id: localStorage.getItem('dininghall'),
    // 如果有id参数，则为编辑模式
    // ...(route.query.id ? { id: route.query.id } : {})
  },
  curl: {
    add: '/private_room/post_add', // 新增接口
    edit: '/private_room/post_modify', // 编辑接口
    info: '/private_room/get_info' // 获取详情接口（编辑时需要）
  },
  groupForm: [
    [0, 2],
    [2, 3],
    [3, 4],
    [4, 5],
  ],
  form: [
    {
      label: "名称",
      key: "title",
      component: "yhc-input",
      required: true,
      rules: [{ required: true, message: "请填写包间名称" }],
    },
    {
      label: "人数",
      key: "capacity",
      component: "yhc-stepper",
      // default: 2, // 默认值
      min: 1, // 最小值
      // max: 10, // 最大值
      step: 1, // 步长
      theme: "default" // 样式 round:圆角 ，default：方形
    },
    {
      label: "标签",
      key: "tags",
      component: "yhc-tag-selector",
      // required: true,
      opts: {
        url: "/private_room_tag/get_all",
        text_key: "title",
        contrast_key: "title",
        maxTagLength: 12,
        maxCustomTags: 15,
        defaultList: [
        ],
        // keyMap: "title",
      },
      defaultSelected: [],
      addApi: {
        url: "/private_room_tag/post_add",
        method: "POST",
        postData: {},
      },
      deleteApi: {
        url: "/private_room_tag/post_del",
        method: "POST",
        postData: {},
      },
    },
    {
      label: "地址",
      key: "address",
      component: "yhc-input",
    },
    {
      label: "图片",
      key: "image",
      component: "yhc-select-image",
    },
  ]
}
const { proxy } = getCurrentInstance();
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: route.query.id ? '修改包间' : '新增包间',
  });
};
setRightA()

// // 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
