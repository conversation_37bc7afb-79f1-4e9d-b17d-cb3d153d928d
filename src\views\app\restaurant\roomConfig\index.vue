<template>
  <div class="wrapper">
    <div style="height: 2px;"></div>
    <div v-for="item in list" :key="item" class="van">
      <div class="frame"  @click="router_to(item)">
        <div class="text">
            {{ item.title }}
        </div>
        <img src="../../../../../public/img/arrow.svg" alt="" class="arrow">
    </div>
      <!-- <van-cell-group inset>
        <van-field
          readonly
          right-icon="arrow"
          input-align="right"
          :label="item.title"
          @click="router_to(item)"
        />
      </van-cell-group> -->
    </div>
  </div>
</template>
<script setup>
import { ref, createApp, onBeforeMount, getCurrentInstance } from "vue";
import { Field, CellGroup } from "vant";

const app = createApp();
app.use(Field, CellGroup);
const router = useRouter();
const { proxy } = getCurrentInstance();
const list = ref([
  {
    title: "包间档案",
    name: "roomManagement",
  },
  {
    title: "表单设计",
    name: "formConfig",
  },
  {
    title: "套餐组合",
    name: "package",
  },
  {
    title: "配置中心",
    name: "config",
  },
]);
const router_to = (op) => {
  // console.log(op, "op");
  router.push({
    name: op.name,
  });
};
const setRight = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title:'包间配置',
  });
};
setRight()
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  min-height: 100vh;
  background: #f6f6f6;  
  .frame {
    margin: 12px;
    height: 54px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: #FFFFFF;
    .text {
        flex-shrink: 0;
        // width: 287px;
        height: 22px;
        color: #171A1D;
        font-family: "PingFang SC";
        font-size: 16px;
        line-height: 22px;
    }
    .arrow {
        flex-shrink: 0;
        // width: 16px;
        height: 16px;
    }
}
}
</style>
