<template>
  <div class="startup-config-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h3>启动页配置</h3>
      <p>设置应用启动页的图片和显示参数</p>
    </div>

    <!-- 配置表单 -->
    <div class="config-form">
      <!-- 启动页预览 -->
      <div class="preview-section">
        <div class="section-title">启动页预览</div>
        <div class="startup-preview" :style="previewStyle">
          <img v-if="form.imageUrl" :src="form.imageUrl" alt="启动页预览" />
          <div v-else class="placeholder">
            <van-icon name="photo-o" size="60" />
            <p>暂无启动页图片</p>
          </div>
          <div v-if="form.showLogo" class="logo-overlay">
            <div class="logo-placeholder">Logo</div>
          </div>
        </div>
      </div>

      <!-- 图片上传 -->
      <div class="form-section">
        <div class="section-title">启动页图片</div>
        <van-uploader
          v-model="fileList"
          :max-count="1"
          :preview-size="100"
          accept="image/*"
          @after-read="onImageUpload"
          @delete="onImageDelete"
        >
          <van-button icon="plus" type="primary" size="small">上传图片</van-button>
        </van-uploader>
        <div class="upload-tips">
          <p>建议上传尺寸：750x1334px 或 1125x2436px</p>
          <p>支持格式：JPG、PNG，文件大小不超过2MB</p>
        </div>
      </div>

      <!-- 尺寸配置 -->
      <div class="form-section">
        <div class="section-title">尺寸设置</div>
        <van-cell-group inset>
          <van-field
            v-model="form.width"
            label="宽度"
            placeholder="请输入宽度"
            type="number"
          >
            <template #right-icon>
              <span class="unit">px</span>
            </template>
          </van-field>
          <van-field
            v-model="form.height"
            label="高度"
            placeholder="请输入高度"
            type="number"
          >
            <template #right-icon>
              <span class="unit">px</span>
            </template>
          </van-field>
          <van-cell title="保持宽高比" center>
            <template #right-icon>
              <van-switch v-model="form.keepAspectRatio" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      <!-- 显示设置 -->
      <div class="form-section">
        <div class="section-title">显示设置</div>
        <van-cell-group inset>
          <van-field
            v-model="form.displayTime"
            label="显示时长"
            placeholder="请输入显示时长"
            type="number"
          >
            <template #right-icon>
              <span class="unit">秒</span>
            </template>
          </van-field>
          <van-field
            v-model="form.fadeInTime"
            label="淡入时间"
            placeholder="请输入淡入时间"
            type="number"
          >
            <template #right-icon>
              <span class="unit">毫秒</span>
            </template>
          </van-field>
          <van-cell title="显示Logo" center>
            <template #right-icon>
              <van-switch v-model="form.showLogo" />
            </template>
          </van-cell>
          <van-cell title="可点击跳过" center>
            <template #right-icon>
              <van-switch v-model="form.clickable" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      <!-- 背景设置 -->
      <div class="form-section">
        <div class="section-title">背景设置</div>
        <van-cell-group inset>
          <van-field
            v-model="form.backgroundColor"
            label="背景颜色"
            placeholder="选择背景颜色"
            readonly
            is-link
            @click="showColorPicker = true"
          >
            <template #right-icon>
              <div class="color-preview" :style="{ backgroundColor: form.backgroundColor }"></div>
            </template>
          </van-field>
          <van-field
            v-model="form.backgroundMode"
            label="背景模式"
            placeholder="选择背景模式"
            readonly
            is-link
            @click="showModePicker = true"
          />
        </van-cell-group>
      </div>

      <!-- 高级设置 -->
      <div class="form-section">
        <div class="section-title">高级设置</div>
        <van-cell-group inset>
          <van-cell title="启用动画效果" center>
            <template #right-icon>
              <van-switch v-model="form.enableAnimation" />
            </template>
          </van-cell>
          <van-cell title="自动适配屏幕" center>
            <template #right-icon>
              <van-switch v-model="form.autoFit" />
            </template>
          </van-cell>
          <van-cell title="缓存图片" center>
            <template #right-icon>
              <van-switch v-model="form.cacheImage" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <van-button type="primary" block @click="saveConfig" :loading="saving">
        保存配置
      </van-button>
      <van-button block @click="previewStartup" style="margin-top: 12px;">
        预览效果
      </van-button>
    </div>

    <!-- 颜色选择器 -->
    <van-popup v-model:show="showColorPicker" position="bottom">
      <div class="color-picker-container">
        <div class="picker-header">选择背景颜色</div>
        <div class="color-options">
          <div 
            v-for="color in colorOptions" 
            :key="color"
            class="color-option"
            :class="{ active: form.backgroundColor === color }"
            :style="{ backgroundColor: color }"
            @click="selectColor(color)"
          ></div>
        </div>
        <van-button type="primary" block @click="showColorPicker = false" style="margin: 16px;">
          确定
        </van-button>
      </div>
    </van-popup>

    <!-- 背景模式选择器 -->
    <van-popup v-model:show="showModePicker" position="bottom">
      <van-picker
        :columns="modeOptions"
        @confirm="onModeConfirm"
        @cancel="showModePicker = false"
      />
    </van-popup>

    <!-- 预览弹窗 -->
    <van-popup 
      v-model:show="showPreview" 
      :style="{ width: '100%', height: '100%' }"
      :overlay-style="{ backgroundColor: 'rgba(0,0,0,0.9)' }"
    >
      <div class="preview-popup">
        <div class="preview-content" :style="previewStyle">
          <img v-if="form.imageUrl" :src="form.imageUrl" alt="启动页预览" />
          <div v-if="form.showLogo" class="logo-overlay">
            <div class="logo-placeholder">Logo</div>
          </div>
        </div>
        <van-button 
          type="primary" 
          @click="showPreview = false"
          style="position: absolute; bottom: 50px; left: 50%; transform: translateX(-50%);"
        >
          关闭预览
        </van-button>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, getCurrentInstance, onMounted } from 'vue'
import { showToast, showSuccessToast } from 'vant'

const router = useRouter()
const { proxy } = getCurrentInstance()

// 响应式数据
const fileList = ref([])
const showColorPicker = ref(false)
const showModePicker = ref(false)
const showPreview = ref(false)
const saving = ref(false)

// 表单数据
const form = reactive({
  imageUrl: '',
  width: '375',
  height: '667',
  keepAspectRatio: true,
  displayTime: '3',
  fadeInTime: '500',
  showLogo: true,
  clickable: true,
  backgroundColor: '#ffffff',
  backgroundMode: '拉伸填充',
  enableAnimation: true,
  autoFit: true,
  cacheImage: true
})

// 颜色选项
const colorOptions = [
  '#ffffff', '#f7f8fa', '#323233', '#969799', 
  '#1989fa', '#07c160', '#ff976a', '#ee0a24'
]

// 背景模式选项
const modeOptions = [
  { text: '拉伸填充', value: 'stretch' },
  { text: '等比缩放', value: 'contain' },
  { text: '等比裁剪', value: 'cover' },
  { text: '居中显示', value: 'center' }
]

// 预览样式
const previewStyle = computed(() => ({
  width: form.width ? `${form.width}px` : '200px',
  height: form.height ? `${form.height}px` : '356px',
  backgroundColor: form.backgroundColor,
  backgroundSize: form.backgroundMode === '拉伸填充' ? '100% 100%' : 
                  form.backgroundMode === '等比缩放' ? 'contain' :
                  form.backgroundMode === '等比裁剪' ? 'cover' : 'auto'
}))

// 图片上传处理
const onImageUpload = (file) => {
  // 验证文件大小
  if (file.file && file.file.size > 2 * 1024 * 1024) {
    showToast('图片大小不能超过2MB')
    return
  }
  
  console.log('上传启动页图片:', file)
  form.imageUrl = file.content || file.file
  fileList.value = [file]
  showToast('图片上传成功')
}

// 图片删除处理
const onImageDelete = () => {
  form.imageUrl = ''
  showToast('图片已删除')
}

// 选择颜色
const selectColor = (color) => {
  form.backgroundColor = color
}

// 背景模式确认
const onModeConfirm = ({ selectedOptions }) => {
  form.backgroundMode = selectedOptions[0].text
  showModePicker.value = false
}

// 预览启动页
const previewStartup = () => {
  if (!form.imageUrl) {
    showToast('请先上传启动页图片')
    return
  }
  showPreview.value = true
}

// 保存配置
const saveConfig = async () => {
  if (!form.imageUrl) {
    showToast('请先上传启动页图片')
    return
  }

  saving.value = true
  try {
    // 这里应该调用保存接口
    await new Promise(resolve => setTimeout(resolve, 1000))
    showSuccessToast('启动页配置保存成功')
    router.back()
  } catch (error) {
    showToast('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 设置页面标题
const setPageTitle = () => {
  if (proxy && proxy.$_dd) {
    proxy.$_dd.biz.navigation.setTitle({
      title: '启动页配置',
    })
  }
}

// 加载已有配置
const loadConfig = async () => {
  try {
    // 这里应该调用获取配置的接口
    // const config = await getStartupConfig()
    // Object.assign(form, config)
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

onMounted(() => {
  setPageTitle()
  loadConfig()
})
</script>

<style lang="scss" scoped>
.startup-config-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 80px;
}

.page-header {
  background: white;
  padding: 20px 16px;
  border-bottom: 1px solid #ebedf0;

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #323233;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #969799;
  }
}

.config-form {
  padding: 16px;
}

.form-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 12px;
    padding-left: 4px;
  }
}

.preview-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 12px;
    padding-left: 4px;
  }

  .startup-preview {
    background: white;
    border: 2px dashed #dcdee0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .placeholder {
      text-align: center;
      color: #c8c9cc;

      .van-icon {
        margin-bottom: 12px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }

    .logo-overlay {
      position: absolute;
      top: 50px;
      left: 50%;
      transform: translateX(-50%);

      .logo-placeholder {
        background: rgba(255, 255, 255, 0.9);
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 12px;
        color: #323233;
      }
    }
  }
}

.upload-tips {
  margin-top: 8px;
  
  p {
    margin: 4px 0;
    font-size: 12px;
    color: #969799;
  }
}

.unit {
  color: #969799;
  font-size: 14px;
  margin-left: 4px;
}

.color-preview {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #ebedf0;
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #ebedf0;
}

.color-picker-container {
  background: white;
  
  .picker-header {
    padding: 16px;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #ebedf0;
  }

  .color-options {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    padding: 20px;

    .color-option {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      border: 2px solid transparent;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        border-color: #1989fa;
        transform: scale(1.1);
      }
    }
  }
}

.preview-popup {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .preview-content {
    max-width: 90%;
    max-height: 80%;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .logo-overlay {
      position: absolute;
      top: 50px;
      left: 50%;
      transform: translateX(-50%);

      .logo-placeholder {
        background: rgba(255, 255, 255, 0.9);
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 12px;
        color: #323233;
      }
    }
  }
}

:deep(.van-uploader__upload) {
  margin: 0;
}

:deep(.van-cell-group--inset) {
  margin: 0;
}
</style>
