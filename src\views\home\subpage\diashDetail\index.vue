<template>
  <div class="window-info" v-if="data.typeList.length">
    <div class="left">
      <div
        :class="`repast-time ${i === data.curTypeIndex ? 'active-repast' : ''}`"
        v-for="(item, i) in data.typeList"
        :key="i"
        @click="onTypeClick(i)"
      >
        <div class="select-line" v-show="i === data.curTypeIndex"></div>
        <div class="text-info">
          <div class="icon">
            <!-- <van-icon name="qr" class="van-haptics-feedback" size="24" /> -->
          </div>
          <div class="repast-text">{{ item.category_title }}</div>
        </div>
      </div>
    </div>
    <div class="right">
      <div
        class="window-dish"
        v-for="(item, i) in data.dishList"
        :key="item.id + i"
      >
        <van-image
          v-if="item.image"
          width="90"
          height="90"
          radius="4"
          :src="
            item.image ||
            'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
          "
        />
        <div
          v-else
          style="
            width: 90px;
            height: 90px;
            border-radius: 4px;
            background: #007fff;
            color: #fff;
            font-size: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
          "
        >
          {{ item.title[0] }}
        </div>
        <div class="info-block">
          <div>{{ item.title }}</div>
          <div
            v-if="item.desc"
            style="margin: 2px 0; font-size: 10px; color: rgba(23, 26, 29, 0.4)"
          >
            {{ item.desc }}
          </div>
          <!-- <div
            v-if="item.stock_limit_text"
            style="margin: 2px 0; font-size: 10px; color: rgba(23, 26, 29, 0.4)"
          >
            限购：{{ item.stock_limit_text }}
          </div> -->
          <!-- <div
            style="margin: 2px 0; font-size: 10px; color: rgba(23, 26, 29, 0.4)"
          >
            {{ item.desc }}
          </div> -->
          <div class="bottom">
            <div style="color: rgba(23, 26, 29, 0.4); font-size: 12px">
              <span style="font-size: 14px; color: #000"
                >￥{{ item.price + "  " }}</span
              >/份
            </div>
           
          </div>
        </div>
      </div>
    </div>
  </div>
  <van-empty v-else description="未发布菜品~" style="height: 200px">
  </van-empty>
</template>
<script setup>
import { ref, reactive, getCurrentInstance, computed } from "vue";
import { showNotify, showToast, closeNotify, ActionSheet } from "vant";
import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
let data = reactive({
  showBottom: false,
  curTypeIndex: 0,
  selectList: app.orderDishSelectList || new Set(),
  typeList: [
    // {
    //   category_id: 23,
    //   category_title: "蔬菜",
    //   dishess: [
    //     {
    //       id: 23,
    //       desc: "",
    //       image: null,
    //       price: "5.00",
    //       title: "西蓝花",
    //       category_id: 23,
    //       category_title: "蔬菜",
    //     },
    //     {
    //       id: 24,
    //       desc: "",
    //       image: null,
    //       price: "5.00",
    //       title: "面包",
    //       category_id: 24,
    //       category_title: "蔬菜",
    //     },
    //   ],
    // },
    // {
    //   category_id: 24,
    //   category_title: "肉",
    //   dishess: [
    //     {
    //       id: 24,
    //       desc: "",
    //       image: null,
    //       price: "5.00",
    //       title: "牛肉",
    //       category_id: 24,
    //       category_title: "蔬菜",
    //     },
    //   ],
    // },
  ],
  dishList: [
    // {
    //   id: 23,
    //   desc: "skdjfk lskjdflskjflkjsalfj alskjf 萨拉开发框架阿斯弗啊沙发上劳动法卡死了kj",
    //   image: null,
    //   price: "5.00",
    //   title: "西蓝花",
    //   quota: 3,
    //   category_id: 23,
    //   category_title: "蔬菜",
    // },
  ],
});
function getImageUrl(name) {
  return new URL(name, import.meta.url).href;
}
const onSettlementClick = () => {
  if (!data.selectList.size) {
    showToast(JSON.stringify("请选择菜品"));
  } else {
    app.orderDishSelectList = data.selectList;
    router.push({
      path: "/confirmOrder",
      query: route.query,
    });
  }
};
let total = computed(() => {
  let total = 0;
  for (let el of data.selectList) {
    total += el.price * 1 * (el.count * 1);
  }
  return total.toFixed(2);
});
const onTypeClick = (i) => {
  data.curTypeIndex = i;
  data.dishList = data.typeList[i].dishess;
};
const onGoodsClear = (i) => {
  for (let el of data.selectList) {
    el.count = 0;
  }
  data.selectList.clear();
};
const onChange = (item) => {
  if (item.count <= 0) {
    let ls = [];
    data.selectList.forEach((el) => {
      if (item.id == el.id) {
        ls.push(el);
      }
    });
    ls.forEach((el) => {
      data.selectList.delete(el);
    });
  } else if (item.count > 0) {
    let ls = [];
    data.selectList.forEach((el) => {
      if (item.id == el.id) {
        el.count = item.count;
        ls.push(el);
      }
    });
    if (!ls.length) {
      data.selectList.add(item);
    }
  }
};
const getMenu = (item) => {
  let res = Object.entries(app.indexDishDetail).map((el) => ({
    category_title: el[0],
    dishess: el[1],
  }));
  data.typeList = res;
  data.dishList = res[0].dishess;
};
getMenu(route.query);
</script>
<style scoped lang="scss">
.window-info {
  position: relative;
  .left {
    position: absolute;
    left: 0;
    top: 0;
    width: 74px;
    overflow: scroll;
    height: 100vh;
    padding-bottom: 120px;

    .repast-time {
      height: 87px;
      width: 100%;
      display: flex;
      .select-line {
        width: 3px;
        background: #007fff;
      }
      .text-info {
        flex: 1;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        font-size: 14px;
        .repast-text {
          font-size: 14px;
          margin-top: 15px;
        }
      }
    }
    .active-repast {
      background: #fff;
    }
  }
  .right {
    margin-left: 74px;
    height: 100vh;
    overflow: scroll;
    background: #fff;
    padding-bottom: 120px;
    .window-dish {
      display: flex;
      justify-content: space-between;
      padding: 12px;
      .info-block {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 8px;
        .bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }
    }
  }
  .goods-bar {
    padding: 0 12px;
    position: fixed;
    bottom: 16px;
    left: 0;
    right: 0;
    height: 58px;
    border-radius: 29px;
    background: #ffffff;
    box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 16px;
    .total {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .popup-top-block {
    color: rgba(23, 26, 29, 0.6);
    font-size: 12px;
    display: flex;
    align-items: center;
    padding: 18px 0;
    margin: 0 12px;
    border-bottom: 1px solid #f6f6f6;
    justify-content: flex-end;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }
  .popup-list {
    height: calc(100% - 59px);
    margin-top: 59px;
  }
}
</style>
