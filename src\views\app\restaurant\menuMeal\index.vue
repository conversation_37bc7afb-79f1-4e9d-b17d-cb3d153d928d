<template>
  <div class="all">
    <van-tabs v-model:active="active" 
    >
      <van-tab v-for="(item, i) in list" :key="i" :title="item.title">
       <div style="margin-top: 16px;">
        <div class="price-container">
          <van-field
            v-for="stall in list[active].stalls"
            :key="stall.stall_id"
            v-model="stall.price"
            :label="stall.title"
            input-align="left"
            placeholder="未设置"
            type="number"
            @blur="handlePriceBlur(stall, $event)"
          >
            <template #right-icon>
              <div style="display: flex; align-items: center; gap: 8px;">
              <!-- 当price有值时显示删除按钮 -->
                <van-icon
                  v-if="stall.price && stall.price !== '' && stall.price !== 0"
                  name="close"
                  size="16"
                  @click="showDeleteDialog(stall)"
              
                />
                <span style="color: #323233; font-size: 15px">元</span>
                
              </div>
            </template>
          </van-field>
        </div>
        </div>
      </van-tab>
    </van-tabs>
    <div v-if="loading" style="text-align: center; margin-top: 50px;">
      <van-loading size="24px">加载中...</van-loading>
    </div>
    <div v-else-if="list.length == 0" style="text-align: center; margin-top: 50px;">
      <van-empty description="暂无数据" />
    </div>

    <!-- 删除确认弹窗 -->
    <van-dialog
      v-model:show="showDeleteConfirm"
      title="确定删除"
      message="确定将立即删除，此操作不可逆，是否继续？"
      show-cancel-button
      cancel-button-text="取消"
      confirm-button-text="删除"
      confirm-button-color="#ee0a24"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import { ref, onBeforeMount, getCurrentInstance, nextTick } from "vue";
import { showToast } from "vant";


const router = useRouter();

const active = ref(0);
const loading = ref(false);
const list = ref([]);

// 删除确认弹窗相关状态
const showDeleteConfirm = ref(false);
const currentDeleteStall = ref(null);

// 显示删除确认弹窗
const showDeleteDialog = (stallItem) => {
  currentDeleteStall.value = stallItem;
  showDeleteConfirm.value = true;
};

// 取消删除
const cancelDelete = () => {
  showDeleteConfirm.value = false;
  currentDeleteStall.value = null;
};

// 确认删除
const confirmDelete = async () => {
  if (!currentDeleteStall.value) return;

  const stallItem = currentDeleteStall.value;
  const currentMeal = list.value[active.value];

  // 构建删除请求参数
  const deleteParams = {
    dininghall_id: stallItem.dininghall_id,
    mealtime_id: currentMeal.mealtime_id,
    stall_id: stallItem.stall_id
  };

  try {
    const res = await proxy.$post("/dininghall_cost/post_del", deleteParams);

    if (res.code === 200) {
      showToast('删除成功');

      // 删除成功后刷新数据
      await fetchMealData();
    } else {
      throw new Error(res.msg || '删除失败');
    }
  } catch (err) {
    console.error('删除失败:', err);
    showToast(err.msg || '删除失败，请重试');
  } finally {
    // 关闭弹窗并清理状态
    showDeleteConfirm.value = false;
    currentDeleteStall.value = null;
  }
};

// 处理价格失焦事件，调用API
const handlePriceBlur = (stallItem, event) => {
  // 获取当前选中的餐时数据
  const currentMeal = list.value[active.value];

  // 记录原始价格状态，判断是新增还是修改
  const originalPrice = stallItem.originalPrice; // 原始价格状态
  const currentPrice = stallItem.price;

  // 如果用户开始输入但又删除了所有内容，阻止失焦并提示
  if (currentPrice === '' && originalPrice !== '未设置餐费') {
    showToast('请输入有效的价格，不能为空');
    // 重新聚焦到输入框
    nextTick(() => {
      if (event && event.target) {
        event.target.focus();
      }
    });
    return;
  }

  // 如果当前价格为空且原始状态也是未设置，允许失焦但不调用接口
  if (!currentPrice || currentPrice === '') {
    console.log('当前价格为空，不调用接口');
    return;
  }

  // 验证价格格式：只允许数字和最多两位小数
  const priceRegex = /^\d+(\.\d{1,2})?$/;
  if (!priceRegex.test(currentPrice)) {
    showToast({
      message: '请输入正确的金额格式（如：10 或 10.50）',
      type: 'fail'
    });
    // 重新聚焦到输入框
    nextTick(() => {
      if (event && event.target) {
        event.target.focus();
      }
    });
    return;
  }

  // 格式化价格显示为2位小数
  let numericPrice = parseFloat(currentPrice);
  if (isNaN(numericPrice)) {
    showToast({
      message: '请输入有效的价格',
      type: 'fail'
    });
    return;
  }

  // 格式化显示
  stallItem.price = numericPrice.toFixed(2);

  // 构建API请求数据
  const requestData = {
    dininghall_id: stallItem.dininghall_id,
    mealtime_id: currentMeal.mealtime_id,
    stall_id: stallItem.stall_id,
    price: numericPrice // 发送number格式的价格
  };

  // 如果原始价格为"未设置餐费"为新增
  const isAdd = originalPrice === '未设置餐费';

  // 检查价格是否真正发生了变化
  const originalNumericPrice = isAdd ? null : parseFloat(originalPrice);
  const priceChanged = isAdd || (originalNumericPrice !== numericPrice);

  const apiUrl = isAdd ? "/dininghall_cost/post_add" : "/dininghall_cost/post_modify";
  const actionText = isAdd ? '新增' : '更新';

  // 调用API接口
  proxy
    .$post(apiUrl, requestData)
    .then((res) => {
      console.log(`价格${actionText}成功:`, res);

      // 只有当价格真正发生变化时才显示成功提示
      if (priceChanged) {
        showToast(`价格${actionText}成功`);
      }

      // 新增成功后，更新原始价格状态
      if (isAdd) {
        stallItem.originalPrice = stallItem.price;
      }
    })
    .catch((err) => {
      console.log(`价格${actionText}失败:`, err);
      showToast(`价格${actionText}失败，请重试`);
    });
};

// 获取餐时档口数据
const { proxy } = getCurrentInstance();

// 获取餐时档口数据的API调用
const fetchMealData = async () => {
  loading.value = true;
  const dininghall_id = localStorage.getItem('dininghall');

  try {
    const res = await proxy.$get("/dininghall_cost/get_detail", {dininghall_id:dininghall_id});

    // 检查响应格式
    if (res.code === 200 && res.data) {
      // 处理数据，记录原始价格状态
      const processedData = res.data.map(meal => ({
        ...meal,
        stalls: meal.stalls.map(stall => ({
          ...stall,
          originalPrice: stall.price // 记录原始价格状态，用于判断新增还是修改
        }))
      }));
      list.value = processedData;
    } else {
      console.log('数据格式:', res);
      showToast(res.msg || ' 数据加载异常');
    }
  } catch (err) {
    console.error('获取餐时数据失败:', err);

    list.value = processedMockData;
  } finally {
    loading.value = false;
  }
};

onBeforeMount(() => {
  fetchMealData();
});
const setRight=()=>{ 
  proxy.$_dd.biz.navigation.setTitle({
    title:'餐费设置',
  });
}
</script>

<style lang="scss" scoped>
.all {
  height: 100vh;
  background: #f6f6f6;
  font-size: 16px;
  // padding-top: 15px;
  .all-top {
    height: 50px;
    background: #ffffff;
    // padding: 12px 16px;
    // margin-bottom: 16px;
    border-bottom: 0.5px solid #f6f6f6;
  }
  .price-container {
    background: #ffffff;
    border-radius: 8px;
    margin: 0 16px;
    overflow: hidden;
    .van-field{
      font-size: 16px;
    }

  }

  .children {
    border-radius: 8px;
    background: #f6f6f6;
    align-items: center;
    margin: 0 16px;
    border-bottom: 0.5px solid #f6f6f6;
  }

}
</style>
