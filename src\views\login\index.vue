<template>
  <div class="wrapper">
    <van-image
      width="100"
      height="100"
      radius="4"
      src="/icons/yunyizhifu.png"
    />
    <van-loading type="spinner" size="20">{{ text }}...</van-loading>
  </div>
</template>
<script setup>
import { ref } from "vue";

import { useLoginStore } from "@/store/dingLogin";
import { showToast} from "vant";
const router = useRouter();
const route = useRoute();
const app = useLoginStore();
let { query } = route;

// console.log("当前环境---------->", route, query, window);
let text = ref("登录中");
function handleRouter() {
  text.value = "登录成功";
  setTimeout(() => {
    // console.log("登录成功---state状态-----》",window.history.state);
    if (window.history.state.back) {
      router.go(-1);
    } else {
      router.replace({ path: "/home" });
      //判断产品授权是否再次展示
      // sessionStorage.setItem("is_expire", 0);
    }
  }, 1500);
}
if (app.browserEnv == "wx") {
  app.getWxAuthCode().then((res) => {
    handleRouter();
  });
} else {
  if (query.corpid) {
    app.corpId = query.corpid;
  }
  app
    .h5Login()
    .then((res) => {
      handleRouter();
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    });
}
</script>
<style lang="scss" scoped>
.wrapper {
  width: 100%;
  min-height: 100vh;
  text-align: center;
  .van-image {
    margin-top: 30%;
  }
  .van-loading {
    margin-top: 20px;
  }
}
</style>
