<template>
  <div class="running-tab-container">
    <!-- 页面标题 -->
    <!-- <van-nav-bar title="交易流水" left-arrow @click-left="$router.go(-1)" /> -->

    <!-- 交易类型列表 -->
    <div class="transaction-list">
      <van-cell-group inset>
        <van-cell
          title="储值账户"
          is-link
          @click="goToDetail(1)"
        />
      </van-cell-group>

      <van-cell-group inset>
        <van-cell
          title="补贴账户"
          is-link
          @click="goToDetail(2)"
        />
      </van-cell-group>

      <van-cell-group inset>
        <van-cell
          title="充享后付"
          is-link
          @click="goToDetail(3)"
        />
      </van-cell-group>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

// 跳转到详情页面
const goToDetail = (type) => {
  router.push({
    path: '/finance/runningTab/savingsAccount',
    query: { type }
  })
}
</script>

<style lang="scss" scoped>
.running-tab-container {
  background: #f2f3f4;
  padding-bottom: 20px;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}

.transaction-list {
  padding: 0;
  width: 100%;
  box-sizing: border-box;

  :deep(.van-cell-group--inset) {
    margin: 16px;

    &:first-child {
      margin-top: 16px;
    }

    &:last-child {
      margin-bottom: 16px;
    }
  }

  :deep(.van-cell) {
    font-size: 16px;
    padding: 16px;
  }
}
</style>