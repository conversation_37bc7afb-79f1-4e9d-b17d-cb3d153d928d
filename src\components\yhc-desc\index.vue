<template>
  <div class="yhc-desc" @click="handleClick">

    <!-- 图标 -->
    <van-icon v-if="config.icon" :name="config.icon" class="yhc-desc__icon" :size="config.iconSize"
      :color="config.iconColor" />

    <!-- 文本内容 -->
    <div class="yhc-desc__content">
      <!-- 标题 -->
      <div v-if="config.title" class="yhc-desc__title">
        {{ config.title }}
      </div>

      <!-- 主要文本 -->
      <div class="yhc-desc__text">
        {{ config.label }}
      </div>

      <!-- 链接文本 -->
      <div v-if="config.linkText" class="yhc-desc__link" @click.stop="handleLinkClick">
        {{ config.linkText }}
      </div>
    </div>

    <!-- 右侧箭头 -->
    <van-icon v-if="config.showArrow" name="arrow" class="yhc-desc__arrow" size="16px" color="#c8c9cc" />
  </div>
</template>

<script setup>
import { deepAssign } from "@/untils";

// 默认配置
let config = {
  // 内容配置
  label: "提示文本",       // 主要文本内容 (字符串) - 描述组件显示的主要文字内容
  title: "",               // 标题文本 (字符串) - 显示在主要内容上方的标题文字
  linkText: "",            // 链接文本 (字符串) - 可点击的链接文字，显示在主要内容右侧

  // 类型和样式配置
  type: "default",         // 组件类型 (字符串) - "default": 默认, "info": 信息, "warning": 警告, "error": 错误, "success": 成功

  // 图标配置
  icon: "",                // 图标名称 (字符串) - 显示在文本左侧的图标名称，如"info-o", "warning-o"
  iconSize: "16px",        // 图标大小 (字符串) - 图标的尺寸，如"16px", "20px"
  iconColor: "",           // 图标颜色 (字符串) - 图标的颜色，为空时使用主题色

  // 交互配置
  clickable: false,        // 是否可点击 (布尔值) - true: 整个组件可点击, false: 不可点击
  showArrow: false,        // 是否显示右侧箭头 (布尔值) - true: 显示右箭头, false: 不显示
  onClick: null,           // 点击回调函数 (函数) - 组件被点击时的回调函数
  onLinkClick: null,       // 链接点击回调函数 (函数) - 链接文本被点击时的回调函数

  // 样式配置
  margin: "0",             // 外边距 (字符串) - 组件的外边距，如"10px", "5px 10px"
  backgroundColor: "#F2F3F4", // 背景色 (字符串) - 组件的背景颜色
  textColor: "#969799",    // 文字颜色 (字符串) - 文本的颜色
  fontSize: "12px",        // 字体大小 (字符串) - 文字的大小
  lineHeight: "1.5",       // 行高 (字符串) - 文字的行高
  borderRadius: "5px",     // 圆角 (字符串) - 组件的圆角大小
};

const props = defineProps({
  config: Object,
  form: Object,
});

// 合并配置
props.config && deepAssign(config, props.config);

// 点击处理
const handleClick = () => {
  if (config.clickable && config.onClick) {
    config.onClick();
  }
};

// 链接点击处理
const handleLinkClick = () => {
  if (config.onLinkClick) {
    config.onLinkClick();
  }
};
</script>

<style lang="scss" scoped>
.yhc-desc {
  // display: flex;
  // align-items: flex-start;
  background-color: v-bind('config.backgroundColor');
  color: v-bind('config.textColor');
  font-size: v-bind('config.fontSize');
  line-height: v-bind('config.lineHeight');
  // padding: v-bind('config.padding');
  padding-left: 16px;
  margin: 5px 0 5px 0;
  border-radius: 5px;
  // transition: all 0.3s ease;

  // 兼容旧版本样式 - 修复影响其他组件的问题
  // margin-left: -32px;
  // text-indent: 40px;

  //   &--clickable {
  //     cursor: pointer;

  //     &:hover {
  //       background-color: #e8e9ea;
  //     }

  //     &:active {
  //       background-color: #dcdee0;
  //     }
  //   }

  //   // 不同类型的样式
  //   &--info {
  //     background-color: #f0f9ff;
  //     color: #1989fa;
  //     border-left: 3px solid #1989fa;
  //   }

  //   &--warning {
  //     background-color: #fff7e6;
  //     color: #ff976a;
  //     border-left: 3px solid #ff976a;
  //   }

  //   &--error {
  //     background-color: #fff1f0;
  //     color: #ee0a24;
  //     border-left: 3px solid #ee0a24;
  //   }

  //   &--success {
  //     background-color: #f6ffed;
  //     color: #07c160;
  //     border-left: 3px solid #07c160;
  //   }

  //   &__icon {
  //     flex-shrink: 0;
  //     margin-right: 8px;
  //     margin-top: 2px;
  //   }

  //   &__content {
  //     flex: 1;
  //     min-width: 0;
  //   }

  //   &__title {
  //     font-weight: 500;
  //     margin-bottom: 4px;
  //     color: rgba(0, 0, 0, 0.85);
  //   }

  //   &__text {
  //     word-wrap: break-word;
  //     word-break: break-all;
  //   }

  //   &__link {
  //     color: #1989fa;
  //     cursor: pointer;
  //     margin-top: 4px;
  //     text-decoration: underline;

  //     &:hover {
  //       color: #0570c9;
  //     }
  //   }

  //   &__arrow {
  //     flex-shrink: 0;
  //     margin-left: 8px;
  //     margin-top: 2px;
  //   }
  // }

  // // 响应式设计
  // @media (max-width: 375px) {
  //   .yhc-desc {
  //     padding: 10px 12px;
  //     font-size: 13px;

  //     &__icon {
  //       margin-right: 6px;
  //     }
  //   }
}
</style>
