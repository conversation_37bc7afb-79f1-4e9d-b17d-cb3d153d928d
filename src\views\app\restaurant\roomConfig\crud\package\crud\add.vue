<template>
  <div class="conston">
    <van-form @submit="onSubmit">
      <!-- 套餐名称 -->
      <van-cell-group inset class="form-section">
        <van-field v-model="page.title" input-align="right" placeholder="请输入"
          :rules="[{ required: true, message: '请输入套餐名称' }]">
          <template #label>
            <span class="required-label">套餐名称</span>
          </template>
        </van-field>
      </van-cell-group>

      <!-- 已选菜品区域 -->
      <div class="dish-header">
        <div class="selected-dishes">
          <div class="selected-text">
            已选菜品（{{ list.length ? list.length : 0 }}）
          </div>
        </div>
        <van-button type="primary" icon="plus" size="small" @click="selinfoClick" class="add-dish-btn">
          添加菜品
        </van-button>
      </div>
      <div class="dish-section">

        <!-- 菜品列表 -->
        <div class="dish-list" v-if="list.length > 0">
          <div class="dish-container">
            <div
              class="dish-item"
              v-for="(item, i) in list"
              :key="i"
              v-show="!is_ex1 || i < 3"
            >
              <div class="dish-image">
                <img :src="item.image" alt="" v-if="item.image" />
                <div v-else class="dish-placeholder">{{ item.title.slice(0, 1) }}</div>
              </div>
              <div class="dish-info">
                <div class="dish-name">{{ item.title }}</div>
                <div class="dish-details">
                  <span class="dish-quantity">x1</span>
                  <span class="dish-price">¥{{ item.price }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 展开/收起按钮 -->
          <div
            :class="['expand-toggle', { 'expanded': !is_ex1 }]"
            @click="ex_click()"
            v-if="list.length > 3"
          >
            <span class="toggle-content">
              <img
                src="../../../../../../../static/img/icon_arrow.png"
                class="toggle-icon"
                :class="{ 'rotated': !is_ex1 }"
              />
            </span>
          </div>
        </div>
      </div>

      <!-- 套餐价格 -->
      <van-cell-group inset class="form-section">
        <van-field v-model="page.price" name="套餐价格" placeholder="请输入价格" input-align="right" type="number"
          :rules="[{ required: true, message: '请填写套餐价格' }]">
          <template #label>
            <span class="required-label">套餐价格</span>
          </template>
          <template #right-icon>
            <span class="price-unit">元</span>
          </template>
        </van-field>
      </van-cell-group>

      <!-- 图片上传 -->
      <van-cell-group inset class="form-section">
        <van-field class="upload-field" input-align="right" label="图片">
          <template #input>
            <van-uploader :before-read="beforeRead" :after-read="afterRead" :max-size="onOverSize" @delete="onDelete"
              v-model="fileList" preview-size="80px" max-count="1">
            </van-uploader>
          </template>
        </van-field>
      </van-cell-group>

      <!-- 为固定底部按钮预留空间 -->
      <div class="bottom-padding"></div>
    </van-form>

    <!-- 固定底部提交按钮 -->
    <div class="fixed-submit-container">
      <van-button block type="primary" native-type="submit" class="submit-btn" @click="onSubmit">
        提交
      </van-button>
    </div>
  </div>
</template>
<script setup>
import {
  ref,
  onBeforeMount,
  onBeforeUnmount,
  getCurrentInstance,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import * as dd from "dingtalk-jsapi";
import { Popup, Search, showToast } from "vant";
import { useLoginStore } from "@/store/dingLogin";

const router = useRouter();
const applist = useLoginStore();
const route = useRoute();
onBeforeMount(() => {
  let array = [];
  page.value.price = 0;
  applist.dishFoodList.forEach((item, i) => {
    page.value.price += Number(item.price);
    array.push(item);
  });

  if (!page.value.title) {
    page.value.dish_title = page.value.title = [...applist.dishFoodList]
      .map((el) => el.title)
      .join("+");
    if (page.value.title.length > 100) {
      page.value.title = page.value.title.substring(0, 100);
    }
  }
  page.value.dishes = JSON.stringify(array);
  if (page.value.price) {
    page.value.price = (page.value.price * 1).toFixed(2);
  }
});
onBeforeUnmount(() => { });
const list = applist.dishFoodList;
const page = ref({
  title: "",
  dishes: [],
  price: null,
  image: "",
  dininghall_id: localStorage.getItem("dininghall"),
});
page.value.title = applist.title;
const selinfoClick = () => {
  applist.title = page.value.title;
  router.back();
};
const is_ex1 = ref(true);
const ex_click = () => {
  is_ex1.value = !is_ex1.value;
};
// 图片
const fileList = ref([]);
let beforeRead = ref();
let onOverSize = ref();
const afterRead = (fileobj) => {
  proxy
    .$upload(`${import.meta.env.VITE_APP_FILE_UPLOAD}file/post_upload`, {
      file: fileobj.file,
    })
    .then((res) => {
      let array = [];
      fileList.value.push({ url: res.result });
      array.push(res.result);
      page.value.image = JSON.stringify(array);
      console.log(fileList.value);
    });
};
// 删除菜品功能（如果需要的话）
const onDelete = (file) => {
  // 处理图片删除
  const index = fileList.value.findIndex(item => item === file);
  if (index !== -1) {
    fileList.value.splice(index, 1);
    if (fileList.value.length === 0) {
      page.value.image = "";
    } else {
      const imageUrls = fileList.value.map(item => item.url);
      page.value.image = JSON.stringify(imageUrls);
    }
  }
};

const onSubmit = () => {
  // 将菜品数据转换为标准格式，添加默认数量
  const processedDishes = JSON.parse(page.value.dishes).map(item => ({
    dish_id: item.dish_id,
    title: item.title,
    price: item.price,
    quantity: 1,
    image: item.image
  }));

  // 将处理后的数据重新赋值
  page.value.dishes = JSON.stringify(processedDishes);
  // console.log(page.value, 'qwqwqww');
  post("meal_set/post_add", page.value);
};
//远程
let { proxy } = getCurrentInstance();
const post = (url, page) => {
  proxy
    .$post(url, page)
    .then((res) => {
      if (url == "meal_set/post_add") {
        if (res.code != 200) {
          showToast(res.msg);
        } else if (res.code === 200) {
          console.log(res, "res");
          showToast({
            message: "操作成功",
            icon: "success",
          });
          applist.dishFoodList = null;
          applist.title = null;
          setTimeout(() => {
            router.go(-2);
            // router.push("/form_dishes_public");
          }, 1500);
        }
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
</script>
<style lang="scss" scoped>
.conston {
  width: 100%;
  background: #f5f5f5;
  min-height: 100vh;
  padding: 10px 0 0 0;
}

// 表单区域样式
.form-section {
  margin-top: 10px;

  :deep(.van-cell) {
    padding: 16px;
    font-size: 16px;

    .van-field__label {
      width: auto;
      margin-right: 12px;
    }
  }
}

// 必填字段标签样式
.required-label {
  position: relative;
  color: #323233;
  font-size: 16px;

  &::before {
    content: '*';
    color: #ee0a24;
    position: absolute;
    left: -8px;
    top: 0;
  }
}

// 普通字段标签样式
.field-label {
  color: #323233;
  font-size: 16px;
}

// 价格单位样式
.price-unit {
  color: #323233;
  font-size: 16px;
  margin-left: 4px;
}

// 已选菜品区域
.dish-section {
  margin-top: 10px;
  background: #fff;
  border-radius: 8px;
  margin-left: 16px;
  margin-right: 16px;
  overflow: hidden;
}

// 菜品头部样式
.dish-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f5f5f5;

  .selected-dishes {
    .selected-text {
      color: #323233;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .add-dish-btn {
    :deep(.van-button) {
      height: 32px;
      padding: 0 16px;
      font-size: 14px;
      border-radius: 6px;
    }
  }
}

// 菜品列表样式
.dish-list {

  .dish-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f5f5f5;
    transition: all 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    .dish-image {
      width: 56px;
      height: 56px;
      margin-right: 12px;
      border-radius: 8px;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .dish-placeholder {
      width: 56px;
      height: 56px;
      border-radius: 8px;
      background: #1678ff;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: 500;
    }

    .dish-info {
      flex: 1;

      .dish-name {
        font-size: 16px;
        color: #323233;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .dish-details {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .dish-quantity {
          font-size: 14px;
          color: #969799;
        }

        .dish-price {
          font-size: 16px;
          color: #ff6b35;
          font-weight: 500;
        }
      }
    }
  }
}

// 展开/收起按钮样式
.expand-toggle {
  text-align: center;
  padding: 12px 0;
  cursor: pointer;
  background: #fff;
  transition: all 0.3s ease;

  .toggle-content {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .toggle-icon {
    width: 7px;
    height: 11px;
    -webkit-transform: rotate(90deg);

    &.rotated {
      transform: rotate(180deg);
    }
  }

  &:active {
    .toggle-icon {
      opacity: 0.8;
      transform: scale(0.95);
    }
  }

  &.expanded {
    .toggle-icon.rotated {
    -webkit-transform: rotate(270deg);
    }
  }
}

// 图片上传样式
.upload-field {
  :deep(.van-field__body) {
    align-items: flex-start;
  }

  :deep(.van-field__label) {
    padding-top: 8px;
  }
}

.upload-container {
  width: 100%;

  :deep(.van-uploader) {
    .van-uploader__wrapper {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .van-uploader__upload {
      width: 80px;
      height: 80px;
      border: 1px dashed #dcdee0;
      border-radius: 8px;
      background: #fafafa;

      .van-uploader__upload-icon {
        font-size: 24px;
        color: #dcdee0;
      }
    }

    .van-uploader__preview {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      overflow: hidden;

      .van-uploader__preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

// 底部预留空间
.bottom-padding {
  height: 80px; // 为固定底部按钮预留空间
}

// 固定底部提交按钮样式
.fixed-submit-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;

  .submit-btn {
    :deep(.van-button) {
      height: 50px;
      font-size: 16px;
      font-weight: 500;
      border-radius: 8px;
      background: #1989fa;
      border: none;

      &:active {
        background: #1677d9;
      }
    }
  }
}

// 移除默认边框
:deep(.van-cell:after) {
  border: none;
}

// 响应式设计
@media (max-width: 375px) {
  .dish-item {
    .dish-image, .dish-placeholder {
      width: 48px;
      height: 48px;
    }

    .dish-info .dish-name {
      font-size: 15px;
    }
  }

  .form-section :deep(.van-cell) {
    padding: 14px;
  }
}
</style>
