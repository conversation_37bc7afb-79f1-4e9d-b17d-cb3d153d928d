<template>
  <div class="wraper-order-confirm">
    <div class="top-bg"></div>
    <!-- 添加背景样式的包装容器 -->
    <div class="address-info-wrapper">
      <div style="height: 12px;"></div>
      <div class="address-info top">
        <div class="address-title">
          <van-cell v-if="data.type === 0" :title="app.loginData.dininghall_title" :border="false" />
          <van-cell v-if="data.type === 1" @click="data.show = true" :title="data.addressList.length
            ? data.addressList[data.selectedAddressId].region +
            data.addressList[data.selectedAddressId].doorplate
            : '请选择地址'
            " is-link :border="false" />
          <div class="type">
            <div :class="`${data.type === 0 && 'active'}`" @click="data.type = 0">
              自提
            </div>
            <div :class="`${data.type === 1 && 'active'}`" @click="data.type = 1">
              外卖
            </div>
          </div>
        </div>
        <span style="font-size: 14px; color: rgba(23, 26, 29, 0.4); padding-left: 16px;"> {{ app.item.window_title
        }}</span>
        <div class="time-bottom address-title">
          <div class="title" style="width: 100px">
            就餐时间
          </div>
          <van-cell :title="data.date" :border="false" />
        </div>
      </div>
      <div class="address-info" style="margin-top: 16px">
        <yhc-booking-type :config="bookingConfig" :form="formData" @change="onBookingTypeChange" />
      </div>
    </div>
    <div class="dish-block">
      <div class="header">
        <div>菜品信息</div>
        <div style="color: #1989FA;font-size: 12px;font-weight: normal;">继续选菜</div>
      </div>
      <div class="title" style="padding-top: 18px; padding-bottom: 2px;">
        {{ app.loginData.dininghall_title }}-{{ app.item.window_title
        }}
      </div>
      <div class="list-block" v-if="displayedDishes">
        <div class="base-flex dish-item" v-for="(el, index) in displayedDishes" :key="index + el.title">
          <div class="dish-img" v-if="el.image">
            <img :src="el.image" alt="" />
          </div>
          <div class="dish-img_wu" v-else>
            <div>{{ el.title ? el.title.slice(0, 1) : "无" }}</div>
          </div>
          <div class="dish-text">
            <div class="dish-name">
              {{ el.title }}
              <div class="base-flex dish-price" v-if="el.price">
                ¥12312
              </div>
            </div>
            <div class="dish-count">
              <span>x1</span><span v-if="el.price && el.quantity > 1">￥{{ el.price }}</span>
            </div>
            <div class="dish-count">
              <div>
                <span style="font-size: 13px">22g</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 展开/收起按钮 -->
        <div :class="['expand-toggle', { 'expanded': !isDishesCollapsed }]" @click="toggleDishesExpand()"
          v-if="app.item.dishess && app.item.dishess.length > 3">
          <span class="toggle-content">
            <span class="toggle-text">{{ isDishesCollapsed ? `展开菜品共${app.item.dishess.length}条数据` : '收起部分菜品' }}</span>
            <img src="../../../../../static/img/icon_arrow.png" class="toggle-icon"
              :class="{ 'rotated': !isDishesCollapsed }" />
          </span>
        </div>
      </div>
      <!-- 实付款 - 放在最前面 -->
      <div class="base-flex sub-row discount-summary">
        <div class="discount-left">
          <text style="font-size: 16px;">实付款</text>
        </div>
        <text space="ensp" style="font-size: 15px;">¥1212</text>
      </div>
      <!-- 总优惠 - 可点击展开 -->
      <div class="base-flex sub-row discount-summary" @click="toggleDiscountDetail">
        <div class="discount-left">
          <text>总优惠</text>
          <img src="../../../../../static/img/icon_arrow.png" class="discount-arrow"
            :class="{ 'rotated': isDiscountExpanded }" />
        </div>
        <text space="ensp" style="color: #EE0A24;font-size: 15px;">-¥12321</text>
      </div>

      <!-- 优惠明细 - 展开时显示 -->
      <div class="discount-details" v-if="isDiscountExpanded">
        <div class="base-flex sub-row detail-item">
          <text>总价</text>
          <text space="ensp" style="font-size: 12px;color: #323233;">¥12312</text>
        </div>
        <div class="base-flex sub-row detail-item">
          <text>减免金额</text>
          <text space="ensp" style="font-size: 12px;color: #EE0A24;">-¥12312</text>
        </div>
        <div class="base-flex sub-row detail-item">
          <text>优惠券折扣</text>
          <text space="ensp" style="font-size: 12px;color: #EE0A24;">-¥123</text>
        </div>
      </div>
    </div>
    <div class="address-info" style="margin-top: 16px">
      <van-field v-model="data.desc" rows="2" autosize label="备注" type="textarea" maxlength="50" placeholder="口味、偏好等"
        show-word-limit :disabled="!!data.orderId" />
    </div>
    <div class="goods-bar" v-if="!data.orderId">
      <div class="total">
        <span style="margin-left: 16px; font-size: 14px">总价</span>
        <span style="font-size: 17px">￥{{ app.item.price * (count || 1) }}</span>
      </div>
      <van-button round type="primary" @click="onSettlementClick">去结算</van-button>
    </div>
    <van-popup v-model:show="data.show" label="选择收货地址" position="bottom" closeable :style="{ height: '45%' }" close-icon-position="top-left">
      <div style="text-align: center; padding: 14px;font-size: 18px;">
        <span class="title-text">选择收货地址</span>
      </div>
      <div class="content">
        <van-radio-group v-model="data.selectedAddressId" class="custom-radio">
          <div v-for="(item, i) in data.addressList" :key="item.id" style="
              padding-bottom: 12px;
              border-bottom: 1px solid rgba(0, 0, 0, 0.08);
              display: flex;
              align-items: center;
              margin-bottom: 12px;
            ">
            <van-radio :name="i" />
            <div style="flex: 1; margin-left: 8px">
              <div>{{ item.region + item.doorplate }}</div>
              <div style="font-size: 12px; color: #888; margin-top: 4px">
                {{ item.name }} {{ item.mobile }}
              </div>
            </div>
            <van-icon name="edit" size="24" @click.stop="onEditAddress(item)" />
          </div>
        </van-radio-group>
      </div>
      <van-empty description="暂无地址" v-if="data.addressList.length === 0"/>

      <div class="button-pos">
        <van-button type="primary" block icon="add-o"  style="margin-top: 16px; border: none; color:#0180ff;background-color: #f2f2f3;border-radius: 14px;height: 48px;font-size: 16px;" @click="onAddAddress">
          新增收货地址
        </van-button>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import {
  onUnmounted,
  onActivated,
  ref,
  reactive,
  getCurrentInstance,
  computed,
} from "vue";
import { showToast, closeToast, showLoadingToast } from "vant";
import { useRoute, useRouter } from "vue-router";
import dayjs from "dayjs";

import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
let data = reactive({
  orderId: "",
  desc: "",
  type: 0,
  isShowAll: false,
  date: dayjs().add(20, "minute").format("MM月DD日(今日)HH:mm"),
  selectList: new Set(app.item.dishess),
  users: "",
  show: false,

  addressList: [],
  selectedAddressId: 0,
});
// app.item.dishess = [{ desc: "", "image": "", "price": "10.23", "title": "晚1", "category_id": 1, "category_title": "晚1" },
// { desc: "", "image": "", "price": "10.23", "title": "晚1", "category_id": 1, "category_title": "晚12" },
// { desc: "", "image": "", "price": "10.23", "title": "晚1", "category_id": 1, "category_title": "晚112" },
// { desc: "", "image": "", "price": "10.23", "title": "晚1", "category_id": 1, "category_title": "晚21" },
// { desc: "", "image": "", "price": "10.23", "title": "晚1", "category_id": 1, "category_title": "晚121" }]// 切换优惠明细展开/收起状态
const toggleDiscountDetail = () => {
  isDiscountExpanded.value = !isDiscountExpanded.value;
};
let mealType = ref(0);
let reportType = ref("");
let reportTypeVal = ref("0");
let count = ref();
let showReportType = ref(false);
const reportList = [];
let item = app.item;
if (item.multi) {
  reportList.push({ text: "多人报餐", value: "1" });
}
if (item.dept) {
  reportList.push({ text: "部门报餐", value: "dept" });
}
if (reportList[0]) {
  reportType.value = reportList[0].text;
  reportTypeVal.value = reportList[0].value;
}
let inputRef = ref();
let count1 = 1;
// 菜品展开/收起状态控制
const isDishesCollapsed = ref(true); // true表示收起状态，只显示前3个菜品

// 优惠明细展开/收起状态控制
const isDiscountExpanded = ref(false);

// 计算属性：控制显示的菜品数量
const displayedDishes = computed(() => {
  if (!app.item.dishess || app.item.dishess.length <= 3) {
    return app.item.dishess || [];
  }
  if (isDishesCollapsed.value) {
    return app.item.dishess.slice(0, 3);
  }
  return app.item.dishess;
});
// 切换菜品展开/收起状态
const toggleDishesExpand = () => {
  isDishesCollapsed.value = !isDishesCollapsed.value;
};
const focus = (key) => {
  console.log([key].value);
  let input = [key].value.$el.querySelector("input");
  if (input && count1) {
    input.focus();
    let length = input.value.length;
    // input.setSelectionRange(length, length);
    input.selectionStart = length;
    input.selectionEnd = length;
    --count1;
  }
};
onActivated(() => {
  if (app.deptList && app.deptList.length) {
    data.users = app.deptList.map((el) => el.name).join(",");
  } else {
    data.users = "";
  }
});
onUnmounted(() => {
  delete app.deptList;
  delete app.dept;
});
const onSelectDept = () => {
  router.push({
    path: "/selectDept",
  });
};
const onMealTypeClick = (i) => {
  mealType.value = i;
};
const onConfirm = (e) => {
  reportType.value = e.selectedOptions[0].text;
  reportTypeVal.value = e.selectedOptions[0].value;
  showReportType.value = false;
};

const onSettlementClick = () => {
  let postData = {
    repast_id: item.repast_id,
    window_id: item.window_id,
    repast_title: item.repast_title,
    window_title: item.window_title,
    date: item.date,
    desc: data.desc,
    dine_type: mealType.value,
  };

  if (reportTypeVal.value == "1") {
    if (count.value === undefined) {
      showToast("请输入报餐份数");
      return;
    }
    postData.count = count.value;
  }
  if (reportTypeVal.value == "dept") {
    if (!app.dept || (app.deptList && !app.deptList.length)) {
      showToast("请选择部门人员");
      return;
    }
    postData.user_data = app.deptList.map((user) => ({
      userid: user.userid,
      name: user.name,
    }));
    postData.dept_id = app.dept.dept_id;
    postData.dept_name = app.dept.name;
  }
  showLoadingToast({
    message: "报餐中...",
    forbidClick: true,
  });
  proxy
    .$post("apply/post_apply_add", postData)
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        data.orderId = "ok";
        showToast("报餐成功~");
        if (res.bill_id) {
          setTimeout(() => {
            closeToast();
            router.back();
          }, 1500);
        } else {
          closeToast();
          router.push({
            path: "/reportResult",
            query: {
              res: JSON.stringify(res)
            },
          });
        }
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
      setTimeout(() => {
        closeToast();
      }, 3000);
    });
};
const formData = reactive({
  booking_type: "个人预定",
  quantity: null,
  selected_user: null,
});

const bookingConfig = {
  label: "预定方式",
  key: "booking_type",
  quantityKey: "quantity",
  userKey: "selected_user",
};

const onBookingTypeChange = (data) => {
  console.log('预定方式变化:', data);
};
function onAddAddress() {
  // 跳转到新增地址页面
  router.push({ path: "/editAddress" });
}
// 获取全部地址
const getAddress = () => {
  proxy
    .$get("https://canyinapitest.eykj.cn/address/get_all")
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        data.addressList = res;
        data.addressList.findIndex((el, i) => {
          if (el.default == 1) {
            data.selectedAddressId = i;
            return true;
          }
        });
        console.log(res);
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(JSON.stringify(err));
      router.back();
    });
};
getAddress();
</script>

<style lang="scss">
.wraper-order-confirm {
  padding-bottom: 120px;

  // 背景样式包装容器
  .address-info-wrapper {
    background: linear-gradient(180deg, rgba(0, 127, 255, 1) 0%, rgba(0, 119, 255, 0.59) 69%, rgba(22, 120, 255, 0) 100%);
    height: 191px;
    margin: 0;
    padding: 0;

    // 重置内部address-info的margin，避免重复间距
    .address-info {
      margin: 0 16px;

      // &.top {
      //   margin-top: 0;
      //   padding-top: 16px;
      // }

      // &:not(.top) {
      //   margin-top: 16px;
      // }
    }
  }

  .dish-block {
    background-color: #ffffff;
    margin: 16px;
    border-radius: 8px;
    padding: 0 16px;
    border-bottom: 1px solid #f6f6f6;

    .dish-item {
      margin: 16px 0;

      /* 菜品项之间的分隔线样式 - 只在多个菜品时显示 */
      &:not(:last-child) {
        border-bottom: 1px solid rgba(126, 134, 142, 0.16);
        padding-bottom: 16px;
      }

      /* 单个菜品时不显示分隔线 */
      &:only-child {
        border-bottom: none;
        padding-bottom: 0;
      }

      .dish-img {
        margin-right: 8px;

        img {
          width: 57px;
          height: 57px;
          border-radius: 8px;
        }
      }

      .dish-img_wu {
        margin-right: 8px;

        div {
          width: 57px;
          height: 57px;
          font-size: 30px;
          border-radius: 8px;
          line-height: 58px;
          text-align: center;
          color: #ffffff;
          background: #1678ff;
        }
      }
    }

    // 展开/收起按钮样式
    .expand-toggle {
      text-align: center;
      cursor: pointer;
      background: #fff;
      transition: all 0.3s ease;

      .toggle-content {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: rgba(23, 26, 29, 0.6);
        font-size: 13px;

        .toggle-text {
          margin-right: 6px;
        }

        .toggle-icon {
          width: 7px;
          height: 11px;
          transform: rotate(90deg);
          transition: transform 0.3s ease;

          &.rotated {
            transform: rotate(270deg);
          }
        }
      }

      &:active {
        .toggle-content {
          opacity: 0.8;
        }
      }
    }

    .dish-text {
      flex: 1;
      display: flex;
      flex-direction: column;
      line-height: 18px;
      justify-content: flex-start;

      .dish-name {
        color: rgba(23, 26, 29, 100);
        font-size: 13px;
        display: flex;
        justify-content: space-between;
      }

      .dish-count {
        color: rgba(23, 26, 29, 0.6);
        font-size: 12px;
      }
    }

    .dish-price {
      align-items: center;
      font-size: 14px;
    }

    .sub-row {
      color: rgba(23, 26, 29, 100);
      font-size: 12px;
      margin: 13px 0;
    }



    /* 优惠明细相关样式 */
    .discount-summary {
      cursor: pointer;
      border-radius: 6px;

      .discount-left {
        display: flex;
        align-items: center;

        .discount-arrow {
          width: 7px;
          height: 11px;
          margin-left: 6px;
          transform: rotate(90deg);
          transition: transform 0.3s ease;

          &.rotated {
            transform: rotate(270deg);
          }
        }
      }
    }

    .discount-details {
      background-color: #f7f8fa;
      border-radius: 6px;
      padding: 8px 12px;
      margin: 10px 0;

      .detail-item {
        margin: 8px 0;
        font-size: 12px;
        color: rgba(23, 26, 29, 0.8);

        &:first-child {
          margin-top: 4px;
        }

        &:last-child {
          margin-bottom: 4px;
        }
      }
    }
  }



  .header {
    display: flex;
    justify-content: space-between;
    padding-top: 16px;
    padding-bottom: 21px;
    font-size: 16px;
    font-weight: 500;
    border-bottom: 1px solid rgba(126, 134, 142, 0.16);
  }

  .dish-block .title {
    color: rgba(25, 31, 37, 100);
    font-size: 16px;
    padding: 12px 0;
  }



  .base-flex {
    display: flex;
    justify-content: space-between;
  }

  .address-info {
    border-radius: 8px;
    background: #ffffff;
    overflow: hidden;

    // 默认样式，当不在包装容器内时使用
    &:not(.address-info-wrapper .address-info) {
      margin: 0 16px;
      margin-top: 16px;
    }

    .address-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .van-cell__title {
        font-size: 15px;
      }

      .type {
        width: 150px;
        height: 28px;
        font-size: 14px;
        background: rgba(0, 0, 0, 0.16);
        display: flex;
        justify-content: space-between;
        padding: 1px;
        margin-right: 16px;
        border-radius: 4px;

        div {
          padding: 5px 8px;
          border-radius: 4px;
        }
      }

      .active {
        background: #fff;
      }
    }

    .time-bottom {
      .title {
        font-size: 14px;
        margin-left: 16px;
      }

      .van-cell__title {
        text-align: right;
        font-size: 14px;
        color: #007fff !important;
      }
    }

  }



}

.goods-bar {
  padding: 0 12px;
  position: fixed;
  bottom: 16px;
  left: 0;
  right: 0;
  height: 58px;
  border-radius: 29px;
  opacity: 1;
  background: #ffffff;
  box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 16px;

  .total {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.button-pos {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 10px 16px 16px 16px;
  // box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
}
</style>
