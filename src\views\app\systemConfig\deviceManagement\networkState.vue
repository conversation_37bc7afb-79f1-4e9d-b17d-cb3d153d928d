<template>
    <div class="device-detail-container">
        <van-cell-group inset class="device-name-card">
            <van-field v-model="value" label="IP地址"  input-align="right" style="height: 56px;align-items: center;" :readonly="true" />
            <van-field v-model="value" label="MAC地址"  input-align="right" style="height: 56px;align-items: center;" :readonly="true" />
        </van-cell-group>
    </div>
</template>
<script setup>
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
let route = useRoute();
onMounted(() => {
    editForm.value.IP = route.query.IP;
    editForm.value.MAC = route.query.MAC;
});
let editForm = ref({
    IP: "",
    MAC:""
})
</script>
<style lang="scss" scoped>
.device-detail-container {
    min-height: 90vh;
    background: #f7f8fa;
}
.device-name-card{
    margin-top: 16px;
}

</style>