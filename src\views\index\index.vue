<template>
  <div class="wrapper">
    <div class="top-block">
      <van-dropdown-menu>
        <van-dropdown-item
          v-model="curDininghall"
          :options="option"
          @change="dingdingChange"
        />
      </van-dropdown-menu>
      <van-icon name="qr" class="van-haptics-feedback" size="24" />
      <van-icon name="setting-o" class="van-haptics-feedback" size="24" />
    </div>
  </div>
</template>
<script setup>
import { ref, onBeforeMount, getCurrentInstance } from "vue";
import {showNotify,showToast,closeNotify} from "vant"
const router = useRouter();

let { proxy } = getCurrentInstance();

const curDininghall = ref(0);
let option = [];
let config = {
  curl: {
    ls: "dininghall/get_all",
  },
  postData: {},
};
const dingdingChange = (e) => {
  console.log(e);
};

const getDininghall = () => {
  proxy
    .$post(config.curl.ls, config.postData)
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        res.forEach((element) => {
          element.text = element.title;
          element.value = element.id;
        });
        option = res;
        if (res[0]) {
          curDininghall.value = res[0].id;
        }
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    })
    .finally(() => {});
};
// getDininghall();
</script>
<style lang="scss" scoped>
.wrapper {
  width: 100%;
  min-height: 100vh;
  .top-block {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding-left: 8px;
    .van-dropdown-menu {
      flex: 1;
      .van-dropdown-menu__bar {
        box-shadow: none !important;
        .van-dropdown-menu__item {
          justify-content: flex-start;
        }
      }
    }
    .van-icon {
      width: 50px;
      text-align: center;
    }
  }
}
</style>
