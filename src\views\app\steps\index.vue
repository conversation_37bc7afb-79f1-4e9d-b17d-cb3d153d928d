<template>
  <div class="demo-container">
    <van-nav-bar title="分段器组件演示" left-arrow @click-left="$router.go(-1)" />
    
    <div class="demo-section">
      <h3>基础用法</h3>
      <yhc-segmented-control :config="basicConfig" :form="form1" />
      <p class="result">当前选择: {{ form1.basic }}</p>
    </div>
    
    <div class="demo-section">
      <h3>4个选项(受控)</h3>
      <yhc-segmented-control :config="fourOptionsConfig" :form="form2" />
      <p class="result">当前选择: {{ form2.fourOptions }}</p>
    </div>
    
    <div class="demo-section">
      <h3>默认选中第二个</h3>
      <yhc-segmented-control :config="defaultSecondConfig" :form="form3" />
      <p class="result">当前选择: {{ form3.defaultSecond }}</p>
    </div>
    
    <div class="demo-section">
      <h3>不可用</h3>
      <yhc-segmented-control :config="disabledConfig" :form="form4" />
      <p class="result">当前选择: {{ form4.disabled }}</p>
    </div>
    
    <div class="demo-section">
      <h3>带图标</h3>
      <yhc-segmented-control :config="iconConfig" :form="form5" />
      <p class="result">当前选择: {{ form5.withIcon }}</p>
    </div>
    
    <div class="demo-section">
      <h3>多选模式</h3>
      <yhc-segmented-control :config="multipleConfig" :form="form6" />
      <p class="result">当前选择: {{ JSON.stringify(form6.multiple) }}</p>
    </div>
    
    <div class="demo-section">
      <h3>大尺寸 + 圆角</h3>
      <yhc-segmented-control :config="largeRoundConfig" :form="form7" />
      <p class="result">当前选择: {{ form7.largeRound }}</p>
    </div>
    
    <div class="demo-section">
      <h3>不同类型</h3>
      <div class="type-demo">
        <div class="type-item">
          <p>Primary</p>
          <yhc-segmented-control :config="primaryConfig" :form="form8" />
        </div>
        <div class="type-item">
          <p>Success</p>
          <yhc-segmented-control :config="successConfig" :form="form9" />
        </div>
        <div class="type-item">
          <p>Warning</p>
          <yhc-segmented-control :config="warningConfig" :form="form10" />
        </div>
        <div class="type-item">
          <p>Danger</p>
          <yhc-segmented-control :config="dangerConfig" :form="form11" />
        </div>
      </div>
    </div>
    
    <div class="demo-section">
      <h3>带徽标</h3>
      <yhc-segmented-control :config="badgeConfig" :form="form12" />
      <p class="result">当前选择: {{ form12.withBadge }}</p>
    </div>
    
    <div class="demo-section">
      <h3>部分禁用</h3>
      <yhc-segmented-control :config="partialDisabledConfig" :form="form13" />
      <p class="result">当前选择: {{ form13.partialDisabled }}</p>
    </div>
  </div>
</template>

<script setup>
import { reactive } from "vue";

// 表单数据
const form1 = reactive({ basic: "选项1" });
const form2 = reactive({ fourOptions: "选项1" });
const form3 = reactive({ defaultSecond: "选项2" });
const form4 = reactive({ disabled: "选项1" });
const form5 = reactive({ withIcon: "home" });
const form6 = reactive({ multiple: ["选项1", "选项3"] });
const form7 = reactive({ largeRound: "选项1" });
const form8 = reactive({ primary: "选项1" });
const form9 = reactive({ success: "选项1" });
const form10 = reactive({ warning: "选项1" });
const form11 = reactive({ danger: "选项1" });
const form12 = reactive({ withBadge: "option1" });
const form13 = reactive({ partialDisabled: "option1" });

// 配置对象
const basicConfig = {
  key: "basic",
  options: ["选项1", "选项2", "选项3"]
};

const fourOptionsConfig = {
  key: "fourOptions",
  options: ["选项1", "选项2", "选项3", "选项4"]
};

const defaultSecondConfig = {
  key: "defaultSecond",
  options: ["选项1", "选项2", "选项3", "选项4"]
};

const disabledConfig = {
  key: "disabled",
  disabled: true,
  options: ["选项1", "选项2"]
};

const iconConfig = {
  key: "withIcon",
  options: [
    { text: "首页", value: "home", icon: "home-o" },
    { text: "订单", value: "bill", icon: "records-o" },
    { text: "工作台", value: "app", icon: "setting-o" },
    { text: "我的", value: "user", icon: "user-o" }
  ]
};

const multipleConfig = {
  key: "multiple",
  multiple: true,
  options: ["选项1", "选项2", "选项3", "选项4"]
};

const largeRoundConfig = {
  key: "largeRound",
  size: "large",
  round: true,
  options: ["选项1", "选项2", "选项3"]
};

const primaryConfig = {
  key: "primary",
  type: "primary",
  options: ["选项1", "选项2", "选项3"]
};

const successConfig = {
  key: "success",
  type: "success",
  options: ["选项1", "选项2", "选项3"]
};

const warningConfig = {
  key: "warning",
  type: "warning",
  options: ["选项1", "选项2", "选项3"]
};

const dangerConfig = {
  key: "danger",
  type: "danger",
  options: ["选项1", "选项2", "选项3"]
};

const badgeConfig = {
  key: "withBadge",
  options: [
    { text: "选项1", value: "option1", badge: "新" },
    { text: "选项2", value: "option2", badge: 5 },
    { text: "选项3", value: "option3" },
    { text: "选项4", value: "option4", badge: "热" }
  ]
};

const partialDisabledConfig = {
  key: "partialDisabled",
  options: [
    { text: "选项1", value: "option1" },
    { text: "选项2", value: "option2", disabled: true },
    { text: "选项3", value: "option3" },
    { text: "选项4", value: "option4", disabled: true }
  ]
};
</script>

<style lang="scss" scoped>
.demo-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.demo-section {
  background: white;
  margin: 8px;
  padding: 16px;
  border-radius: 8px;
  
  h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #323233;
    font-weight: 500;
  }
  
  .result {
    margin: 12px 0 0 0;
    padding: 8px 12px;
    background: #f7f8fa;
    border-radius: 4px;
    font-size: 14px;
    color: #646566;
    border-left: 3px solid #1989fa;
  }
}

.type-demo {
  .type-item {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    p {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: #646566;
    }
  }
}
</style>
