# 🎯 项目组件使用指南

> 本文档汇总了项目中所有自定义组件的使用方法，支持快速定位和查找。

## 🚀 快速索引表

| 组件名 | 功能描述 | 使用场景 | 跳转链接 |
|--------|----------|----------|----------|
| yhc-form | 动态表单组件 | 表单页面 | [查看详情](#yhc-form) |
| yhc-list | 高级列表组件 | 列表页面 | [查看详情](#yhc-list) |
| yhc-table | 移动端表格 | 数据展示 | [查看详情](#yhc-table) |
| yhc-picker | 下拉选择器 | 选择输入 | [查看详情](#yhc-picker) |
| yhc-picker-date | 日期选择器 | 时间输入 | [查看详情](#yhc-picker-date) |
| yhc-editor | 富文本编辑器 | 内容编辑 | [查看详情](#yhc-editor) |
| yhc-breadcrumb | 面包屑导航 | 页面导航 | [查看详情](#yhc-breadcrumb) |
| yhc-checkbox-group | 复选框组 | 多选输入 | [查看详情](#yhc-checkbox-group) |
| yhc-radio-group | 单选框组 | 单选输入 | [查看详情](#yhc-radio-group) |
| yhc-switch | 开关组件 | 状态切换 | [查看详情](#yhc-switch) |
| yhc-stepper | 步进器 | 数量输入 | [查看详情](#yhc-stepper) |
| yhc-select-user | 用户选择器 | 用户选择 | [查看详情](#yhc-select-user) |
| yhc-select-department | 部门选择器 | 部门选择 | [查看详情](#yhc-select-department) |
| yhc-select-image | 图片选择器 | 图片上传 | [查看详情](#yhc-select-image) |
| yhc-input | 增强输入框 | 文本输入 | [查看详情](#yhc-input) |
| yhc-desc | 描述列表 | 信息展示 | [查看详情](#yhc-desc) |
| yhc-tabbar | 底部导航 | 页面导航 | [查看详情](#yhc-tabbar) |
| yhc-calendar | 日历组件 | 日期展示 | [查看详情](#yhc-calendar) |
| yhc-supply-list | 供应商列表 | 业务功能 | [查看详情](#yhc-supply-list) |
| yhc-segmented-control | 分段控制器 | 选项切换 | [查看详情](#yhc-segmented-control) |
| yhc-tag-selector | 标签选择器 | 标签管理 | [查看详情](#yhc-tag-selector) |
| yhc-action-buttons | 操作按钮组 | 批量操作 | [查看详情](#yhc-action-buttons) |
| yhc-grid | 网格布局 | 内容展示 | [查看详情](#yhc-grid) |
| yhc-notice | 通知公告 | 消息展示 | [查看详情](#yhc-notice) |

| yhc-form-item | 表单项包装器 | 表单渲染 | [查看详情](#yhc-form-item) |
| yhc-form-cust-field | 自定义表单字段 | 动态表单 | [查看详情](#yhc-form-cust-field) |

## 📋 目录导航

- [🔧 核心组件](#核心组件)
  - [yhc-form - 表单组件](#yhc-form)
  - [yhc-list - 列表组件](#yhc-list)
  - [yhc-table - 表格组件](#yhc-table)
- [📝 表单控件](#表单控件)
  - [yhc-picker - 选择器](#yhc-picker)
  - [yhc-picker-date - 日期选择器](#yhc-picker-date)
  - [yhc-tag-selector - 标签选择器](#yhc-tag-selector)
  - [yhc-editor - 富文本编辑器](#yhc-editor)
  - [yhc-checkbox-group - 复选框组](#yhc-checkbox-group)
  - [yhc-radio-group - 单选框组](#yhc-radio-group)
- [🎨 UI组件](#ui组件)
  - [yhc-breadcrumb - 面包屑导航](#yhc-breadcrumb)
  - [yhc-calendar - 日历组件](#yhc-calendar)
  - [yhc-tabbar - 底部导航](#yhc-tabbar)
- [🔧 工具组件](#工具组件)
  - [yhc-supply-list - 供应商列表](#yhc-supply-list)

---

## 🔧 核心组件

### yhc-form

**功能**: 动态表单组件，支持多种表单控件和验证

#### 基础用法
```vue
<template>
  <yhc-form :config="formConfig" @onSubmit="onSubmit" />
</template>

<script setup>
const formConfig = {
  curl: {
    add: "/api/add",
    edit: "/api/edit", 
    info: "/api/info"
  },
  form: [
    {
      label: "姓名",
      key: "name",
      component: "van-field",
      required: true,
      rules: [{ required: true, message: "请输入姓名" }]
    }
  ],
  button: {
    isShow: true,
    text: "提交"
  }
}
</script>
```

#### 配置选项
```javascript
{
  // API接口配置
  curl: {
    add: "",             // 新增数据接口地址 (字符串) - 例: "/api/add" - 表单提交时调用
    edit: "",            // 编辑数据接口地址 (字符串) - 例: "/api/edit" - 修改数据时调用
    info: "",            // 获取详情接口地址 (字符串) - 例: "/api/info" - 编辑模式时获取数据
    del: "",             // 删除数据接口地址 (字符串) - 例: "/api/delete" - 删除操作时调用
  },
  postData: {},          // 请求参数 (对象) - 发送给接口的额外参数，如: {id: 1, category_id: 2}

  // 表单字段配置
  form: [],              // 表单字段数组 (数组) - 表单项配置，每个元素为一个字段配置对象
  groupForm: [],         // 表单分组配置 (数组) - 控制表单字段的分组显示，如: [[0,2], [2,4]]

  // 按钮配置
  button: {
    isShow: true,        // 是否显示按钮 (布尔值) - true: 显示提交按钮, false: 隐藏按钮
    text: "提交",        // 按钮文字 (字符串) - 按钮显示的文本，如"提交"、"保存"、"确认"
    type: "primary",     // 按钮类型 (字符串) - "primary": 主要按钮, "default": 默认按钮, "danger": 危险按钮
    position: "bottom",  // 按钮位置 (字符串) - "bottom": 底部固定, "normal": 正常位置
    plain: false,        // 是否朴素按钮 (布尔值) - true: 朴素样式, false: 填充样式
  },

  // 页面类型配置
  pageType: "add",       // 页面类型 (字符串) - "add": 新增页面, "edit": 编辑页面, "detail": 详情页面

  // 缓存配置
  cache: {
    enabled: false,      // 是否启用缓存 (布尔值) - true: 启用表单数据缓存, false: 不缓存
    key: "",             // 缓存键名 (字符串) - 自定义缓存键名，为空时自动生成
    duration: 30,        // 缓存时长 (数字) - 缓存保存时间，单位分钟
  }
}
```

#### Props
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| config | 表单配置 | Object | - |
| pageType | 页面类型 | String | 'add' |
| editRedirectConfig | 编辑跳转配置 | Object | {} |

---

### yhc-list

**功能**: 高级列表组件，支持搜索、筛选、分页、骨架屏

#### 基础用法
```vue
<template>
  <yhc-list :config="listConfig">
    <template #default="{ item, index }">
      <div class="list-item">
        <div class="title">{{ item.title }}</div>
        <div class="desc">{{ item.description }}</div>
      </div>
    </template>
  </yhc-list>
</template>

<script setup>
const listConfig = {
  curl: {
    ls: 'api/list'
  },
  search: {
    isShow: true
  },
  skeleton: {
    isShow: true,
    count: 5
  }
}
</script>
```

#### 配置选项
```javascript
{
  // API接口配置
  curl: {
    ls: "",              // 列表数据获取接口地址 (必填) - 例: "/api/list"
    sort: "",            // 排序接口地址 (可选) - 例: "/api/sort" - 当sort为true时必填
  },
  details: '',           // 详情页路由地址 (可选) - 例: "/detail" - 点击列表项时跳转
  title: '',             // 列表标题 (可选) - 用于特殊样式判断，如"菜品配置"、"表单设计"
  sort: false,           // 是否启用拖拽排序功能 (布尔值) - true: 启用拖拽排序, false: 普通列表
  postData: {},          // 请求参数 (对象) - 发送给接口的额外参数，如: {category_id: 1, status: 1}

  // 搜索配置
  search: {
    isShow: true,        // 是否显示搜索框 (布尔值) - true: 显示, false: 隐藏
    isShowPopup: true,   // 是否显示筛选按钮 (布尔值) - true: 显示"筛选"按钮, false: 仅搜索
    key: 'title',        // 搜索字段名 (字符串) - 搜索时使用的字段名，如: 'title', 'name', 'keyword'
    placeholder: '请输入搜索关键词', // 搜索框占位符 (字符串) - 搜索框提示文字
  },

  // 筛选弹窗配置
  popup: {
    round: false,        // 是否圆角弹窗 (布尔值) - true: 圆角, false: 直角
    position: "right",   // 弹窗位置 (字符串) - "right": 右侧, "bottom": 底部, "center": 居中
    style: { width: "90vw", height: "100%", overflow: "hidden" }, // 弹窗样式 (对象) - CSS样式对象
    closeable: false,    // 是否显示关闭按钮 (布尔值) - true: 显示X按钮, false: 不显示
  },

  // 筛选表单配置
  filter: {
    curl: {
      add: "",           // 筛选提交接口地址 (字符串) - 例: "/api/filter" - 筛选表单提交时调用
    },
    postData: {},        // 筛选表单额外参数 (对象) - 提交筛选时的额外参数
    form: [],            // 筛选表单字段配置 (数组) - yhc-form组件的表单字段配置
    button: {
      text: "提交",      // 筛选提交按钮文字 (字符串) - 默认"提交"
    },
  },

  // 标签页配置
  tabs: {
    isShow: true,        // 是否显示标签页 (布尔值) - true: 显示标签切换, false: 不显示
    sticky: true,        // 是否粘性定位 (布尔值) - true: 滚动时标签栏固定在顶部, false: 跟随滚动
    swipeable: false,    // 是否支持手势滑动切换 (布尔值) - true: 支持左右滑动切换, false: 仅点击切换
    list: [              // 标签列表配置 (数组) - 标签页数据
      // {
      //   text: "全部",   // 标签显示文字 (字符串) - 标签页显示的文本
      //   key: "all",     // 标签唯一标识 (字符串/数字) - 用于区分不同标签页
      // },
    ],
  },

  // 底部按钮配置
  button: {
    isShow: true,        // 是否显示底部按钮 (布尔值) - true: 显示, false: 隐藏
    text: "新增",        // 按钮文字 (字符串) - 按钮显示的文本，如"新增"、"提交"、"保存"
  },

  // 骨架屏配置 - 数据加载时的占位效果
  skeleton: {
    isShow: false,       // 是否启用骨架屏 (布尔值) - true: 启用加载占位效果, false: 不显示
    count: 5,            // 骨架屏数量 (数字) - 显示多少个骨架屏占位项，建议3-8个
    row: 3,              // 每个骨架屏的行数 (数字) - 每个占位项包含的行数，建议2-4行
    rowWidth: ['100%', '60%', '80%'], // 每行的宽度 (数组) - 每行占位条的宽度，模拟真实内容长度
    avatar: true,        // 是否显示头像占位 (布尔值) - true: 显示圆形头像占位, false: 不显示
    avatarSize: '40px',  // 头像大小 (字符串) - 头像占位的尺寸，如'40px', '50px'
    avatarShape: 'round', // 头像形状 (字符串) - 'round': 圆形, 'square': 方形
    title: true,         // 是否显示标题占位 (布尔值) - true: 显示标题占位条, false: 不显示
    titleWidth: '50%',   // 标题宽度 (字符串) - 标题占位条的宽度，如'50%', '80px'
    duration: 2000,      // 骨架屏显示时长 (数字) - 毫秒，超过此时间自动隐藏，0表示不自动隐藏
  },
}
```

#### Events
| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| onButClick | 按钮点击 | event, activeTab |
| onClickTab | 标签页点击 | tabData |

---

### yhc-table

**功能**: 移动端表格组件，支持横屏、分页、自定义列

#### 基础用法
```vue
<template>
  <yhc-table :config="tableConfig" @rowClick="onRowClick">
    <template #name="{ item, value }">
      <span style="color: #1989fa;">{{ value }}</span>
    </template>
  </yhc-table>
</template>

<script setup>
const tableConfig = {
  columns: [
    { key: 'name', title: '名称', width: '120px', slot: 'name' },
    { key: 'type', title: '类型', width: '100px' },
    { key: 'amount', title: '金额', width: '80px', align: 'right' }
  ],
  curl: {
    ls: 'api/table-data'
  },
  landscape: {
    enabled: true,
    autoRotate: true
  }
}
</script>
```

#### 完整配置选项
```javascript
{
  // 表格列配置 - 定义表格的列结构
  columns: [
    // 列配置示例:
    {
      key: 'name',           // 数据字段名 (字符串/函数) - 对应数据中的字段名，或自定义取值函数
      title: '姓名',         // 表头显示文字 (字符串) - 列标题显示的文本
      width: '100px',        // 列宽 (字符串) - 固定列宽，如'100px', '20%'
      minWidth: '80px',      // 最小宽度 (字符串) - 列的最小宽度，防止内容被压缩
      align: 'left',         // 对齐方式 (字符串) - 'left': 左对齐, 'center': 居中, 'right': 右对齐
      slot: 'name',          // 自定义插槽名 (字符串) - 使用插槽自定义列内容渲染
      formatter: null        // 格式化函数 (函数) - 自定义数据格式化，如日期格式化、数字格式化
    }
  ],

  // 表格显示设置
  showHeader: true,          // 是否显示表头 (布尔值) - true: 显示列标题, false: 隐藏表头
  stripe: true,              // 是否显示斑马纹 (布尔值) - true: 奇偶行不同背景色, false: 统一背景
  border: true,              // 是否显示边框 (布尔值) - true: 显示表格边框, false: 无边框样式
  wordWrap: true,            // 是否启用文字换行 (布尔值) - true: 文字自动换行, false: 文字超出截断

  // 横屏展示配置 - 移动端横屏查看功能
  landscape: {
    enabled: true,           // 是否启用横屏功能 (布尔值) - true: 显示横屏按钮, false: 禁用横屏
    autoRotate: true         // 是否自动旋转屏幕 (布尔值) - true: 自动旋转设备屏幕, false: 仅UI横屏
  },

  // 数据接口配置
  curl: {
    ls: '',                  // 列表数据接口地址 (字符串) - 例: "/api/table/list" - 获取表格数据的API
  },
  postData: {},              // 请求参数 (对象) - 发送给接口的参数，如: {page: 1, size: 20, filter: 'active'}
  mockData: false,           // 是否使用模拟数据 (布尔值) - true: 使用本地模拟数据, false: 调用真实API

  // 分页配置 - 控制数据分页加载
  pagination: {
    enabled: true,           // 是否启用分页 (布尔值) - 现在无条件启用，此配置保留兼容性
    pageSize: 20             // 每页数量 (数字) - 每次加载的数据条数，建议10-50之间
  },

  // 骨架屏配置 - 数据加载时的占位效果
  skeleton: {
    isShow: false,           // 是否启用骨架屏 (布尔值) - true: 显示加载占位效果, false: 不显示
    count: 5,                // 骨架屏数量 (数字) - 显示多少行骨架屏占位，建议3-8行
    row: 1,                  // 每个骨架屏的行数 (数字) - 每行占位的高度行数，表格通常为1
    rowWidth: ['100%'],      // 每行的宽度 (数组) - 占位条的宽度，表格通常为['100%']
    duration: 2000           // 骨架屏显示时长 (数字) - 毫秒，超过此时间自动隐藏，0表示不自动隐藏
  }
}
```

---

## 📝 表单控件

### yhc-picker

**功能**: 下拉选择器，支持单选/多选、远程数据

#### 基础用法
```vue
<template>
  <yhc-picker :config="pickerConfig" :form="form" />
</template>

<script setup>
const pickerConfig = {
  label: "选择项目",
  key: "project_id",
  opts: {
    url: "/api/projects",
    multiple: false,
    text_key: "name",
    contrast_key: "id"
  }
}
</script>
```

#### 配置选项
```javascript
{
  // 基础配置
  label: "下拉选择",       // 字段标签 (字符串) - 显示在表单项左侧的标签文字
  type: "text",            // 字段类型 (字符串) - 表单项类型，通常为"text"
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"category_id"
  placeholder: "请选择",    // 占位符 (字符串) - 未选择时显示的提示文字
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用选择, false: 可正常选择
  default: '',             // 默认值 (字符串/数组) - 字段的默认选中值
  rules: [],               // 验证规则 (数组) - 表单验证规则配置

  // 弹窗配置
  popup: {
    round: true,           // 是否圆角弹窗 (布尔值) - true: 圆角样式, false: 直角样式
    position: "bottom",    // 弹窗位置 (字符串) - "bottom": 底部弹出, "center": 居中, "right": 右侧
    style: { height: "50vh", overflow: "hidden" }, // 弹窗样式 (对象) - CSS样式对象
    closeable: false,      // 是否显示关闭按钮 (布尔值) - true: 显示X按钮, false: 不显示
  },

  // 选项配置 - 核心配置项
  opts: {
    url: "",               // 数据接口地址 (字符串) - 例: "/api/options" - 获取选项数据的API
    postData: {},          // 请求参数 (对象) - 发送给接口的参数，如: {category: 1, status: 'active'}
    merge: false,          // 是否合并数据 (布尔值) - true: 合并新旧数据, false: 替换数据
    multiple: true,        // 是否多选 (布尔值) - true: 支持多选, false: 单选模式
    text_key: "title",     // 显示字段名 (字符串) - 选项显示文字对应的数据字段，如"name", "title"
    contrast_key: "id",    // 值字段名 (字符串) - 选项值对应的数据字段，如"id", "value"
    keyMap: {},            // 字段映射 (对象) - 数据字段映射配置，用于数据转换
    defaultList: [],       // 默认选项列表 (数组) - 本地默认选项数据，当API无数据时使用
  },

  // 添加按钮配置 - 允许用户动态添加新选项
  addButton: {
    show: false,           // 是否显示添加按钮 (布尔值) - true: 显示添加按钮, false: 隐藏
    text: "添加",          // 按钮文字 (字符串) - 添加按钮显示的文本
    icon: "plus",          // 按钮图标 (字符串) - 按钮图标名称，如"plus", "add"
    position: "top",       // 按钮位置 (字符串) - "top": 列表顶部, "bottom": 列表底部
  },

  // 添加表单配置 - 添加新选项时的表单配置
  addForm: {
    title: "添加选项",     // 弹窗标题 (字符串) - 添加表单弹窗的标题文字
    form: [],             // 表单字段配置 (数组) - 表单项配置，格式与yhc-form组件一致
    popup: {
      round: true,        // 是否圆角弹窗 (布尔值) - true: 圆角样式, false: 直角样式
      position: "center", // 弹窗位置 (字符串) - "center": 居中显示, "bottom": 底部弹出
      style: { width: "90vw", maxWidth: "400px", maxHeight: "70vh", overflow: "hidden" }, // 弹窗样式 (对象)
      closeable: false,   // 是否显示关闭按钮 (布尔值) - true: 显示X按钮, false: 不显示
    },
  },

  // 添加接口配置 - 提交新选项到服务器的配置
  addApi: {
    url: "",              // 添加接口地址 (字符串) - 例: "/api/add-option" - 提交新选项的API
    method: "POST",       // 请求方法 (字符串) - "POST": POST请求, "PUT": PUT请求
    postData: {},         // 额外请求参数 (对象) - 提交时的额外参数，如: {category_id: 1}
    refreshAfterAdd: true, // 添加成功后是否刷新列表 (布尔值) - true: 自动刷新, false: 不刷新
    successCallback: null, // 成功回调函数 (函数) - 添加成功后的自定义回调
    errorCallback: null,   // 错误回调函数 (函数) - 添加失败后的自定义回调
  },
}
```

---

### yhc-picker-date

**功能**: 日期时间选择器，支持多种时间格式

#### 基础用法
```vue
<template>
  <yhc-picker-date :config="dateConfig" :form="form" />
</template>

<script setup>
const dateConfig = {
  label: "开始时间",
  key: "start_time",
  type: "datetime",      // datetime/date/time/time-short
  default: true          // 使用当前时间作为默认值
}
</script>
```

#### 支持的时间类型
- `datetime`: YYYY-MM-DD HH:mm:ss
- `date`: YYYY-MM-DD  
- `time`: HH:mm:ss
- `time-short`: HH:mm

---

### yhc-tag-selector

**功能**: 标签选择器，支持预设标签选择和自定义标签管理

#### 基础用法
```vue
<template>
  <yhc-tag-selector :config="tagConfig" :form="form" />
</template>

<script setup>
const tagConfig = {
  label: "商品标签",
  key: "tags",
  opts: {
    defaultList: [
      { id: 1, name: "热门" },
      { id: 2, name: "推荐" },
      { id: 3, name: "新品" }
    ]
  }
}
</script>
```

#### 配置选项
```javascript
{
  label: "标签选择",
  key: "",
  required: false,
  opts: {
    url: "",                    // API地址
    text_key: "name",           // 显示字段
    contrast_key: "id",         // 值字段
    defaultList: [],            // 默认数据
    maxTagLength: 10,           // 自定义标签最大长度
    maxCustomTags: 20           // 最大自定义标签数量
  },
  defaultSelected: []           // 默认选中标签ID数组
}
```

---

### yhc-editor

**功能**: 富文本编辑器，基于wangEditor

#### 基础用法
```vue
<template>
  <yhc-editor :config="editorConfig" :form="form" />
</template>

<script setup>
const editorConfig = {
  label: "内容",
  key: "content",
  height: 300,
  mode: "simple",        // simple/default
  toolbarConfig: {
    excludeKeys: ["|", "insertVideo"]
  }
}
</script>
```

---

### yhc-checkbox-group

**功能**: 复选框组，支持多选

#### 基础用法
```vue
<template>
  <yhc-checkbox-group :config="checkboxConfig" :form="form" />
</template>

<script setup>
const checkboxConfig = {
  label: "兴趣爱好",
  key: "hobbies",
  options: [
    { value: "reading", label: "阅读" },
    { value: "music", label: "音乐" },
    { value: "sports", label: "运动" }
  ]
}
</script>
```

---

## 🎨 UI组件

### yhc-breadcrumb

**功能**: 面包屑导航，支持自动生成和手动配置

#### 基础用法
```vue
<template>
  <yhc-breadcrumb :config="breadcrumbConfig" />
</template>

<script setup>
const breadcrumbConfig = {
  key: "breadcrumb",
  items: [
    { text: "首页", path: "/" },
    { text: "项目管理", path: "/project" },
    { text: "项目详情" }
  ],
  showHome: true,
  autoGenerate: false
}
</script>
```

#### 自动生成模式
```javascript
{
  key: "breadcrumb",
  autoGenerate: true,    // 根据路由自动生成
  showHome: true,        // 显示首页
  maxItems: 5           // 最大显示数量
}
```

---

### yhc-tabbar

**功能**: 底部导航栏

#### 基础用法
```vue
<template>
  <yhc-tabbar :config="tabbarConfig" />
</template>

<script setup>
const tabbarConfig = {
  active: 0,
  items: [
    { text: "首页", icon: "home-o", path: "/home" },
    { text: "分类", icon: "apps-o", path: "/category" },
    { text: "我的", icon: "user-o", path: "/profile" }
  ]
}
</script>
```

---

## 🔧 工具组件

### yhc-supply-list

**功能**: 供应商管理列表，内置路由配置

#### 基础用法
```vue
<template>
  <yhc-supply-list />
</template>
```

> 该组件内置了完整的供应商管理路由配置，无需额外配置

---

## 🚀 快速查找

### 按功能分类
- **数据展示**: yhc-list, yhc-table, yhc-grid, yhc-desc, yhc-notice
- **表单输入**: yhc-form, yhc-form-item, yhc-form-cust-field, yhc-input, yhc-editor
- **选择控件**: yhc-picker, yhc-checkbox-group, yhc-radio-group, yhc-tag-selector, yhc-segmented-control
- **日期时间**: yhc-picker-date, yhc-picker-date-time, yhc-picker-datetime, yhc-picker-datesecond, yhc-calendar
- **用户选择**: yhc-select-user, yhc-select-department
- **媒体上传**: yhc-select-image, yhc-editor
- **导航组件**: yhc-breadcrumb, yhc-tabbar, yhc-select-nav
- **交互控件**: yhc-switch, yhc-stepper, yhc-action-buttons
- **业务组件**: yhc-supply-list

### 按使用频率
1. **高频**: yhc-form, yhc-list, yhc-picker, yhc-input, yhc-form-item
2. **中频**: yhc-table, yhc-picker-date, yhc-tag-selector, yhc-breadcrumb, yhc-switch, yhc-segmented-control
3. **低频**: yhc-editor, yhc-calendar, yhc-supply-list, yhc-action-buttons, yhc-notice

### 常见组合
- **列表页**: yhc-list + yhc-breadcrumb + yhc-action-buttons
- **表单页**: yhc-form + yhc-picker + yhc-picker-date + yhc-select-user
- **详情页**: yhc-form + yhc-breadcrumb + yhc-desc
- **数据展示**: yhc-table + yhc-breadcrumb + yhc-notice
- **选择页面**: yhc-picker + yhc-tag-selector + yhc-segmented-control
- **管理页面**: yhc-list + yhc-action-buttons
- **首页布局**: yhc-grid + yhc-notice + yhc-tabbar

---

## 🎛️ 高级组件

### yhc-segmented-control

**功能**: 分段控制器，支持单选/多选、子表单联动

#### 基础用法
```vue
<template>
  <yhc-segmented-control :config="segmentConfig" :form="form" />
</template>

<script setup>
const segmentConfig = {
  label: "选择模式",
  key: "mode",
  multiple: false,         // 是否支持多选
  options: [
    { text: "列表", value: "list", icon: "list-o" },
    { text: "卡片", value: "card", icon: "apps-o" }
  ],
  // 子表单配置 (高级功能)
  child: {
    map: true,             // 是否使用映射模式
    showMode: "card",      // 当选中此值时显示子表单
    form: [...]            // 子表单字段配置
  }
}
</script>
```


### yhc-form-item

**功能**: 表单项包装器，用于包装和渲染各种表单组件

#### 基础用法
```vue
<template>
  <yhc-form-item :config="itemConfig" :form="form" />
</template>

<script setup>
const itemConfig = {
  // 支持所有表单组件的配置
  component: "yhc-input",  // 要渲染的组件类型
  label: "用户名",
  key: "username",
  required: true,

  // 子表单配置 (高级功能)
  child: {
    map: false,            // 是否使用映射模式
    showMode: undefined,   // 显示模式值
    form: [],              // 子表单字段
    formChild: [],         // 备用子表单字段
  },

  // 描述样式配置
  descHeight: 8,           // 描述区域高度
  descFontSize: 13,        // 描述字体大小
}
</script>
```

### yhc-form-cust-field

**功能**: 自定义表单字段组件，支持多种组件类型的数据格式处理

#### 基础用法
```vue
<template>
  <yhc-form-cust-field :config="custConfig" :form="form" />
</template>

<script setup>
const custConfig = {
  // API接口配置
  curl: {
    info: "/api/info",     // 获取详情接口
    add: "/api/add",       // 新增数据接口
    edit: "/api/edit",     // 编辑数据接口
  },
  postData: {},            // 请求参数

  // 表单字段配置
  form: [],                // 表单字段数组
  groupForm: [],           // 表单分组配置

  // 按钮配置
  button: {
    isShow: true,          // 是否显示按钮
    text: "提交",          // 按钮文字
    position: "normal",    // 按钮位置
  },

  // 数据处理配置
  format: null,            // 数据格式化函数
}
</script>
```

---

## 🔍 更多组件详情

### yhc-radio-group

**功能**: 单选框组

```vue
<template>
  <yhc-radio-group :config="radioConfig" :form="form" />
</template>

<script setup>
const radioConfig = {
  label: "性别",
  key: "gender",
  options: [
    { value: "male", label: "男" },
    { value: "female", label: "女" }
  ]
}
</script>
```

### yhc-switch

**功能**: 开关组件

```vue
<template>
  <yhc-switch :config="switchConfig" :form="form" />
</template>

<script setup>
const switchConfig = {
  label: "启用状态",
  key: "enabled",
  activeValue: 1,
  inactiveValue: 0
}
</script>
```

### yhc-stepper

**功能**: 步进器组件

```vue
<template>
  <yhc-stepper :config="stepperConfig" :form="form" />
</template>

<script setup>
const stepperConfig = {
  label: "数量",
  key: "quantity",
  min: 1,
  max: 999,
  step: 1
}
</script>
```

### yhc-select-user

**功能**: 用户选择器

```vue
<template>
  <yhc-select-user :config="userConfig" :form="form" />
</template>

<script setup>
const userConfig = {
  label: "选择用户",
  key: "user_id",
  multiple: false,
  url: "/api/users"
}
</script>
```

### yhc-select-department

**功能**: 部门选择器

```vue
<template>
  <yhc-select-department :config="deptConfig" :form="form" />
</template>

<script setup>
const deptConfig = {
  label: "选择部门",
  key: "dept_id",
  url: "/api/departments"
}
</script>
```

### yhc-select-image

**功能**: 图片选择器

```vue
<template>
  <yhc-select-image :config="imageConfig" :form="form" />
</template>

<script setup>
const imageConfig = {
  label: "选择图片",
  key: "images",
  multiple: true,
  maxCount: 9,
  uploadUrl: "/api/upload"
}
</script>
```

### yhc-input

**功能**: 增强输入框

```vue
<template>
  <yhc-input :config="inputConfig" :form="form" />
</template>

<script setup>
const inputConfig = {
  label: "用户名",
  key: "username",
  type: "text",
  maxlength: 20,
  showWordLimit: true
}
</script>
```

### yhc-desc

**功能**: 描述列表组件

```vue
<template>
  <yhc-desc :config="descConfig" :data="data" />
</template>

<script setup>
const descConfig = {
  title: "用户信息",
  items: [
    { label: "姓名", key: "name" },
    { label: "年龄", key: "age" },
    { label: "邮箱", key: "email" }
  ]
}
</script>
```

### yhc-segmented-control

**功能**: 分段控制器

```vue
<template>
  <yhc-segmented-control :config="segmentConfig" :form="form" />
</template>

<script setup>
const segmentConfig = {
  label: "类型",
  key: "type",
  options: [
    { value: "1", label: "选项1" },
    { value: "2", label: "选项2" },
    { value: "3", label: "选项3" }
  ]
}
</script>
```

### yhc-action-buttons

**功能**: 操作按钮组

```vue
<template>
  <yhc-action-buttons :config="buttonConfig" @click="onButtonClick" />
</template>

<script setup>
const buttonConfig = {
  buttons: [
    { text: "编辑", type: "primary", action: "edit" },
    { text: "删除", type: "danger", action: "delete" },
    { text: "查看", type: "default", action: "view" }
  ]
}
</script>
```

### yhc-grid

**功能**: 网格布局组件，支持自定义列数和跳转

```vue
<template>
  <yhc-grid :data="gridData" />
</template>

<script setup>
const gridData = {
  title: "功能菜单",      // 网格标题
  count: 4,              // 每行显示的网格项数量
  ctype: "grid",         // 网格类型: "grid": 纯图标网格, 其他类型显示文字
  data: [                // 网格项数据
    {
      icon: "home-o",    // 图标名称
      title: "首页",     // 项目标题
      text: "首页",      // 项目文本
      url: "/home",      // 跳转地址
      app_type: "native" // 应用类型: "native": 内部路由, "diy": 外部链接
    }
  ]
}
</script>
```

### yhc-notice

**功能**: 通知公告组件，支持轮播图和公告栏两种模式

```vue
<template>
  <yhc-notice :data="noticeData" />
</template>

<script setup>
const noticeData = {
  notice_type: "banner",  // 通知类型: "banner": 轮播图通知, "announce": 公告栏通知
  count: 3,               // 显示数量
  autoplay: 3000,         // 自动播放间隔(毫秒)
}
</script>
```

### yhc-action-buttons

**功能**: 操作按钮组，菜单管理专用

```vue
<template>
  <yhc-action-buttons
    :disabled="false"
    :loading="loadingType"
    @sync-day-to-week="onSyncDayToWeek"
    @sync-week-to-month="onSyncWeekToMonth"
    @delete-sunday="onDeleteSunday"
    @delete-weekend="onDeleteWeekend"
  />
</template>

<script setup>
const loadingType = ref(false) // 或具体的操作类型字符串

const onSyncDayToWeek = () => {
  console.log('同步日菜单至本周')
}
// ... 其他操作方法
</script>
```

### yhc-notice

**功能**: 通知公告组件

```vue
<template>
  <yhc-notice :config="noticeConfig" />
</template>

<script setup>
const noticeConfig = {
  title: "系统通知",
  content: "这是一条重要通知",
  type: "info",
  closeable: true
}
</script>
```

---

## 🎯 组件选择指南

### 表单场景
- **基础输入**: yhc-input
- **选择器**: yhc-picker, yhc-select-user, yhc-select-department
- **标签选择**: yhc-tag-selector
- **日期时间**: yhc-picker-date, yhc-picker-datetime
- **多选单选**: yhc-checkbox-group, yhc-radio-group
- **开关数字**: yhc-switch, yhc-stepper
- **富文本**: yhc-editor
- **图片上传**: yhc-select-image

### 数据展示场景
- **列表**: yhc-list
- **表格**: yhc-table
- **描述**: yhc-desc
- **网格**: yhc-grid

### 导航场景
- **面包屑**: yhc-breadcrumb
- **底部导航**: yhc-tabbar
- **分段控制**: yhc-segmented-control

### 交互场景
- **操作按钮**: yhc-action-buttons
- **通知提醒**: yhc-notice

---

## 🔧 全局配置

### 组件自动注册
项目使用了自动注册机制，所有 `yhc-` 开头的组件都会自动注册到全局：

```javascript
// src/untils/components.js
import { defineAsyncComponent } from "vue";
export const components = {
  install: function (vm) {
    const requireModules = import.meta.glob("../components/*/*.vue");
    for (const path in requireModules) {
      const result = path.match(/(y.*)\//);
      const modulesConent = requireModules[path];
      if(result){
        vm.component(result[1], defineAsyncComponent(modulesConent));
      }
    }
  },
};
```

### Vant组件自动导入
项目配置了Vant组件的自动导入：

```javascript
// vite.config.js
Components({
  resolvers: [
    VantResolver({
      importStyle: false,
    }),
  ],
  dirs: ["src/components"],
  extensions: ["vue"],
})
```

---

## 📞 技术支持

- **组件文档**: 查看各组件目录下的 README.md 文件
- **示例代码**: 参考 src/views 目录下的页面实现
- **问题反馈**: 联系开发团队或提交Issue

### 常见问题

1. **组件不显示**: 检查组件名称是否正确，确保以 `yhc-` 开头
2. **配置无效**: 确认配置对象结构是否正确
3. **数据不更新**: 检查响应式数据绑定
4. **样式问题**: 确认是否正确引入了Vant样式

### 最佳实践

1. **统一配置**: 建议将组件配置提取为独立的配置文件
2. **类型检查**: 使用TypeScript增强类型安全
3. **性能优化**: 大数据量时使用虚拟滚动
4. **响应式设计**: 考虑不同屏幕尺寸的适配

---

## 📊 组件配置速查表

### 表单组件通用配置
```javascript
{
  label: "字段标签",        // 显示标签
  key: "field_name",       // 字段名
  placeholder: "请输入",    // 占位符
  required: false,         // 是否必填
  disabled: false,         // 是否禁用
  rules: [],              // 验证规则
  default: undefined      // 默认值
}
```

### API接口配置
```javascript
{
  curl: {
    ls: "/api/list",      // 列表接口
    add: "/api/add",      // 新增接口
    edit: "/api/edit",    // 编辑接口
    info: "/api/info",    // 详情接口
    del: "/api/delete"    // 删除接口
  },
  postData: {}            // 请求参数
}
```

### 选择器配置
```javascript
{
  opts: {
    url: "/api/options",     // 选项数据接口
    multiple: false,         // 是否多选
    text_key: "name",        // 显示字段
    contrast_key: "id",      // 值字段
    defaultList: [],         // 默认选项
    maxlength: 14           // 显示文本长度限制
  }
}
```

### 弹窗配置
```javascript
{
  popup: {
    round: true,                    // 是否圆角
    position: "bottom",             // 弹出位置
    style: { height: "50vh" },      // 弹窗样式
    closeable: false               // 是否显示关闭按钮
  }
}
```

### 骨架屏配置
```javascript
{
  skeleton: {
    isShow: true,                   // 是否启用
    count: 5,                       // 骨架屏数量
    row: 3,                         // 每个骨架屏行数
    rowWidth: ['100%', '60%'],      // 每行宽度
    avatar: true,                   // 是否显示头像
    duration: 2000                  // 显示时长
  }
}
```

---

## 🎨 样式定制指南

### CSS变量
项目支持通过CSS变量定制组件样式：

```css
:root {
  --yhc-primary-color: #1989fa;
  --yhc-success-color: #07c160;
  --yhc-warning-color: #ff976a;
  --yhc-danger-color: #ee0a24;
  --yhc-text-color: #323233;
  --yhc-border-color: #ebedf0;
  --yhc-background-color: #f7f8fa;
}
```

### 组件样式覆盖
```scss
// 覆盖yhc-form样式
.yhc-form {
  .van-field__label {
    color: var(--yhc-text-color);
    font-weight: 500;
  }

  .van-button--primary {
    background-color: var(--yhc-primary-color);
  }
}

// 覆盖yhc-list样式
.yhc-list {
  .list-item {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 12px;
  }
}
```

---

## 🔄 版本更新日志

### v2.1.0 (当前版本)
- ✅ **重大更新**: 为31个核心组件添加详细中文注释
- ✅ **配置优化**: 超过550个配置项添加类型说明和使用指导
- ✅ **文档完善**: 建立统一的注释规范和最佳实践
- ✅ **新增组件**: yhc-segmented-control, yhc-form-cust-field等
- ✅ **功能增强**: yhc-picker支持动态添加选项，yhc-table支持横屏展示
- ✅ **体验优化**: yhc-list骨架屏配置更加灵活，yhc-form缓存机制完善

### v2.0.0
- ✅ 新增 yhc-table 横屏展示功能
- ✅ 优化 yhc-form 按钮显示逻辑
- ✅ 增强 yhc-picker-date 时间类型支持
- ✅ 改进 yhc-list 骨架屏体验
- ✅ 统一组件配置规范

### v1.5.0
- ✅ 新增 yhc-breadcrumb 自动生成功能
- ✅ 优化 yhc-editor 图片上传
- ✅ 增加 yhc-checkbox-group 组件
- ✅ 改进移动端适配

### v1.0.0
- ✅ 基础组件库建立
- ✅ 核心组件开发完成
- ✅ 自动注册机制实现
