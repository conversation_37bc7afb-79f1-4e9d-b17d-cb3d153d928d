import instance from "./request";
import { config } from "@/config/api";
import { useLoginStore } from "@/store/dingLogin";

export function post(url, data = {}, config = {}) {
  return instance(
    Object.assign(
      {
        method: "post",
        url: url,
        data,
      },
      config
    )
  );
}
export function postDel(url,data = {}, config = {}) {
  // console.log("postDel",data);
  let fullUrl = url + "?id=" + data.id;
  if (data.type !== undefined) {
    fullUrl += "&type=" + data.type;
  }
  return instance(
    Object.assign(
      {
        method: "post",
        // url: url + "?id=" + data.id,
        url: fullUrl,
      },
      config
    )
  );
}
export function get(url, params = {}, config = {}) {
  return instance(
    Object.assign(
      {
        method: "get",
        url: url,
        params,
      },
      config
    )
  );
}
let ran = navigator.userAgent;
let isAndroid = ran.indexOf("Android") > -1 || ran.indexOf("Linux") > -1;
let isIOS = !!ran.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
export function upload(url, data = {}, config = {}) {
  let formData = new FormData();
  for (let key in data) {
    if (data.hasOwnProperty(key)) {
      formData.append(key, data[key]);
    }
  }
  return instance(
    Object.assign(
      {
        method: "post",
        url: url,
        data: formData,
      },
      config,
      isIOS
        ? {
            transformRequest: (data) => {
              data.toString = () => "[object FormData]";
              return data;
            },
            headers: {
              // "Content-Type": "multipart/form-data",
            },
          }
        :
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    )
  );
}
export function download(url, data = {}, config = { responseType: "blob" }) {
  return new Promise((resolve, reject) => {
    instance(
      Object.assign(
        {
          method: "post",
          url: url,
          data,
        },
        config
      )
    )
      .then((response) => {
        /* 从请求头中获取文件名称,用于将文件流转换成对应文件格式的文件,防止文件损坏 */
        let split = response.headers["content-disposition"].split("=");
        /* 将数据流转换为对应格式的文件,并创建a标签,模拟点击下载,实现文件下载功能 */
        let fileReader = new FileReader();
        fileReader.readAsDataURL(response.data);
        fileReader.onload = (e) => {
          let a = document.createElement("a");
          a.download = split[1];
          a.href = e.target.result;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          resolve(response);
        };
      })
      .catch((err) => {
        reject(err);
      });
  });
}
export function checkApi(apiConfig) {
  let token = useLoginStore().token;
  if (token) {
    apiConfig.headers["Authorization"] = token;
  }
  if (config.isOffWhiteList) {
    let url = config.whiteList.find((el) => apiConfig.url.includes(el));
    return url ? true : token ? true : false;
  } else {
    return true;
  }
}

export const axiosPlugins = {
  install: function (vm) {
    vm.config.globalProperties.$get = get;
    vm.config.globalProperties.$post = post;
    vm.config.globalProperties.$postDel = postDel;
    vm.config.globalProperties.$upload = upload;
    vm.config.globalProperties.$download = download;
  },
};
