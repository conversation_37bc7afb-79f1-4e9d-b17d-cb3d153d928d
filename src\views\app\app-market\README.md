# 应用市场页面

## 功能概述

应用市场页面是一个展示企业应用的移动端页面，采用现代化的设计风格，提供完整的应用浏览、搜索和分类功能。

## 页面特性

### 🎨 界面设计
- **仿钉钉风格**: 采用企业级移动端设计规范
- **响应式布局**: 完美适配各种移动设备
- **现代化UI**: 圆角卡片、渐变色彩、流畅动画

### 🔍 核心功能
- **Banner轮播**: 顶部轮播图展示重点推荐
- **智能搜索**: 支持应用名称和描述的实时搜索
- **分类浏览**: 四大分类（行业专属、进销存ERP、信息模块、其他）
- **应用卡片**: 左图标右信息的卡片式布局
- **骨架屏**: 优化加载体验的骨架屏动画

### 📱 移动端优化
- **触摸友好**: 适合手指操作的按钮和间距
- **滚动流畅**: 优化的滚动性能
- **加载优化**: 智能的数据加载和缓存策略

## 技术实现

### 组件架构
```
应用市场页面
├── van-nav-bar (顶部导航)
├── van-swipe (Banner轮播)
└── yhc-list (核心列表组件)
    ├── 搜索功能
    ├── 分类标签
    ├── 骨架屏
    └── 应用卡片列表
```

### 核心组件使用

#### yhc-list 配置
```javascript
const listConfig = {
  search: {
    isShow: true,
    placeholder: '搜索应用...',
    key: 'search'
  },
  tabs: {
    isShow: true,
    sticky: true,
    list: [
      { text: '行业专属', key: 'industry' },
      { text: '进销存ERP', key: 'erp' },
      { text: '信息模块', key: 'info' },
      { text: '其他', key: 'other' }
    ]
  },
  skeleton: {
    isShow: true,
    count: 6,
    duration: 1500
  }
}
```

#### Banner轮播配置
```javascript
const bannerList = [
  {
    title: 'Banner 1',
    subtitle: '应用市场精选推荐',
    color: '#1989fa'
  }
  // ... 更多banner
]
```

## 数据结构

### 应用数据格式
```javascript
{
  id: 1,
  title: '进销存管理全链条流程',
  description: '货物 库存 发票销售',
  price: '18.98',
  category: 'industry'
}
```

### 分类数据结构
```javascript
const mockAppData = {
  industry: [...], // 行业专属应用
  erp: [...],      // 进销存ERP应用
  info: [...],     // 信息模块应用
  other: [...]     // 其他应用
}
```

## 样式特色

### 应用卡片设计
- **左侧图标**: 60x60px 蓝色渐变圆角图标
- **右侧信息**: 标题、描述、价格垂直排列
- **悬停效果**: 卡片上浮和阴影变化
- **文字处理**: 标题单行省略，描述两行省略

### 色彩方案
- **主色调**: #1989fa (蓝色)
- **价格色**: #ee0a24 (红色)
- **背景色**: #f7f8fa (浅灰)
- **卡片色**: #ffffff (白色)

### 响应式适配
- **小屏适配**: 375px以下设备的特殊优化
- **图标缩放**: 小屏下图标尺寸自动调整
- **文字缩放**: 字体大小响应式调整

## 使用方法

### 1. 路由配置
```javascript
{
  name: "app-market",
  path: "/app-market",
  meta: { hasTabbar: false },
  component: () => import("@/views/app/app-market")
}
```

### 2. 导航集成
在工作台添加入口：
```javascript
{
  text: "应用市场",
  url: "/app-market",
  icon: "datav",
  color: "rgba(138,43,226, 1.0)"
}
```

### 3. 事件处理
```javascript
// 应用点击事件
const handleAppClick = (item) => {
  showToast(`点击了应用: ${item.title}`)
  // 跳转到应用详情页
  // router.push(`/app-detail/${item.id}`)
}

// 分类切换事件
const onClickTab = (tabData) => {
  const tabKey = listConfig.tabs.list[tabData.name]?.key
  setMockDataForCurrentTab(tabKey)
}
```

## 扩展功能

### 可扩展特性
1. **应用详情页**: 点击应用卡片跳转详情
2. **应用安装**: 集成应用安装功能
3. **收藏功能**: 用户收藏喜欢的应用
4. **评分评论**: 应用评价系统
5. **推荐算法**: 智能应用推荐

### API集成
```javascript
// 替换模拟数据为真实API
listConfig.curl.ls = '/api/apps/list'
listConfig.mockData = false
```

## 性能优化

### 加载优化
- **骨架屏**: 1.5秒骨架屏提升感知性能
- **懒加载**: 图片和内容的懒加载
- **缓存策略**: 应用数据的本地缓存

### 交互优化
- **防抖搜索**: 搜索输入防抖处理
- **平滑滚动**: 优化的滚动体验
- **触摸反馈**: 点击时的视觉反馈

## 维护说明

### 数据更新
- 修改 `mockAppData` 对象更新应用数据
- 调整 `bannerList` 数组更新轮播内容
- 配置 `listConfig.tabs.list` 修改分类

### 样式定制
- 修改 CSS 变量调整主题色彩
- 调整 `.app-card` 样式定制卡片外观
- 更新响应式断点适配不同设备

### 功能扩展
- 在 `handleAppClick` 中添加跳转逻辑
- 扩展 `onClickTab` 实现分类统计
- 集成真实API替换模拟数据

## 更新日志

### v1.0.0 (2024-01-15)
- ✅ 完成基础页面结构
- ✅ 集成yhc-list组件
- ✅ 实现Banner轮播功能
- ✅ 添加搜索和分类功能
- ✅ 完成响应式样式适配
- ✅ 集成到工作台导航
