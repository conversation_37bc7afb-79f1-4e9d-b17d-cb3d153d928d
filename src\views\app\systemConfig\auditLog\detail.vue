<template>
  <div class="audit-detail-container">
    <!-- 详情内容 -->
    <div class="detail-content">
      <div class="detail-card">
        <!-- 操作名称 -->
        <div class="detail-item">
          <div class="item-label">操作名称</div>
          <div class="item-value">{{ logDetail.operation }}</div>
        </div>

        <!-- 操作人 -->
        <div class="detail-item">
          <div class="item-label">操作人</div>
          <div class="item-value">{{ logDetail.operator }}</div>
        </div>

        <!-- 操作时间 -->
        <div class="detail-item">
          <div class="item-label">操作时间</div>
          <div class="item-value">{{ logDetail.operateTime }}</div>
        </div>

        <!-- 明细 -->
        <div class="detail-item detail-item-full">
          <div class="item-label">明细</div>
          <div class="item-value detail-content-text">{{ logDetail.detail }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 日志详情数据
const logDetail = ref({
  id: '',
  operation: '',
  operator: '',
  operateTime: '',
  detail: ''
})

// 模拟详情数据
const mockDetailData = {
  1: {
    id: 1,
    operation: '管理员张振北进行了充值计划新增操作',
    operator: '张振北',
    operateTime: '2025-07-15 22:42:27',
    detail: '新增充值计划：\n- 计划名称：学生充值套餐A\n- 充值金额：100元\n- 赠送金额：10元\n- 适用对象：在校学生\n- 有效期：2025-12-31\n- 操作IP：***************\n- 操作终端：管理后台\n- 备注：针对学生群体的优惠充值方案'
  },
  2: {
    id: 2,
    operation: '管理员张振北进行了充值计划删除操作',
    operator: '张振北',
    operateTime: '2025-07-15 22:36:18',
    detail: '删除充值计划：\n- 计划名称：临时充值套餐B\n- 原充值金额：50元\n- 原赠送金额：5元\n- 删除原因：活动结束\n- 操作IP：***************\n- 操作终端：管理后台\n- 影响用户：0人（无用户使用此套餐）'
  },
  3: {
    id: 3,
    operation: '管理员张振北进行了充值计划新增操作',
    operator: '张振北',
    operateTime: '2025-07-15 22:36:05',
    detail: '新增充值计划：\n- 计划名称：教职工充值套餐\n- 充值金额：200元\n- 赠送金额：15元\n- 适用对象：教职工\n- 有效期：2025-12-31\n- 操作IP：***************\n- 操作终端：管理后台\n- 备注：为教职工提供的专属充值优惠'
  },
  4: {
    id: 4,
    operation: '管理员张振北进行了充值计划删除操作',
    operator: '张振北',
    operateTime: '2025-07-15 22:35:35',
    detail: '删除充值计划：\n- 计划名称：节日特惠套餐\n- 原充值金额：150元\n- 原赠送金额：20元\n- 删除原因：节日活动结束\n- 操作IP：***************\n- 操作终端：管理后台\n- 影响用户：12人（已使用此套餐的用户不受影响）'
  },
  5: {
    id: 5,
    operation: '管理员张振北进行了充值计划删除操作',
    operator: '张振北',
    operateTime: '2025-07-15 22:35:32',
    detail: '删除充值计划：\n- 计划名称：新生入学套餐\n- 原充值金额：80元\n- 原赠送金额：8元\n- 删除原因：新生入学季结束\n- 操作IP：***************\n- 操作终端：管理后台\n- 影响用户：0人（套餐已过期）'
  }
}

// 加载详情数据
const loadDetail = () => {
  const id = route.query.id
  if (id && mockDetailData[id]) {
    logDetail.value = mockDetailData[id]
  } else {
    // 如果没有找到对应数据，显示默认信息
    logDetail.value = {
      id: id || '',
      operation: '未知操作',
      operator: '未知',
      operateTime: '未知',
      detail: '暂无详细信息'
    }
  }
}

onMounted(() => {
  loadDetail()
})
</script>

<style lang="scss" scoped>
.audit-detail-container {
  min-height: 100%;
  background: #f7f8fa;
}

.detail-content {
  padding: 16px;
}

.detail-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 20px;
  margin-bottom: 20px;
  min-height: 24px;
  border-bottom: 0.5px solid #ebedf0;

  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }

  .item-label {
    font-size: 14px;
    color: #646566;
    font-weight: normal;
    flex-shrink: 0;
    text-align: left;
    width: 80px;
    line-height: 24px;
  }

  .item-value {
    font-size: 16px;
    color: #323233;
    font-weight: normal;
    line-height: 24px;
    word-wrap: break-word;
    word-break: break-all;
    text-align: right;
    flex: 1;
    margin-left: 20px;
  }
}

.detail-item-full {
  .item-label {
    text-align: left;
  }

  .item-value {
    text-align: right;
    margin-left: 20px;
  }

  .detail-content-text {
    white-space: pre-line;
    line-height: 22px;
    font-size: 15px;
    text-align: right;
  }
}
</style>
