<template>
  <section class="advanced-config-section">
    <!-- 取消预定截止时间 -->
    <van-cell-group inset>
      <van-field
        v-model="formData.cancel_booking_deadline_type_text"
        name="cancel_deadline_type"
        label="取消预定截止时间"
        labelWidth="130px"
        placeholder="请选择取消预定截止时间"
        readonly
        :is-link="!props.disabled"
        :disabled="props.disabled"
        @click="props.disabled ? null : $emit('showCancelBookingDeadlineSelector')"
      />

      <!-- 自定义截止子表单 - 嵌套在父级卡片内部 -->
      <div v-if="formData.cancel_deadline_type === 3" class="conditional-form">
        <van-field name="cancel_deadline_time_type" label="截止时间" :disabled="props.disabled">
          <template #input>
            <van-radio-group v-model="formData.cancel_deadline_time_type" direction="horizontal" :disabled="props.disabled">
              <van-radio :name="0" shape="square" :disabled="props.disabled">统一截止</van-radio>
              <van-radio :name="1" shape="square" :disabled="props.disabled">分餐截止</van-radio>
            </van-radio-group>
          </template>
        </van-field>



        <!-- 统一截止子表单 -->
        <div v-if="formData.cancel_deadline_time_type === 0" class="unified-deadline-form">
          <van-field
            v-model="formData.cancel_unified_deadline_time_text"
            name="cancel_unified_deadline_time"
            label="时间"
            placeholder="请选择截止时间"
            readonly
            :is-link="!props.disabled"
            :disabled="props.disabled"
            @click="props.disabled ? null : $emit('showCancelUnifiedTimeSelector')"
          />
          <van-field name="cancel_deadline_advance_days" labelWidth="130px" :disabled="props.disabled" label="截止时间提前/天">
            <template #input>
              <van-stepper v-model="formData.cancel_deadline_advance_days" min="0" max="99" :disabled="props.disabled" />
            </template>
          </van-field>
        </div>

        <!-- 分餐截止子表单 -->
        <div v-if="formData.cancel_deadline_time_type === 1" class="separate-deadline-form">
          <!-- 餐时列表 -->
          <div class="meal-times-list">
            <div
              v-for="mealtime in formData.mealtimes_list"
              :key="mealtime.id"
              class="meal-time-item"
            >
              <van-field
                :model-value="getMealtimeDeadlineTime(mealtime.id, 'cancel')"
                :name="`cancel_meal_time_${mealtime.id}`"
                :label="mealtime.title"
                placeholder="请选择时间"
                readonly
                :is-link="!props.disabled"
                :disabled="props.disabled"
                @click="props.disabled ? null : handleMealtimeClick(mealtime.id, 'cancel')"
              />
            </div>
          </div>
           <van-field name="cancel_deadline_advance_days" labelWidth="130px" :disabled="props.disabled" label="截止时间提前/天">
            <template #input>
              <van-stepper v-model="formData.cancel_deadline_advance_days" min="0" max="99" :disabled="props.disabled" />
            </template>
          </van-field>
        </div>
      </div>
    </van-cell-group>

    <!-- 统一截止提前天数说明文字 -->
    <div v-if="formData.cancel_deadline_type === 3 && formData.cancel_deadline_time_type === 0" class="advance-days-description">
      例：填1天，提前1天截止时间前可进行预定
    </div>

    <!-- 说明文字 -->
    <div v-if="formData.cancel_deadline_type === 3 && formData.cancel_deadline_time_type === 1" class="advance-days-description">
      例：填1天，提前1天截止时间前可进行预定
    </div>

    <!-- 预定时核销 -->
    <van-cell-group inset>
      <van-field name="booking_verification_enabled" input-align="right" label="预定时核销" :disabled="props.disabled">
        <template #input>
          <van-switch v-model="formData.booking_verification_enabled" :active-value="1" :inactive-value="0" :disabled="props.disabled" />
        </template>
      </van-field>

      <!-- 核销方式子表单 - 嵌套在父级卡片内部 -->
      <div v-if="formData.booking_verification_enabled === 0" class="conditional-form">
        <van-field
         name="verification_methods"
         input-align="right"
          label="核销方式" :disabled="props.disabled">
          <template #input>
            <van-checkbox-group v-model="formData.verification_methods" direction="horizontal" shape="square" :disabled="props.disabled">
              <van-checkbox name="主动" disabled :model-value="true">主动</van-checkbox>
              <van-checkbox name="出餐" :disabled="props.disabled">出餐</van-checkbox>
              <van-checkbox name="闭餐" :disabled="props.disabled">闭餐</van-checkbox>
            </van-checkbox-group>
          </template>
        </van-field>
      </div>
    </van-cell-group>

    <div v-if="formData.booking_verification_enabled === 0" class="advance-days-description">
      例：闭餐核销，未核销订单在餐时结束时统一核销
    </div>

    <!-- 扣费方式 -->
    <van-cell-group inset>
      <van-field
        v-model="formData.charge_method_text"
        name="charge_method"
        label="扣费方式"
        placeholder="请选择扣费方式"
        readonly
        :is-link="!props.disabled"
        :disabled="props.disabled"
        @click="props.disabled ? null : $emit('showChargeMethodSelector')"
      />
    </van-cell-group>
  </section>
</template>

<script setup>
import { watch } from 'vue'

// Props
const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'showCancelBookingDeadlineSelector',
  'showCancelUnifiedTimeSelector',
  'showCancelMealTimeSelector',
  'showChargeMethodSelector'
])

// 处理餐时点击事件
const handleMealtimeClick = (mealtimeId, type) => {
  emit('showCancelMealTimeSelector', mealtimeId, type)
}

// 获取餐时截止时间的方法
const getMealtimeDeadlineTime = (mealtimeId, type) => {
  const deadline = props.formData.mealtime_deadlines?.find(d => d.mealtime_id === mealtimeId)
  if (!deadline) return ''

  return type === 'booking' ? deadline.booking_deadline_time : deadline.cancel_deadline_time
}

// 确保"主动"选项始终在核销方式中
watch(() => props.formData.verification_methods, (newMethods) => {
  if (Array.isArray(newMethods) && !newMethods.includes('主动')) {
    newMethods.unshift('主动')
  }
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
@import './common-styles.scss';
.advanced-config-section{
  .van-field {
    padding: 16px;
    font-size: 16px;
  }
}
</style>
