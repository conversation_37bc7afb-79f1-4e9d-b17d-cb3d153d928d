<template>
  <div class="announcement-add-container">
    <!-- 导航栏 -->
    <van-nav-bar title="新增公告" left-arrow @click-left="goBack" />
    
    <!-- 表单内容 -->
    <div class="form-container">
      <van-form @submit="onSubmit" ref="formRef">
        <!-- 公告标题 -->
        <van-cell-group inset title="基本信息">
          <van-field
            v-model="formData.title"
            name="title"
            label="公告标题"
            placeholder="请输入公告标题"
            :rules="[{ required: true, message: '请输入公告标题' }]"
            maxlength="50"
            show-word-limit
          />
          
          <!-- 公告状态 -->
          <van-field
            v-model="statusText"
            name="status"
            label="发布状态"
            placeholder="请选择发布状态"
            readonly
            is-link
            @click="showStatusPicker = true"
            :rules="[{ required: true, message: '请选择发布状态' }]"
          />
        </van-cell-group>

        <!-- 公告内容 -->
        <van-cell-group inset title="公告内容">
          <van-field
            v-model="formData.content"
            name="content"
            label="公告内容"
            type="textarea"
            placeholder="请输入公告内容"
            rows="6"
            autosize
            maxlength="1000"
            show-word-limit
            :rules="[{ required: true, message: '请输入公告内容' }]"
          />
        </van-cell-group>

        <!-- 附件上传 -->
        <van-cell-group inset title="附件">
          <van-cell title="附件上传" :value="`已上传${formData.attachments.length}个文件`" is-link @click="showUploader = true" />
          
          <!-- 附件列表 -->
          <div v-if="formData.attachments.length > 0" class="attachment-list">
            <div v-for="(file, index) in formData.attachments" :key="index" class="attachment-item">
              <div class="file-info">
                <van-icon name="description" size="16" />
                <span class="file-name">{{ file.name }}</span>
                <span class="file-size">{{ file.size }}</span>
              </div>
              <van-icon name="cross" size="16" @click="removeAttachment(index)" />
            </div>
          </div>
        </van-cell-group>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <van-button type="default" block @click="saveDraft">
            保存草稿
          </van-button>
          <van-button type="primary" block native-type="submit" :loading="submitting">
            发布公告
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 状态选择器 -->
    <van-popup v-model:show="showStatusPicker" position="bottom">
      <van-picker
        :columns="statusColumns"
        @confirm="onStatusConfirm"
        @cancel="showStatusPicker = false"
      />
    </van-popup>

    <!-- 文件上传 -->
    <van-popup v-model:show="showUploader" position="bottom" :style="{ height: '50%' }">
      <div class="uploader-container">
        <div class="uploader-header">
          <span>上传附件</span>
          <van-icon name="cross" @click="showUploader = false" />
        </div>
        <van-uploader
          v-model="uploadFiles"
          multiple
          :max-count="5"
          :max-size="10 * 1024 * 1024"
          @oversize="onOversize"
          @delete="onFileDelete"
          upload-text="选择文件"
        />
        <div class="uploader-footer">
          <van-button type="primary" block @click="confirmUpload">
            确认上传
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'

const router = useRouter()
const formRef = ref()

// 表单数据
const formData = reactive({
  title: '',
  status: '',
  content: '',
  attachments: []
})

// 状态相关
const showStatusPicker = ref(false)
const statusColumns = [
  { text: '立即发布', value: 'published' },
  { text: '保存草稿', value: 'draft' }
]

const statusText = computed(() => {
  const status = statusColumns.find(item => item.value === formData.status)
  return status ? status.text : ''
})

// 文件上传相关
const showUploader = ref(false)
const uploadFiles = ref([])
const submitting = ref(false)

// 方法
const goBack = async () => {
  if (hasFormData()) {
    try {
      await showConfirmDialog({
        title: '确认离开',
        message: '当前有未保存的内容，确定要离开吗？'
      })
      router.back()
    } catch (error) {
      // 用户取消
    }
  } else {
    router.back()
  }
}

const hasFormData = () => {
  return formData.title || formData.content || formData.attachments.length > 0
}

const onStatusConfirm = ({ selectedOptions }) => {
  formData.status = selectedOptions[0].value
  showStatusPicker.value = false
}

const removeAttachment = (index) => {
  formData.attachments.splice(index, 1)
}

const onOversize = () => {
  showToast('文件大小不能超过10MB')
}

const onFileDelete = (file, detail) => {
  console.log('删除文件:', file, detail)
}

const confirmUpload = () => {
  // 模拟文件上传
  uploadFiles.value.forEach(file => {
    if (!formData.attachments.find(item => item.name === file.file.name)) {
      formData.attachments.push({
        name: file.file.name,
        size: formatFileSize(file.file.size),
        url: URL.createObjectURL(file.file)
      })
    }
  })
  
  showUploader.value = false
  uploadFiles.value = []
  showToast('附件上传成功')
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const saveDraft = async () => {
  try {
    await formRef.value.validate(['title', 'content'])
    
    submitting.value = true
    
    // 模拟保存草稿
    setTimeout(() => {
      submitting.value = false
      showToast('草稿保存成功')
      router.back()
    }, 1000)
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

const onSubmit = () => {
  submitting.value = true
  
  // 模拟发布公告
  setTimeout(() => {
    submitting.value = false
    showToast('公告发布成功')
    router.back()
  }, 1000)
}
</script>

<style lang="scss" scoped>
.announcement-add-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.form-container {
  padding: 16px 0;
}

.attachment-list {
  padding: 0 16px;
  
  .attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #ebedf0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .file-info {
      display: flex;
      align-items: center;
      flex: 1;
      
      .file-name {
        margin: 0 8px;
        font-size: 14px;
        color: #323233;
        flex: 1;
      }
      
      .file-size {
        font-size: 12px;
        color: #969799;
      }
    }
  }
}

.submit-section {
  padding: 24px 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.uploader-container {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .uploader-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
  }
  
  .uploader-footer {
    margin-top: auto;
    padding-top: 16px;
  }
}

:deep(.van-cell-group__title) {
  font-weight: 500;
  color: #323233;
}

:deep(.van-field__label) {
  color: #646566;
  font-weight: normal;
}

:deep(.van-uploader) {
  flex: 1;
}
</style>
