import { useLoginStore } from "@/store/dingLogin";
import { config } from "@/config/router";

export function setTransitionName(to, from) {
  let toLg = to.path.split("/").length;
  let fromLg = from.path.split("/").length;
  to.meta.transitionName =
    toLg > fromLg ? "left" : toLg === fromLg ? "fade" : "right";
}
export function checkRouter(to, from) {
  if (!config.isOffWhiteList || config.whiteList.includes(to.path)) {
    return true;
  }
  let app = useLoginStore();
  return app.token;
}
let list = [
  {
    icon: "home-o",
    text: "首页",
    path: "/home",
  },
  {
    icon: "records",
    text: "账单",
    path: "/bill",
  },
  {
    icon: "setting-o",
    text: "工作台",
    path: "/app",
  },
  {
    icon: "user-o",
    text: "我的",
    path: "/user",
  },
];
let position = (window.history.state && window.history.state.position) || 0;
export function checkBack(to, from) {
  if (list.find((el) => el.path == from.path)) {
    let curPosition = window.history.state.position;
    if (curPosition < position) {
      let app = useLoginStore();
      if (app.browserEnv == "wx") {
        app.proxy.$_dd.closeWindow();
      } else {
        app.proxy.$_dd.biz.navigation.close({
          onSuccess: function (result) {
            console.log(result, "result");
          },
          onFail: function (err) {
            console.log(err, "err");
          },
        });
      }
    }
  }
  position = window.history.state.position;
}
