.left-enter-active,
.left-leave-active {
  // transition: all 0.35s ease-out;
}

.left-enter-to {
  position: absolute;
  right: 0;
}

.left-enter-from {
  position: absolute;
  right: -100%;
}

.left-leave-to {
  position: absolute;
  left: -100%;
}

.left-leave-from {
  position: absolute;
  left: 0;
}


.right-enter-active,
.right-leave-active {
  // transition: all 0.35s ease-out;
}

.right-enter-to {
  position: absolute;
  left: 0;
}

.right-enter-from {
  position: absolute;
  left: -100%;
}

.right-leave-to {
  position: absolute;
  right: -100%;
}

.right-leave-from {
  position: absolute;
  right: 0;
}

.fade-enter-active,
.fade-leave-active {
  // transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}