<template>
    <div class="create-group-container">
        <div class="group-name-section">
            <van-cell-group inset>
                <van-checkbox v-model="checked" style="height: 52px;padding-left: 16px;"
                    @click="allSelect">全选</van-checkbox>
            </van-cell-group>
        </div>
        <div class="group-name-section" style="margin-bottom: 10px;">
            <van-cell-group inset>
                <template v-for="item in availableDevices" :key="item">
                    <van-checkbox v-model="item.checked" style="height: 52px;padding-left: 16px;font-size: 17px;"
                        @click="select(item)">{{ item.title }}</van-checkbox>
                </template>
            </van-cell-group>
        </div>
        <div
            style="height: 90px;width: 100%;background-color: #fff;padding: 0 16px;display: flex;justify-content: space-between;align-items: center;position: fixed;bottom: 0px;">
            <div style="font-size: 14px;color: #007FFF;">已选中：{{ checkedDevices.length }}台</div>
            <van-button square type="primary" style="width: 112px;height: 44px;border-radius: 8px;"
                @click="confirmSelect">确定</van-button>
        </div>
    </div>
</template>
<script setup>
import { ref, getCurrentInstance } from 'vue'
import { showToast } from 'vant'
import { useRouter, useRoute } from 'vue-router'
let checked = ref(false)
let route = useRoute()
let router = useRouter()
let checkedDevices = ref([])
const { proxy } = getCurrentInstance();
let availableDevices = ref([])
let routeSelectArr = ref([])
onMounted(() => {
    console.log('当前路由参数:111', route)
    if (route.query.arr&&JSON.parse(route.query.arr).length > 0) {
        let arr = JSON.parse(route.query.arr)
        arr.forEach(v => {
            let obj = {
                id: v.id,
                title: v.title,
            }
            routeSelectArr.value.push(obj)
        })
        console.log('已选设备:', availableDevices.value)
    }
})
function getDeviceList() {
    proxy.$get('/device/get_device_all', { type: route.query.type }).then((res) => {
        if (res.code === 200) {
            availableDevices.value = res.data
            availableDevices.value.forEach(v => {
                v.checked = false
            });
            if (routeSelectArr.value.length > 0) {
                availableDevices.value.forEach(v => {
                    routeSelectArr.value.forEach(item => {
                        if (v.id === item.id) {
                            v.checked = true
                            checkedDevices.value.push({
                                id: v.id,
                                title: v.title
                            })
                        }
                    })
                })
            }
            console.log('获取设备列表成功:', res.data)
        } else {
            showToast(res.msg || '获取设备列表失败，请稍后重试')
        }
    }).catch(error => {
        showToast(error.msg || '获取设备列表失败，请稍后重试')
    })
}
getDeviceList()
function select(item) {
    console.log('选中设备:', item)
    if (item.checked) {
        checkedDevices.value.push({ id: item.id, title: item.title })
    } else {
        checkedDevices.value = checkedDevices.value.filter(v => v.id !== item.id)
    }
    console.log('已选设备:', checkedDevices.value)
}
function allSelect() {
    if (checked.value) {
        checkedDevices.value = []
        availableDevices.value.forEach(v => {
            v.checked = true
            checkedDevices.value.push({ id: v.id, title: v.title })
        })
    } else {
        availableDevices.value.forEach(v => {
            v.checked = false
        })
        checkedDevices.value = []
    }
    console.log('已选设备:', checkedDevices.value)
}
function confirmSelect() {
    if (checkedDevices.value.length === 0) {
        showToast('请至少选择一台设备')
        return
    }
    console.log('确认选中设备:', route.query)
    if (route.query.itemType === 'add') { 
        router.push({
            name: 'deviceManagementCreateGroup',
            query: {
                devices: JSON.stringify(checkedDevices.value),
                type: route.query.type,
                name: route.query.name,
            }
        })
    }else{
        router.push({
            name: 'deviceManagementEditGroup',
            query: {
                devices: JSON.stringify(checkedDevices.value),
                type: route.query.type,
                id: route.query.id,
                name: route.query.name,
                isSelect: true
            }
        })
    }
}
</script>
<style lang="scss" scoped>
.create-group-container {
    min-height: 90vh;
    background: #f7f8fa;
    padding-bottom: 80px;
}

.group-name-section,
.add-device-section,
.devices-section {
    margin-top: 16px;
}
</style>