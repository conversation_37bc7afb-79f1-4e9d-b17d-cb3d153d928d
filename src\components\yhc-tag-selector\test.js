/**
 * yhc-tag-selector 组件测试用例
 * 
 * 测试功能：
 * 1. 基础标签选择功能
 * 2. 自定义标签添加/删除
 * 3. 数据双向绑定
 * 4. API数据加载
 * 5. 表单验证
 */

// 测试配置数据
export const testConfigs = {
  // 基础配置
  basic: {
    label: "基础标签选择",
    key: "basic_tags",
    opts: {
      defaultList: [
        { id: 1, name: "热门" },
        { id: 2, name: "推荐" },
        { id: 3, name: "新品" },
        { id: 4, name: "特价" }
      ],
      maxTagLength: 8,
      maxCustomTags: 10
    }
  },

  // API配置
  api: {
    label: "API标签选择",
    key: "api_tags",
    opts: {
      url: "/api/tags/list",
      postData: { type: "product" },
      text_key: "tag_name",
      contrast_key: "tag_id",
      maxTagLength: 10,
      maxCustomTags: 15
    },
    defaultSelected: [1, 3]
  },

  // 必填配置
  required: {
    label: "必填标签",
    key: "required_tags",
    required: true,
    opts: {
      defaultList: [
        { id: 1, name: "分类A" },
        { id: 2, name: "分类B" },
        { id: 3, name: "分类C" }
      ]
    },
    rules: [
      { required: true, message: "请选择至少一个标签" }
    ]
  },

  // 禁用配置
  disabled: {
    label: "禁用标签选择",
    key: "disabled_tags",
    disabled: true,
    opts: {
      defaultList: [
        { id: 1, name: "已选标签1" },
        { id: 2, name: "已选标签2" }
      ]
    }
  },

  // 自定义字段配置
  customFields: {
    label: "自定义字段标签",
    key: "custom_tags",
    opts: {
      text_key: "title",
      contrast_key: "value",
      defaultList: [
        { value: "A", title: "选项A" },
        { value: "B", title: "选项B" },
        { value: "C", title: "选项C" }
      ]
    }
  }
};

// 测试表单数据
export const testForms = {
  basic: { basic_tags: [] },
  api: { api_tags: [] },
  required: { required_tags: [] },
  disabled: { disabled_tags: [1, 2] },
  custom: { custom_tags: [] }
};

// 模拟API响应数据
export const mockApiResponse = {
  code: 200,
  msg: "success",
  data: [
    { tag_id: 1, tag_name: "川菜" },
    { tag_id: 2, tag_name: "粤菜" },
    { tag_id: 3, tag_name: "湘菜" },
    { tag_id: 4, tag_name: "素食" },
    { tag_id: 5, tag_name: "辣味" }
  ]
};

// 测试用例函数
export const testCases = {
  // 测试基础功能
  testBasicFunctionality: (component) => {
    console.log('测试基础功能...');
    
    // 测试预设标签点击
    const presetTag = component.find('.tag-item').first();
    presetTag.trigger('click');
    
    // 验证选中状态
    expect(presetTag.classes()).toContain('tag-selected');
    
    console.log('✓ 基础功能测试通过');
  },

  // 测试自定义标签
  testCustomTags: (component) => {
    console.log('测试自定义标签功能...');
    
    // 测试添加自定义标签
    const input = component.find('.tag-input input');
    const addButton = component.find('.add-button');
    
    input.setValue('自定义标签');
    addButton.trigger('click');
    
    // 验证标签是否添加成功
    const customTags = component.findAll('.tag-custom');
    expect(customTags.length).toBeGreaterThan(0);
    
    console.log('✓ 自定义标签测试通过');
  },

  // 测试数据绑定
  testDataBinding: (component, form) => {
    console.log('测试数据双向绑定...');
    
    // 选中一个标签
    const tag = component.find('.tag-item');
    tag.trigger('click');
    
    // 验证表单数据是否更新
    expect(form.value.length).toBeGreaterThan(0);
    
    console.log('✓ 数据绑定测试通过');
  },

  // 测试表单验证
  testValidation: (component, config) => {
    console.log('测试表单验证...');
    
    if (config.required) {
      // 测试必填验证
      const form = component.find('form');
      form.trigger('submit');
      
      // 验证是否显示错误信息
      const errorMsg = component.find('.van-field__error-message');
      expect(errorMsg.exists()).toBe(true);
    }
    
    console.log('✓ 表单验证测试通过');
  },

  // 测试禁用状态
  testDisabledState: (component, config) => {
    console.log('测试禁用状态...');
    
    if (config.disabled) {
      const tags = component.findAll('.tag-item');
      tags.forEach(tag => {
        tag.trigger('click');
        // 验证点击无效果
        expect(tag.classes()).not.toContain('tag-selected');
      });
    }
    
    console.log('✓ 禁用状态测试通过');
  }
};

// 性能测试
export const performanceTests = {
  // 测试大量标签渲染性能
  testLargeDataSet: () => {
    const largeDataSet = Array.from({ length: 1000 }, (_, i) => ({
      id: i + 1,
      name: `标签${i + 1}`
    }));
    
    const startTime = performance.now();
    
    // 模拟渲染大量标签
    const config = {
      opts: {
        defaultList: largeDataSet
      }
    };
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    console.log(`大数据集渲染时间: ${renderTime}ms`);
    
    // 性能要求：1000个标签渲染时间应小于100ms
    expect(renderTime).toBeLessThan(100);
  },

  // 测试内存泄漏
  testMemoryLeak: () => {
    console.log('测试内存泄漏...');
    
    const initialMemory = performance.memory?.usedJSHeapSize || 0;
    
    // 创建和销毁多个组件实例
    for (let i = 0; i < 100; i++) {
      // 模拟组件创建和销毁
      const component = { destroy: () => {} };
      component.destroy();
    }
    
    // 强制垃圾回收（如果支持）
    if (window.gc) {
      window.gc();
    }
    
    const finalMemory = performance.memory?.usedJSHeapSize || 0;
    const memoryIncrease = finalMemory - initialMemory;
    
    console.log(`内存增长: ${memoryIncrease} bytes`);
    
    // 内存增长应该在合理范围内
    expect(memoryIncrease).toBeLessThan(1024 * 1024); // 小于1MB
  }
};

// 集成测试
export const integrationTests = {
  // 测试与yhc-form集成
  testFormIntegration: () => {
    console.log('测试与yhc-form集成...');
    
    const formConfig = {
      form: [
        {
          label: "商品标签",
          key: "tags",
          component: "yhc-tag-selector",
          opts: {
            defaultList: [
              { id: 1, name: "热门" },
              { id: 2, name: "推荐" }
            ]
          },
          required: true
        }
      ]
    };
    
    // 验证配置正确性
    expect(formConfig.form[0].component).toBe('yhc-tag-selector');
    
    console.log('✓ yhc-form集成测试通过');
  },

  // 测试API集成
  testApiIntegration: async () => {
    console.log('测试API集成...');
    
    // 模拟API调用
    const mockFetch = jest.fn().mockResolvedValue({
      json: () => Promise.resolve(mockApiResponse)
    });
    
    global.fetch = mockFetch;
    
    // 测试API调用
    const response = await fetch('/api/tags/list');
    const data = await response.json();
    
    expect(data.code).toBe(200);
    expect(data.data).toBeInstanceOf(Array);
    
    console.log('✓ API集成测试通过');
  }
};

// 导出所有测试
export default {
  testConfigs,
  testForms,
  mockApiResponse,
  testCases,
  performanceTests,
  integrationTests
};
