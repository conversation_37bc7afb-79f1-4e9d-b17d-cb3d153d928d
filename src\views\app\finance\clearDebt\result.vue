<template>
  <div class="result-container">
    <!-- 蓝色背景区域 -->
    <div class="blue-section">
      <!-- 成功图标 -->
      <div class="success-icon">
        <div class="check-mark">✓</div>
      </div>

      <!-- 标题 -->
      <div class="result-title">清空欠款结果</div>
    </div>

    <!-- 白色背景区域 -->
    <div class="white-section">
      <!-- 白色卡片 -->
      <div class="detail-card">
        <div class="card-title">清空欠款明细</div>

        <!-- 结果列表 -->
        <div class="result-list">
          <div class="list-header">
            <span class="header-name">人员</span>
            <span class="header-result">结果</span>
          </div>

          <template v-for="(item, index) in allResults" :key="index">
            <div class="result-item">
              <span class="item-name">{{ item.user_name }}</span>
              <span
                :class="['item-result', item.success ? 'success' : 'failed']"
              >
                {{ item.message }}
              </span>
            </div>
          </template>
        </div>

        <!-- 展开按钮 -->
        <div class="expand-btn" @click="toggleExpand" v-if="hasMoreItems">
          <van-icon :name="isExpanded ? 'arrow-up' : 'arrow-down'" />
          <span>{{ isExpanded ? '收起' : '展开' }}</span>
        </div>
      </div>

      <!-- 返回按钮 -->
      <div class="back-btn-container">
        <van-button
          type="primary"
          size="large"
          class="back-btn"
          @click="goBack"
        >
          返回
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter,useRoute } from 'vue-router'
import { useLoginStore } from '@/store/dingLogin'

const router = useRouter()
const route = useRoute()
const app = useLoginStore()

// 展开状态
const isExpanded = ref(false)

// 模拟结果数据
const allResults = ref([])

// 显示的结果列表


// 是否有更多项目
const hasMoreItems = computed(() => {
  return allResults.value.length > 5
})

// 切换展开状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

// 页面加载时生成模拟数据
onMounted(() => {
  const results = route.query.results
  console.log('清空欠款结果:', route.query)
  if (results) {
    allResults.value = JSON.parse(results)
  }
  // generateMockResults()
})

// 生成模拟结果数据
// const generateMockResults = () => {
//   const results = []

//   // 添加内部员工数据（模拟）
//   const mockInternalUsers = [
//     { name: '张三', success: true },
//     { name: '李四', success: true },
//     { name: '王五', success: false, errorMsg: '清空失败，账户异常' },
//     { name: '赵六', success: true },
//     { name: '孙七', success: true },
//     { name: '周八', success: false, errorMsg: '清空失败，网络错误' },
//   ]

//   results.push(...mockInternalUsers)

//   // 添加外部人员数据
//   if (app.clearDebt.externalUserList && app.clearDebt.externalUserList.length > 0) {
//     app.clearDebt.externalUserList.forEach((user, index) => {
//       results.push({
//         name: user.name || `外部用户${index + 1}`,
//         success: Math.random() > 0.3, // 外部用户成功率稍高
//         errorMsg: Math.random() > 0.3 ? '' : '清空失败，外部用户验证失败'
//       })
//     })
//   }

//   allResults.value = results
// }

// 返回上一页
const goBack = () => {
  // 清空清空欠款的选中状态
  app.clearDebt.externalUserList = []
  app.clearDebt.visiterList = []
  
  // 返回上一页
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.result-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.blue-section {
  background: #007FFF;
  height: 200px;
  padding: 40px 20px 20px 20px;
  position: relative;
  border-radius: 0 0 25px 25px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .success-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;

    .check-mark {
      width: 60px;
      height: 60px;
      background: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #007FFF;
      font-size: 28px;
      font-weight: bold;
      line-height: 1;
    }

    .van-icon {
      width: 60px;
      height: 60px;
      background: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #007FFF;
      font-size: 24px;
      font-weight: bold;
    }
  }

  .result-title {
    text-align: center;
    color: #fff;
    font-size: 15px;
    font-weight: 500;
  }
}

.white-section {
  flex: 1;
  background: #f5f5f5;
  padding: 0 16px;
  z-index: 2;
}

.detail-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  margin-top: -15px;
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
  }
}

.result-list {
  .list-header {
    width: 100%;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 8px;
    
    .header-name{
      font-size: 15px;
      color: #969799;
      width:30%;
      font-weight: 500;
    }
    .header-result {
      font-size: 15px;
      color: #000000;
      margin-left: 23%;
      width:30%;
      font-weight: 500;
    }
  }
  
  .result-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    
    &:not(:last-child) {
      border-bottom: 1px solid #f7f8fa;
    }
    
    .item-name {
      width:33%;
      display: block;
      font-size: 15px;
      color: #969799;
      // flex: 1;
    }
    
    .item-result {
      font-size: 15px;
      display: block;
      // margin-left: 23%;
      
      &.success {
        color: #07c160;
      }
      
      &.failed {
        color: #ED6A0C;
      }
    }
  }
}

.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  color: #1989fa;
  font-size: 14px;
  cursor: pointer;
  
  .van-icon {
    margin-right: 4px;
    font-size: 16px;
  }
}

.back-btn-container {
  margin-top: 30px;
  padding-bottom: 30px;
  
  .back-btn {
    width: 200px;
    height: 44px;
    border-radius: 22px;
    font-size: 16px;
    font-weight: 500;
    margin: 0 auto;
    display: block;
  }
}
</style>
