<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="detail"
      :editRedirectConfig="editRedirectConfig"
      @onSubmit="onBasicSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const {proxy } = getCurrentInstance();
const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    id: route.query.id // 从路由获取id参数
  },
  curl: {
    info: '/mealtime/get_info', // 获取详情接口
    del: '/mealtime/post_del' // 删除接口
  },
  groupForm: [
    [0, 1],
    [1, 4]
  ],
  form: [
    {
      label: "餐时名称",
      key: "title",
      component: "yhc-input",
      type: "text",
      disabled: true,
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请输入餐时名称" }],
    },
    {
      label: "开始时间",
      key: "start_time",
      component: "yhc-picker-date",
      required: true,
      disabled: true,
      rules: [{ required: true, message: "请选择开始时间" }],
      type: "time",
      typeLabel: "start",
      crossDayConfig: {
        startTimeKey: "start_time",
        endTimeKey: "end_time"
      },
      // 必填            
      // default: true, // 默认值
      // type: "time-short", //time-short 时分 date：日期 datetime：日期时间 time：时分秒
    },
    {
      label: "结束时间",
      key: "end_time",
      component: "yhc-picker-date",
      disabled: true,
      required: true,
      rules: [{ required: true, message: "请选择结束时间" }],
      type: "time",
      typeLabel: "end",
      crossDayConfig: {
        startTimeKey: "start_time",
        endTimeKey: "end_time"
      },
      // 必填            
      // default: true, // 默认值
      // type: "time-short", //time-short 时分 date：日期 datetime：日期时间 time：时分秒
    },
  ]
}

// 修改按钮跳转配置
const editRedirectConfig = {
  path: '/menuTimeAdd', // 跳转到新增页面进行编辑
  query: {
    id: route.query.id, // 传递id参数
    from: 'detail' // 标识来源
  }
}

// 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: '餐时详情',
  });
};
setRightA()
setRight()
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
