# yhc-action-buttons 操作按钮组组件

一个专为菜单管理系统设计的四按钮操作组件，包含同步和删除操作。

## 功能特性

- 🎨 **主题区分**: 蓝色主题用于同步操作，红色主题用于删除操作
- 📱 **响应式设计**: 支持移动端和桌面端适配
- ⚡ **加载状态**: 支持单个按钮的加载状态显示
- 🚫 **禁用状态**: 支持全局禁用所有按钮
- 🎯 **点击反馈**: 提供视觉和触觉反馈

## 基础用法

```vue
<template>
  <yhc-action-buttons @click="handleButtonClick" />
</template>

<script setup>
import YhcActionButtons from '@/components/yhc-action-buttons/index.vue'

const handleButtonClick = (actionType) => {
  console.log('点击的操作类型:', actionType)
  // actionType 可能的值:
  // 'sync_day_to_week' - 同步日菜单至本周
  // 'sync_week_to_month' - 同步周菜单至本月  
  // 'delete_sunday' - 删除周日
  // 'delete_weekend' - 删除周六日
}
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| disabled | 是否禁用所有按钮 | boolean | false |
| loading | 加载状态，可以是具体的操作类型或false | string \| boolean | false |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| click | 按钮点击时触发 | actionType: string |

### 操作类型说明

| 操作类型 | 说明 | 按钮文本 | 主题色 |
|----------|------|----------|--------|
| sync_day_to_week | 同步日菜单至本周 | 同步日菜单<br>至本周 | 蓝色 |
| sync_week_to_month | 同步周菜单至本月 | 同步周菜单<br>至本月 | 蓝色 |
| delete_sunday | 删除周日 | 删除<br>周日 | 红色 |
| delete_weekend | 删除周六日 | 删除<br>周六日 | 红色 |

## 高级用法

### 加载状态

```vue
<template>
  <yhc-action-buttons 
    :loading="currentLoading"
    @click="handleAsyncOperation" 
  />
</template>

<script setup>
import { ref } from 'vue'

const currentLoading = ref(false)

const handleAsyncOperation = async (actionType) => {
  currentLoading.value = actionType
  
  try {
    // 执行异步操作
    await performOperation(actionType)
  } finally {
    currentLoading.value = false
  }
}
</script>
```

### 禁用状态

```vue
<template>
  <yhc-action-buttons 
    :disabled="isProcessing"
    @click="handleButtonClick" 
  />
</template>

<script setup>
import { ref } from 'vue'

const isProcessing = ref(false)
</script>
```

## 样式定制

组件使用 SCSS 编写，支持响应式设计：

- **移动端** (≤375px): 紧凑布局，较小的按钮尺寸
- **桌面端** (≥768px): 宽松布局，较大的按钮尺寸  
- **横屏模式**: 适配低高度屏幕的特殊布局

## 注意事项

1. 组件依赖 Vant4 的 `van-loading` 组件
2. 按钮文本使用换行符分割，确保在小屏幕上的可读性
3. 同步操作使用蓝色主题，删除操作使用红色主题，符合用户直觉
4. 加载状态下会显示对应按钮的加载动画，其他按钮保持可用状态
