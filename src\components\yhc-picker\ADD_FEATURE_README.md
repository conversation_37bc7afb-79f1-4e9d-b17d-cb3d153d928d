# yhc-picker 组件添加功能说明

## 概述

yhc-picker 组件现在支持可配置的"添加"按钮功能，允许用户在选择器界面中直接添加新的选项。

## 功能特性

- ✅ 支持单选和多选模式
- ✅ 可配置添加按钮的显示位置（顶部/底部）
- ✅ 支持动态表单字段配置
- ✅ 支持接口调用添加数据
- ✅ 添加成功后自动刷新选项列表
- ✅ 支持 keyMap 配置映射
- ✅ 与现有功能完全兼容
- ✅ 优化的交互体验：添加弹窗在中间显示，添加完成后自动返回选择器

## 交互流程

1. **点击选择器字段** → 打开选择器弹窗（底部）
2. **点击添加按钮** → 选择器弹窗关闭，添加弹窗在中间打开
3. **填写表单并确认** → 添加弹窗关闭，选择器弹窗重新打开
4. **新数据显示** → 新添加的数据出现在选项列表中

## 配置说明

### 1. 添加按钮配置 (addButton)

```javascript
addButton: {
  show: true,           // 是否显示添加按钮
  text: "添加",         // 按钮文本
  icon: "plus",         // 按钮图标
  position: "bottom",   // 按钮位置：top, bottom
}
```

### 2. 添加表单配置 (addForm)

```javascript
addForm: {
  title: "添加项目",    // 弹窗标题
  form: [               // 表单字段配置数组
    {
      label: "名称",
      key: "name",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入名称",
      required: true,
      rules: [{ required: true, message: "请填写名称" }]
    },
    {
      label: "描述",
      key: "description",
      component: "yhc-input",
      type: "textarea",
      placeholder: "请输入描述",
      required: false
    }
  ]
}
```

### 3. 添加接口配置 (addApi)

```javascript
addApi: {
  url: "/api/add",      // 添加接口URL
  method: "POST",       // 请求方法
  postData: {           // 额外的请求参数
    type: "custom",
    status: "active"
  }
}
```

## 支持的表单组件类型

- `yhc-input` / `input` - 输入框
  - 支持 type: text, textarea, email, number 等
- `yhc-picker` / `select` - 选择器
  - 需要配置 options 数组

## 使用示例

### 单选模式示例

```javascript
{
  label: "选择部门",
  key: "department",
  component: "yhc-picker",
  opts: {
    multiple: false,
    text_key: "name",
    contrast_key: "id",
    defaultList: [
      { id: 1, name: "技术部" },
      { id: 2, name: "市场部" }
    ]
  },
  addButton: {
    show: true,
    text: "添加部门",
    icon: "plus",
    position: "bottom"
  },
  addForm: {
    title: "添加新部门",
    form: [
      {
        label: "部门名称",
        key: "name",
        component: "yhc-input",
        type: "text",
        placeholder: "请输入部门名称",
        required: true,
        rules: [{ required: true, message: "请填写部门名称" }]
      }
    ]
  },
  addApi: {
    url: "/api/department/add",
    method: "POST",
    postData: {}
  }
}
```

### 多选模式示例

```javascript
{
  label: "选择员工",
  key: "employees",
  component: "yhc-picker",
  opts: {
    multiple: true,
    text_key: "name",
    contrast_key: "id",
    maxlength: 20
  },
  addButton: {
    show: true,
    text: "添加员工",
    position: "top"
  },
  addForm: {
    title: "添加新员工",
    form: [
      {
        label: "员工姓名",
        key: "name",
        component: "yhc-input",
        required: true
      },
      {
        label: "职位",
        key: "position",
        component: "yhc-input",
        required: true
      }
    ]
  },
  addApi: {
    url: "/api/employee/add",
    method: "POST"
  }
}
```

## 事件说明

组件新增了 `onRefresh` 事件，当使用 defaultList 且添加成功后会触发此事件：

```javascript
<yhc-picker 
  :config="config" 
  :form="form" 
  @onConfirm="handleConfirm"
  @onRefresh="handleRefresh"
/>
```

## 接口返回格式

添加接口应返回标准格式：

```javascript
{
  code: 200,
  msg: "添加成功",
  data: {
    // 新添加的数据项
    id: 5,
    name: "新部门",
    // ... 其他字段
  }
}
```

## 注意事项

1. 添加功能需要配置 `addButton.show: true` 才会显示
2. 表单字段的 `key` 应与接口期望的字段名一致
3. 添加成功后会自动刷新列表数据
4. 支持与现有的 keyMap 配置配合使用
5. 表单验证规则与 yhc-form 组件保持一致

## 测试页面

可以查看 `src/views/app/test/yhc-picker-add-test.vue` 文件中的完整测试示例。
