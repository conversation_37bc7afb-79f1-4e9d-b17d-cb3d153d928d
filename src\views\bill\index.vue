<template>
  <div class="wrapper-bill">
    <yhc-list :config="listConfig" :key="listConfig.key || 'default'" ref="listRef">
      <template #default="{ item}">
        <div class="bill-item" @click="onButClickdetail(item)">
          <div class="item-top">
            <span style="font-size: 15px;font-weight: normal;color: #323233;line-height: 18px;">{{
              item.window_title
                ? `${item.dininghall_title}--${item.window_title}`
                : `${item.dininghall_title}`
            }}</span>
            <span :style="`color:${statusColor[item.order_status]}`" style="font-size:12px;">{{
              statusLs[item.order_status]
            }}</span>
          </div>
          <div class="dish-block">
             <div class="dish-left" v-if="item.dishess && item.dishess.length > 1">
               <div
                 class="dish-item-wrapper"
                 v-for="dish in item.dishess"
                 :key="dish.id"
               >
               <div
                 style="
                   width: 54px;
                   height: 54px;
                   border-radius: 8px;
                   background: #007fff;
                   color: #fff;
                   font-size: 25px;
                   display: flex;
                   align-items: center;
                   justify-content: center;
                 "
                 v-if="dish.image===''"
               >
               {{
                dish.title[0]
                 }}
               </div>
                 <van-image
                   width="54"
                   height="54"
                   radius="8"
                   v-else
                   :src="dish.image"
                 />
                 <div style="height: 5px;" v-if="dish.image===''"></div>
                 <div class="dish-name">{{ dish.title }}</div>
               </div>
             </div>
             <div class="dish-left one-dish" v-else>
               <van-image
                 width="54"
                 height="54"
                 radius="8"
                 :src="item.dishess[0] && item.dishess[0].image"
                 v-if="item.dishess[0] && item.dishess[0].image"
               />
               <div
                 v-else
                 style="
                   width: 54px;
                   height: 54px;
                   border-radius: 8px;
                   background: #007fff;
                   color: #fff;
                   font-size: 25px;
                   display: flex;
                   align-items: center;
                   justify-content: center;
                   margin-right: 8px;
                 "
               >
                 {{
                   (item.dishess[0] ? item.dishess[0].title : item.repast_title)[0]
                 }}
               </div>
               <span style="font-size: 15px;color: #323233;font-weight: 500;">{{
                 item.dishess[0] ? item.dishess[0].title : item.repast_title
               }}</span>
             </div>
             <div class="dish-right">
               <div style="overflow: scroll">
                 <span style="font-size: 15px; color: #000">￥</span
                 ><span style="font-size: 15px; color: #000">{{
                   item.dishess && item.dishess.length
                     ? item.dishess
                         .reduce(
                           (pre, cur) => cur.price * 1 * (cur.quantity * 1) + pre,
                           0
                         )
                         .toFixed(2)
                     : item.total_amount
                 }}</span>
               </div>
               <div>共{{ item.dishess.length }}件</div>
             </div>
           </div>
          <!-- <div class="button-block" >
             <span style="font-size: 14px; color: #323233;">{{ item.created_at }}</span>
   
             <van-button v-show="item.status === 3" type="default" size="small" @click.stop="onButClick(item)"
               >评价</van-button
             >
           </div> -->
        </div>
      </template>
    </yhc-list>
  </div>
</template>
<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter();
// 骨架屏配置
const skeletonConfig = reactive({
  isShow: false,
  count: 3,
  row: 2,
  rowWidth: ['100%', '60%', '80%'],
  avatar: true,
  avatarSize: '40px',
  avatarShape: 'round',
  title: true,
  titleWidth: '50%',
  duration: 500
})
// 列表组件引用
const listRef = ref(null)
const listConfig = reactive({
  curl: {
    ls: "/order/get_ls",
  },
  postData: {
    dininghall_id: localStorage.getItem("dininghall"),
  },
  search: {
    isShow: false,
    isShowPopup: false
  },
  tabs: {
    isShow: true,
    sticky: true,
    swipeable: false,
    list: [
      {
        text: "全部订单",
        order_status: null,
      },
      {
        text: "待付款",
        order_status: 0,
      },
      {
        text: "待使用",
        order_status: 1,
      },
      {
        text: "待评价",
        order_status: 2,
      },
      {
        text: "退款/取消",
        order_status: 5,
      },
    ],
  },
  button: {
    isShow: false,
  },
  skeleton: skeletonConfig,
  // 模拟数据格式化
  format: (data) => {
    data.forEach((element) => {
      element.dishess = JSON.parse(element.order_items);
    });
  },
  // 添加模拟数据标识
  mockData: false
})
let statusLs = ["待付款", "待使用", "待评价", "已完成", "已取消", "退款成功"];
let statusColor = {
  2: "#969799",
  3: "#969799",
  5: "#FAAB0C",
};
// const onButClick = (item) => {
//   console.log("评价跳转事件", item);
//   router.push({ path: "/bill_evaluate", query: { bill_id: item.id } });
// };
const onButClickdetail = (item) => {
  router.push({ path: "/billdetail", query: { id: item.id } });
};
</script>
<style lang="scss" scoped>
.wrapper-bill {
  width: 100%;
  min-height: 100vh;

  .bill-item {
    margin: 16px;
    background: #fff;
    border-radius: 4px;
    padding: 16px;
    color: rgba(23, 26, 29, 0.6);

    .item-top {
      display: flex;
      justify-content: space-between;
      font-size: 15px;
      margin-bottom: 8px;
    }

    .dish-block {
      display: flex;
      justify-content: space-between;
      padding: 20px 0 7px;
      border-top: 1px solid #f6f6f6;

      .dish-left {
        flex: 1;
        overscroll-behavior: contain;
        overflow: scroll;
        white-space: nowrap;
        display: flex;
        align-items: flex-start;

        .dish-item-wrapper {
          display: inline-flex;
          flex-direction: column;
          align-items: center;
          // margin-right: 8px;
          min-width: 66px;

          .van-image {
            margin-bottom: 4px;
          }



          .dish-name {
            // padding-top: 5px;
            // padding-bottom: -50px;
            // margin-top: 5px;
            font-size: 12px;
            color: #646566;
            text-align: center;
            line-height: 16px;
            max-width: 66px;
            word-wrap: break-word;
            white-space: normal;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }

      .one-dish {
        display: flex;
        align-items: center;

        .van-image {
          margin-right: 8px;
        }
      }

      .dish-right {
        width: 60px;
        font-size: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: right;

        div {
          width: 100%;
        }
      }
    }

    // .button-block {
    //   display: flex;
    //   align-items: center;
    //   justify-content: space-between;
    //   padding-top: 16px;
    //   border-top: 1px solid #f6f6f6;
    // }
  }
}
</style>
