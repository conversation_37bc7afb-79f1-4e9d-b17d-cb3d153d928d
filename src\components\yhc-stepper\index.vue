<template>
  <div class="wrapper">
    <van-cell center :title="config.label" :required="config.required"
      :title-style="`color:${config.disabled ? '#c8c9cc' : ''}`">
      <template #right-icon>
        <van-stepper v-model="props.form[config.key]" :name="config.key" :min="config.min" :max="config.max"
          :step="config.step" :integer="config.integer" :disabled="config.disabled" :disabled-plus="config.disabledPlus"
          :disabled-minus="config.disabledMinus" :disabled-input="config.disabledInput" :show-plus="config.showPlus"
          :show-minus="config.showMinus" :show-input="config.showInput" :input-width="config.inputWidth"
          :button-size="config.buttonSize" :placeholder="config.placeholder" :decimal-length="config.decimalLength"
          :theme="config.theme" :allow-empty="config.allowEmpty" @change="onChange" @overlimit="onOverlimit"
          @plus="onPlus" @minus="onMinus" @focus="onFocus" @blur="onBlur">
          <!-- 减号按钮插槽 -->
          <template #minus v-if="$slots.minus">
            <slot name="minus"></slot>
          </template>

          <!-- 加号按钮插槽 -->
          <template #plus v-if="$slots.plus">
            <slot name="plus"></slot>
          </template>
        </van-stepper>
      </template>
    </van-cell>
  </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, watch } from "vue";
import { deepAssign } from "@/untils";
import { showToast } from "vant";

const { proxy } = getCurrentInstance();
let config = {
  // 基础配置
  label: "步进器",         // 字段标签 (字符串) - 显示在步进器左侧的标签文字
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"quantity", "count"
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用步进器 (布尔值) - true: 禁用整个步进器, false: 可正常操作
  rules: [],               // 验证规则 (数组) - 表单验证规则配置

  // 数值配置
  min: 1,                  // 最小值 (数字) - 允许输入的最小数值
  max: Infinity,           // 最大值 (数字) - 允许输入的最大数值，Infinity表示无限制
  step: 1,                 // 步长 (数字) - 每次点击增减按钮的变化量
  default: 1,              // 默认值 (数字) - 组件的初始值
  integer: false,          // 是否只允许输入整数 (布尔值) - true: 仅整数, false: 允许小数
  decimalLength: null,     // 固定显示的小数位数 (数字) - 小数位数，null表示不固定
  allowEmpty: false,       // 是否允许输入的值为空 (布尔值) - true: 允许空值, false: 不允许空值

  // 按钮控制配置
  disabledPlus: false,     // 是否禁用增加按钮 (布尔值) - true: 禁用+按钮, false: 可正常使用
  disabledMinus: false,    // 是否禁用减少按钮 (布尔值) - true: 禁用-按钮, false: 可正常使用
  disabledInput: false,    // 是否禁用输入框 (布尔值) - true: 禁用输入框, false: 可正常输入
  showPlus: true,          // 是否显示增加按钮 (布尔值) - true: 显示+按钮, false: 隐藏
  showMinus: true,         // 是否显示减少按钮 (布尔值) - true: 显示-按钮, false: 隐藏
  showInput: true,         // 是否显示输入框 (布尔值) - true: 显示输入框, false: 隐藏

  // 样式配置
  inputWidth: "32px",      // 输入框宽度 (字符串) - 输入框的宽度，如"32px", "50px"
  buttonSize: "28px",      // 按钮大小 (字符串) - 增减按钮的尺寸，如"28px", "32px"
  placeholder: "",         // 输入框占位提示文字 (字符串) - 输入框为空时显示的提示文字
  theme: "default",        // 样式风格 (字符串) - "default": 默认样式, "round": 圆角样式
};

// 定义props
const props = defineProps({
  config: Object,
  form: Object,
});

// 合并配置
props.config && deepAssign(config, props.config);

// 定义事件
const emit = defineEmits([
  "change",      // 当绑定值变化时触发的事件
  "overlimit",   // 点击不可用的按钮时触发
  "plus",        // 点击增加按钮时触发
  "minus",       // 点击减少按钮时触发
  "focus",       // 输入框聚焦时触发
  "blur"         // 输入框失焦时触发
]);

// 初始化表单值
if (props.form[config.key] === undefined || props.form[config.key] === null || props.form[config.key] === '') {
  props.form[config.key] = config.default;
}

// 数值变化事件
const onChange = (value) => {
  // console.log("yhc-stepper数值变化---->", value, props.form);
  emit("change", {
    components: "yhc-stepper",
    key: config.key,
    value: value,
    form: props.form
  });
};

// 超出限制事件
const onOverlimit = (action) => {
  // console.log("yhc-stepper超出限制---->", action);
  emit("overlimit", {
    components: "yhc-stepper",
    key: config.key,
    action: action, // 'plus' 或 'minus'
    form: props.form
  });

  // 显示提示信息
  if (action === 'plus') {
    showToast(`不能超过最大值 ${config.max}`);
  } else if (action === 'minus') {
    showToast(`不能小于最小值 ${config.min}`);
  }
};

// 点击增加按钮事件
const onPlus = () => {
  // console.log("yhc-stepper点击增加按钮---->", props.form[config.key]);
  emit("plus", {
    components: "yhc-stepper",
    key: config.key,
    value: props.form[config.key],
    form: props.form
  });
};

// 点击减少按钮事件
const onMinus = () => {
  // console.log("yhc-stepper点击减少按钮---->", props.form[config.key]);
  emit("minus", {
    components: "yhc-stepper",
    key: config.key,
    value: props.form[config.key],
    form: props.form
  });
};

// 输入框聚焦事件
const onFocus = (event) => {
  // console.log("yhc-stepper输入框聚焦---->", event);
  emit("focus", {
    components: "yhc-stepper",
    key: config.key,
    event: event,
    form: props.form
  });
};

// 输入框失焦事件
const onBlur = (event) => {
  // console.log("yhc-stepper输入框失焦---->", event);
  emit("blur", {
    components: "yhc-stepper",
    key: config.key,
    event: event,
    form: props.form
  });
};
</script>

<style lang="scss" scoped>
.wrapper {

  // 可以在这里添加自定义样式
  .van-cell {
    font-size: 16px;
    padding: 16px;
  }
}
</style>
