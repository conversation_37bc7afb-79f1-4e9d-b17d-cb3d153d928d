/**
 * 跨天时间选择配置工具函数
 * 用于快速生成yhc-picker-date组件的跨天时间配置
 */

import { createCrossDayValidator } from './timeValidation.js';

/**
 * 创建跨天时间配置
 * @param {string} startTimeKey - 开始时间字段名
 * @param {string} endTimeKey - 结束时间字段名
 * @param {object} options - 配置选项
 * @returns {object} 包含开始时间和结束时间配置的对象
 */
export function createCrossDayTimeConfig(startTimeKey, endTimeKey, options = {}) {
  const {
    timeType = 'time-short',
    allowCrossDay = true,
    minDuration = 0,
    maxDuration = 24 * 60,
    startTimeLabel = '开始时间',
    endTimeLabel = '结束时间',
    startTimePlaceholder = '请选择开始时间',
    endTimePlaceholder = '请选择结束时间',
    required = true,
    messages = {}
  } = options;

  // 创建跨天验证器
  const validator = createCrossDayValidator(startTimeKey, endTimeKey, {
    timeType,
    allowCrossDay,
    minDuration,
    maxDuration,
    messages
  });

  // 基础跨天配置
  const crossDayConfig = {
    startTimeKey,
    endTimeKey
  };

  // 开始时间配置
  const startTimeConfig = {
    label: startTimeLabel,
    key: startTimeKey,
    component: "yhc-picker-date",
    type: timeType,
    required,
    typeLabel: "start",
    placeholder: startTimePlaceholder,
    crossDayConfig,
    rules: [
      { required, message: `请选择${startTimeLabel}` },
      { validator }
    ]
  };

  // 结束时间配置
  const endTimeConfig = {
    label: endTimeLabel,
    key: endTimeKey,
    component: "yhc-picker-date",
    type: timeType,
    required,
    typeLabel: "end",
    placeholder: endTimePlaceholder,
    crossDayConfig,
    rules: [
      { required, message: `请选择${endTimeLabel}` },
      { validator }
    ]
  };

  return {
    startTimeConfig,
    endTimeConfig
  };
}

/**
 * 创建简单的跨天时间配置（使用默认设置）
 * @param {string} startTimeKey - 开始时间字段名
 * @param {string} endTimeKey - 结束时间字段名
 * @returns {object} 包含开始时间和结束时间配置的对象
 */
export function createSimpleCrossDayConfig(startTimeKey, endTimeKey) {
  return createCrossDayTimeConfig(startTimeKey, endTimeKey, {
    timeType: 'time-short',
    allowCrossDay: true,
    minDuration: 0,
    maxDuration: 24 * 60
  });
}

/**
 * 创建限制跨天的时间配置
 * @param {string} startTimeKey - 开始时间字段名
 * @param {string} endTimeKey - 结束时间字段名
 * @param {object} options - 配置选项
 * @returns {object} 包含开始时间和结束时间配置的对象
 */
export function createSameDayTimeConfig(startTimeKey, endTimeKey, options = {}) {
  return createCrossDayTimeConfig(startTimeKey, endTimeKey, {
    ...options,
    allowCrossDay: false
  });
}

/**
 * 创建带时间限制的跨天配置
 * @param {string} startTimeKey - 开始时间字段名
 * @param {string} endTimeKey - 结束时间字段名
 * @param {number} minHours - 最小持续小时数
 * @param {number} maxHours - 最大持续小时数
 * @param {object} options - 其他配置选项
 * @returns {object} 包含开始时间和结束时间配置的对象
 */
export function createTimeLimitedCrossDayConfig(startTimeKey, endTimeKey, minHours = 0, maxHours = 24, options = {}) {
  return createCrossDayTimeConfig(startTimeKey, endTimeKey, {
    ...options,
    minDuration: minHours * 60,
    maxDuration: maxHours * 60,
    messages: {
      minDuration: `持续时间不能少于${minHours}小时`,
      maxDuration: `持续时间不能超过${maxHours}小时`,
      ...options.messages
    }
  });
}

/**
 * 为现有配置添加跨天支持
 * @param {object} startConfig - 开始时间配置对象
 * @param {object} endConfig - 结束时间配置对象
 * @param {object} options - 跨天配置选项
 * @returns {object} 更新后的配置对象
 */
export function addCrossDaySupport(startConfig, endConfig, options = {}) {
  const {
    timeType = 'time-short',
    allowCrossDay = true,
    minDuration = 0,
    maxDuration = 24 * 60,
    messages = {}
  } = options;

  const startTimeKey = startConfig.key;
  const endTimeKey = endConfig.key;

  // 创建验证器
  const validator = createCrossDayValidator(startTimeKey, endTimeKey, {
    timeType,
    allowCrossDay,
    minDuration,
    maxDuration,
    messages
  });

  // 跨天配置对象
  const crossDayConfig = {
    startTimeKey,
    endTimeKey
  };

  // 更新开始时间配置
  const updatedStartConfig = {
    ...startConfig,
    type: timeType,
    typeLabel: "start",
    crossDayConfig,
    rules: [
      ...(startConfig.rules || []),
      { validator }
    ]
  };

  // 更新结束时间配置
  const updatedEndConfig = {
    ...endConfig,
    type: timeType,
    typeLabel: "end",
    crossDayConfig,
    rules: [
      ...(endConfig.rules || []),
      { validator }
    ]
  };

  return {
    startTimeConfig: updatedStartConfig,
    endTimeConfig: updatedEndConfig
  };
}

/**
 * 预设配置模板
 */
export const CROSS_DAY_PRESETS = {
  // 工作时间配置（8小时工作制，允许跨天）
  WORK_SHIFT: {
    timeType: 'time-short',
    allowCrossDay: true,
    minDuration: 4 * 60, // 最少4小时
    maxDuration: 12 * 60, // 最多12小时
    messages: {
      minDuration: '工作时间不能少于4小时',
      maxDuration: '工作时间不能超过12小时'
    }
  },
  
  // 营业时间配置（同一天内）
  BUSINESS_HOURS: {
    timeType: 'time-short',
    allowCrossDay: false,
    minDuration: 1 * 60, // 最少1小时
    maxDuration: 16 * 60, // 最多16小时
    messages: {
      minDuration: '营业时间不能少于1小时',
      maxDuration: '营业时间不能超过16小时',
      crossDayNotAllowed: '营业时间不能跨天'
    }
  },
  
  // 夜班时间配置（支持跨天）
  NIGHT_SHIFT: {
    timeType: 'time-short',
    allowCrossDay: true,
    minDuration: 6 * 60, // 最少6小时
    maxDuration: 10 * 60, // 最多10小时
    messages: {
      minDuration: '夜班时间不能少于6小时',
      maxDuration: '夜班时间不能超过10小时'
    }
  },
  
  // 灵活时间配置（无限制）
  FLEXIBLE: {
    timeType: 'time-short',
    allowCrossDay: true,
    minDuration: 0,
    maxDuration: 24 * 60
  }
};

/**
 * 使用预设配置创建跨天时间配置
 * @param {string} startTimeKey - 开始时间字段名
 * @param {string} endTimeKey - 结束时间字段名
 * @param {string} presetName - 预设配置名称
 * @param {object} overrides - 覆盖配置
 * @returns {object} 包含开始时间和结束时间配置的对象
 */
export function createCrossDayConfigFromPreset(startTimeKey, endTimeKey, presetName, overrides = {}) {
  const preset = CROSS_DAY_PRESETS[presetName];
  if (!preset) {
    throw new Error(`未找到预设配置: ${presetName}`);
  }
  
  return createCrossDayTimeConfig(startTimeKey, endTimeKey, {
    ...preset,
    ...overrides
  });
}
