// 通用条件表单样式
.conditional-form {
  animation: slideDown 0.3s ease-out;

  .van-field {
    margin-top: 0;
    padding: 16px;
    font-size: 16px;
  }
}

// 确保所有 van-cell-group inset 都有正确的间距
:deep(.van-cell-group--inset) {
  margin: 16px 0;

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 单选框组样式
:deep(.van-radio-group--horizontal) {
  display: flex;
  justify-content: end;
  width: 100%;

  .van-radio {
    flex: none;
    margin-right: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

:deep(.van-radio__label) {
  color: #323233;
  margin-left: 8px;
}

:deep(.van-radio__icon--checked) {
  color: var(--van-primary-color);
}

// 餐时列表样式
.meal-times-list {
  .meal-time-item {
    :deep(.van-field) {
      margin-top: 0;
    }
  }
}

// 步进器字段样式
:deep(.van-field) {
  .van-stepper {
    margin-left: auto;
  }
}

// 提前天数说明文字样式
.advance-days-description {
  padding: 6px 16px 20px 16px;
  font-size: 12px;
  color: #969799;
  line-height: 1.4;
  background: #f7f8fa;
  margin: -8px 0;
  border-radius: 0 0 8px 8px;
}

// 动画
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 预定截止时间样式 - 继承通用样式
.unified-deadline-form,
.separate-deadline-form {
  @extend .conditional-form;
}

// 复选框组样式
:deep(.van-checkbox-group--horizontal) {
  display: flex;
  flex-wrap: nowrap;
  justify-content: end;
  // width: 100%;

  .van-checkbox {
    flex: none;
    // margin-right: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

:deep(.van-checkbox__label) {
  color: #323233;
  margin-left: 8px;
}

:deep(.van-checkbox__icon--checked) {
  color: var(--van-primary-color);
}

// 禁用状态样式
:deep(.van-checkbox--disabled) {
  .van-checkbox__icon--checked,
  .van-checkbox__label {
    color: #c8c9cc;
  }
}
