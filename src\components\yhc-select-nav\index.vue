<template>
  <div class="wrapper-select-nav">
    <van-field v-model="data.value" is-link :name="config.key" :label="config.label" :type="config.type"
      :placeholder="config.placeholder + config.label" :required="config.required" :rules="config.rules"
      :disabled="config.disabled" readonly input-align="right" @click="onClick" />
  </div>
</template>
<script setup>
import {
  reactive,
  ref,
  getCurrentInstance,
  watch,
  triggerRef,
  nextTick,
  shallowReactive,
  onMounted,
} from "vue";
import { deepAssign, handleFormData } from "@/untils";
import { showToast } from "vant";
import { useLoginStore } from "@/store/dingLogin";
import { useRouter } from "vue-router";

const app = useLoginStore();
const { proxy } = getCurrentInstance();
const router = useRouter();
let config = {
  // 基础配置
  label: "导航组件",       // 字段标签 (字符串) - 显示在导航组件左侧的标签文字
  type: "text",            // 字段类型 (字符串) - 表单项类型，通常为"text"
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，为空时跳过数据写入
  placeholder: "请选择",    // 占位符 (字符串) - 未选择时显示的提示文字
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用点击, false: 可正常点击
  rules: [],               // 验证规则 (数组) - 表单验证规则配置

  // 导航配置
  routeUrl: "/dishFoodList", // 跳转路由地址 (字符串) - 点击时跳转的页面路径
  routeQuery: {},          // 路由查询参数 (对象) - 跳转时携带的查询参数，如: {type: 'select', mode: 'multi'}

  // 数据处理配置
  dataKey: "",             // 数据存储键名 (字符串) - 用于在store中存储数据的键名
  returnDataKey: "",       // 返回数据键名 (字符串) - 从目标页面返回时获取数据的键名
};
const props = defineProps({
  config: Object,
  form: Object,
});
props.config && deepAssign(config, props.config);
let emit = defineEmits(["onConfirm"]);

// 检查是否需要跳过数据写入：当key属性存在且有值时跳过
const shouldSkipDataWrite = config.key && config.key.trim() !== "";

const data = shallowReactive({
  value: "",
  selectList: null,
});

// 如果需要跳过数据写入，创建一个内部存储，不直接污染form
let internalData = [];

const onClick = () => {
  app.dishFoodListFrom = props.form;
  app.dishFoodList = data.selectList;

  // 检查是否为只读模式跳转（从detail页面跳转）
  const isReadonlyMode = props.form && props.form._isDetailPage;

  if (isReadonlyMode) {
    // 只读模式：传递readonly参数
    router.push({
      path: config.routeUrl,
      query: { readonly: 'true', from: 'detail' }
    });
  } else {
    // 检查是否有配置的路由参数
    const routeQuery = config.routeQuery || {};
    console.log('yhc-select-nav onClick routeQuery:', routeQuery);

    if (Object.keys(routeQuery).length > 0) {
      // 有配置参数时，使用对象形式跳转
      console.log('yhc-select-nav 跳转参数:', { path: config.routeUrl, query: routeQuery });
      router.push({
        path: config.routeUrl,
        query: routeQuery
      });
    } else {
      // 正常模式，直接跳转
      console.log('yhc-select-nav 直接跳转:', config.routeUrl);
      router.push(config.routeUrl);
    }
  }
};

watch(
  () => data.selectList,
  (v, o) => {
    if (shouldSkipDataWrite) {
      // 当配置了key属性时，只存储在内部变量中，不写入form
      internalData = [...v];
      data.value = internalData.length ?
        internalData.map((el) => el.title).slice(0, 3) + ".." : '';
      console.log(`yhc-select-nav: ${config.key}字段数据已更新，仅存储在内部，不写入form:`, internalData);
    } else {
      // 没有配置key属性时正常处理
      props.form[config.key] = [...v];
      data.value = props.form[config.key].length ?
        props.form[config.key].map((el) => el.title).slice(0, 3) + ".." : '';
    }
  }
);

const init = () => {
  if (app.dishFoodListFrom) {
    if (shouldSkipDataWrite) {
      // 当配置了key属性时，特殊处理从其他页面传回的数据
      const formDataCopy = { ...app.dishFoodListFrom };
      // 保存key字段数据到内部存储
      if (formDataCopy[config.key]) {
        internalData = Array.isArray(formDataCopy[config.key]) ? [...formDataCopy[config.key]] : [];
      }
      // 从副本中删除key字段，避免污染表单数据
      delete formDataCopy[config.key];
      Object.assign(props.form, formDataCopy);
      console.log(`yhc-select-nav: ${config.key}字段初始化，内部存储:`, internalData);
    } else {
      Object.assign(props.form, app.dishFoodListFrom);
    }
    app.dishFoodListFrom = null;
    console.log("初始化-----1----？", props.form);
  }
  data.selectList = app.dishFoodList || new Set();
  app.dishFoodList = null;
  if (!data.selectList.size && props.form[config.key]) {
    props.form[config.key].forEach((el) => {
      data.selectList.add(el);
    });
  }
};

// 组件挂载后，如果配置了key属性，设置表单数据过滤逻辑
onMounted(() => {
  if (shouldSkipDataWrite) {
    // 重写form对象的序列化行为
    const originalForm = props.form;

    // 创建一个代理对象来拦截对指定key字段的访问
    const formProxy = new Proxy(originalForm, {
      get (target, prop) {
        if (prop === config.key) {
          // 当访问指定key字段时，返回内部存储的数据用于显示
          return internalData;
        }
        return target[prop];
      },
      set (target, prop, value) {
        if (prop === config.key) {
          // 当设置指定key字段时，存储到内部变量，不污染原始form
          internalData = Array.isArray(value) ? [...value] : [];
          console.log(`yhc-select-nav: 拦截${config.key}字段设置，存储到内部:`, internalData);
          return true;
        }
        target[prop] = value;
        return true;
      },
      ownKeys (target) {
        // 在序列化时，排除指定key字段
        const keys = Object.keys(target).filter(key => key !== config.key);
        console.log(`yhc-select-nav: 表单序列化时过滤${config.key}字段，剩余字段:`, keys);
        return keys;
      },
      has (target, prop) {
        if (prop === config.key) {
          // 在序列化检查时，声明指定key字段不存在
          return false;
        }
        return prop in target;
      },
      getOwnPropertyDescriptor (target, prop) {
        if (prop === config.key) {
          // 在序列化时，指定key字段不可枚举
          return undefined;
        }
        return Object.getOwnPropertyDescriptor(target, prop);
      }
    });

    console.log(`yhc-select-nav: ${config.key}字段代理已设置，表单提交时将自动过滤${config.key}字段`);
  }
});

init();
</script>
<style lang="scss">
.wrapper {
  .van-field {
    font-size: 16px;
  }
}
</style>
