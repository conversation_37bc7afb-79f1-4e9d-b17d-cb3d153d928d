<template>
  <div class="result-container">
    <!-- 蓝色背景区域 -->
    <div class="blue-section">
      <!-- 成功图标 -->
      <div class="success-icon">
        <div class="check-mark">✓</div>
      </div>

      <!-- 标题 -->
      <div class="result-title">{{ detail.transaction_typeName }}</div>
      <div style="margin-top: 16px;color: #fff;font-size: 22px;font-weight: 500;">{{ detail.price }}</div>
    </div>

    <!-- 白色背景区域 -->
    <div class="white-section">
      <!-- 白色卡片 -->
      <div class="detail-card">
        <div class="card-title">交易明细</div>

        <!-- 结果列表 -->
        <div class="result-list">
          <div class="list-header">
            <span class="header-name">人员</span>
            <span class="header-result">{{ detail.user_name }}</span>
          </div>
          <div class="list-header">
            <span class="header-name">部门</span>
            <span class="header-result">{{ detail.dept_name }}</span>
          </div>
          <div class="list-header">
            <span class="header-name">时间</span>
            <span class="header-result">{{ detail.transaction_time }}</span>
          </div>
          <div class="list-header">
            <span class="header-name">备注</span>
            <span class="header-result">{{ detail.remark }}</span>
          </div>
          <div class="list-header">
            <span class="header-name">业务订单号</span>
            <span class="header-result">{{ detail.business_order_no }}</span>
          </div>
          <div class="list-header">
            <span class="header-name">交易流水号</span>
            <span class="header-result">{{ detail.transaction_no }}</span>
          </div>

        </div>

      </div>


    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const { proxy } = getCurrentInstance()
console.log(route, 888)
let detail = ref({})
function getdetail() {
  // 获取路由参数
  const id = route.query.id
  console.log(id, 'type')
  proxy.$get('/transaction_flow/get_info', { id }).then((res) => {
    if(res.code === 200) {
      // 设置页面标题
      detail.value = res.data
      changeTypeTags.forEach((item) => {
        if (item.type === res.data.transaction_type) {
          detail.value.transaction_typeName = item.label
          detail.value.price= item.value + res.data.price
        }
      })
    } else {
     detail.value = {}
    }
  }).catch((error) => {
    console.error('获取详情失败:', error)
    detail.value = {}
  })
}
getdetail()


const changeTypeTags = [
  { label: '补贴发放', value: '+',type:1, },
  { label: '补贴扣除', value: '-',type:2  },
  { label: '支付宝充值', value: '+',type:3  },
  { label: '支付宝提现', value: '-' ,type:4 },
  { label: '微信充值', value: '+',type:5  },
  { label: '微信提现', value: '-' ,type:6 },
  { label: '转账收款', value: '+',type:10  },
  { label: '转账扣款', value: '-',type:9  },
  { label: '消费支出', value: '-',type:7  },
  { label: '退款收入', value: '+',type:8  }
]




</script>

<style lang="scss" scoped>
.result-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.blue-section {
  background: #007FFF;
  height: 240px;
  padding: 40px 20px 20px 20px;
  position: relative;
  border-radius: 0 0 25px 25px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .success-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;

    .check-mark {
      width: 60px;
      height: 60px;
      background: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #007FFF;
      font-size: 28px;
      font-weight: bold;
      line-height: 1;
    }

    .van-icon {
      width: 60px;
      height: 60px;
      background: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #007FFF;
      font-size: 24px;
      font-weight: bold;
    }
  }

  .result-title {
    text-align: center;
    color: #fff;
    font-size: 15px;
    font-weight: 500;
  }
}

.white-section {
  flex: 1;
  background: #f5f5f5;
  padding: 0 16px;
  // margin-top: -10px;
  z-index: 2;
}

.detail-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  margin-top: -20px;

  .card-title {
    font-size: 16px;
    font-weight: 600;
    // height: 52px;
    color: #333;
    margin-bottom: 14px;
  }
}

.result-list {
  .list-header {
    width: 100%;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 8px;
    height: 38px;
    display: flex;

    .header-name {
      display: inline-block;
      font-size: 15px;
      color: #969799;
      width: 30%;
      font-weight: 500;
      // background-color: #000000;
    }

    .header-result {
      font-size: 15px;
      color: #000000;
      display: inline-block;
      // margin-left: 23%;
      width: 65%;
      // background-color: #000000;
      font-weight: 500;
    }
  }

  .result-item {
    align-items: center;
    padding: 12px 0;

    &:not(:last-child) {
      border-bottom: 1px solid #f7f8fa;
    }

    .item-name {
      font-size: 15px;
      color: #969799;
      flex: 1;
    }

    .item-result {
      font-size: 15px;
      margin-left: 23%;

      &.success {
        color: #07c160;
      }

      &.failed {
        color: #ED6A0C;
      }
    }
  }
}

.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  color: #1989fa;
  font-size: 14px;
  cursor: pointer;

  .van-icon {
    margin-right: 4px;
    font-size: 16px;
  }
}

.back-btn-container {
  margin-top: 30px;
  padding-bottom: 30px;

  .back-btn {
    width: 200px;
    height: 44px;
    border-radius: 22px;
    font-size: 16px;
    font-weight: 500;
    margin: 0 auto;
    display: block;
  }
}
</style>
