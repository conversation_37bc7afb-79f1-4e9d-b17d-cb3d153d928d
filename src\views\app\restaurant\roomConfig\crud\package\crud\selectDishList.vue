<template>
  <div class="content-list-dish">
    <van-search
      show-action
      @update:model-value="onSearch"
      placeholder="搜索"
      @cancel="cancel"
      :clearable="false"
      v-model="searchValue"
    />
    <div class="nav">
      <van-sidebar v-model="active" class="van-sidebar">
        <van-sidebar-item class="item" @click="switchSort('')">
          <template #title>
            <div
              class="title"
              style="
                height: 70px;
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
              <div class="title">全部</div>
            </div>
          </template>
        </van-sidebar-item>
        <template v-for="(el, i) in sortlist" :key="i">
          <van-sidebar-item class="item" @click="switchSort(el.id)">
            <template #title>
              <div class="title">
                <!-- <img :src="el.image" alt="" /> -->
                 <div style="height: 30px; "></div>
                 <div class="category-item">
                   <div class="title">{{ el.title }}</div>
                   <div
                     v-if="getCategoryDishCount(el.id) > 0"
                     class="custom-badge"
                   >
                     {{ getCategoryDishCount(el.id) }}
                   </div>
                 </div>
              </div>
            </template>
          </van-sidebar-item>
        </template>
      </van-sidebar>
    </div>
    <div class="list">
      <van-list
        class="van-list"
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <template v-for="(item, index) in disheslist" :key="item.id">
          <div class="card">
            <div class="left">
              <div class="imgs">
                <template v-if="item.image">
                  <img class="img" :src="item.image" alt="" />
                </template>
                <template v-else>
                  <div class="img">{{ item.title[0] }}</div>
                </template>
              </div>
              <div class="desc">
                <div class="title">{{ item.title }}</div>
                <div class="info">
                  库存：{{ item.stock ? item.stock : "无限" }}
                </div>
                <div class="info">
                  限购：{{ item.quota ? item.quota : "无限" }}
                </div>
                <div class="btn">￥{{ item.price }}</div>
                <!-- <div class="btn" @click="advancedSettings(index)">高级设置</div> -->
              </div>
            </div>
            <div class="right">
              
              <div class="select">
                <van-checkbox
                  v-model="item.checked"
                  icon-size="20px"
                  @change="selectChange(item)"
                />
              </div>
            </div>
          </div>
        </template>
      </van-list>
    </div>

    <div class="settle-account">
      <div class="left">
        <img src="../../../../../../../static/icons/car.png" alt="" />
        <div class="text">已选{{ selectNum }}个</div>
      </div>
      <div class="right" @click="goRealseinfo">完成</div>
    </div>
    <van-popup
      v-model:show="showLeft"
      position="right"
      :style="{ width: '75%', height: '100%' }"
    >
      <van-form @submit="onSubmit">
        <van-cell title="库存与限购"></van-cell>
        <van-cell center title="库存">
          <template #right-icon>
            <van-switch v-model="form.isstock" />
          </template>
        </van-cell>
        <template v-if="form.isstock">
          <van-field
            v-model="form.stock"
            label="库存"
            type="digit"
            placeholder="请输入"
            input-align="right"
          >
            <template #left-icon>
              <img
                :style="{ width: '18px', height: '18px' }"
                src="http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/huashan/2023-07-25/O2ACXY1Pp4TTbus5NFSG2sB9O7MSRJCw.png"
                alt=""
              />
            </template>
          </van-field>
        </template>
        <van-field
          v-model="form.quota"
          label="限购"
          type="digit"
          placeholder="请输入"
          input-align="right"
        />
        <div
          class="tips"
          :style="{
            fontSize: '14px',
            color: '#a2a3a5',
            marginLeft: '5%',
            marginTop: '8px',
          }"
        >
          默认为0，0为无限制
        </div>

        <div style="margin: 16px">
          <van-button block type="primary" native-type="submit">
            确定
          </van-button>
        </div>
      </van-form>
    </van-popup>
  </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, watch, onMounted } from "vue";
import { showFailToast, showToast } from "vant";
const { proxy } = getCurrentInstance();
import { useRouter, useRoute } from "vue-router";
import { dishesInfo } from "@/store/dishes_public";
import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();

// let data = reactive({
//   showBottom: false,
//   curTypeIndex: -1, // 默认选中"全部"
//   selectList: app.dishFoodList,
//   typeList: [],
//   dishList: [],
// });

// 路由跳转
const route = useRoute();
const router = useRouter();

let active = ref(0);
let searchValue = ref("");
let category_id = "";

let list = ref([]);
let loading = ref(false);
let finished = ref(false);
// 侧边框
let showLeft = ref(false);

// 菜品分类列表
let sortlist = ref([]);
// 菜品列表
let disheslist = ref([]);
// pinia 上的菜品信息
let dishes = dishesInfo();
// 选择的时间
// 菜品申请数据
let postData = ref({
  page: 0,
  per_page: 10,
});
let total = ref(0);

// 高级设置数据
let form = ref({
  isstock: false,
  stock: '',
  quota: ''
});
// 高级设置配置的项
let seniorindex = ref(0);
// 已选个数
let selectNum = ref(0);
// 分类菜品数量缓存
let categoryDishCounts = ref({});

// 获取分类下的菜品数量
const getCategoryDishCount = (categoryId) => {
  const count = categoryDishCounts.value[categoryId];
  return count > 0 ? count : '';
};`2`
  if(app.dishFoodList){
    console.log(app.dishFoodList,'qqq')
  }
// 获取所有分类的菜品数量
const getAllCategoryDishCounts = () => {
  // 获取每个分类的菜品数量
  sortlist.value.forEach(category => {
    proxy
      .$get("dishes/get_list", {
        category_id: category.id,
        page: 1,
        dininghall_id: localStorage.getItem('dininghall'),
        per_page: 1000 // 获取足够多的数据来统计总数
      })
      .then((res) => {
        if (res.code == 200  && res.data.items) {
          categoryDishCounts.value[category.id] = res.data.items.length;
        }
      })
      .catch((err) => {
        console.log(`获取分类${category.id}菜品数量失败:`, err);
      });
  });
};

function debounce(fn, delay = 300) {
  let time = null;
  function _debounce(...args) {
    if (time !== null) {
      clearTimeout(time);
    }
    time = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  }
  return _debounce;
}
let keyword = "";
// let ccategory_id = "";

let onSearch = debounce((e) => {
  searchValue.value = keyword = e;
  postData.value.page = 0;
  disheslist.value = [];
  category_id = null;
  onLoad();
});
let cancel = () => {
  keyword = "";
  category_id = "";
  postData.value.page = 0;
  disheslist.value = [];
  active.value=0
  onLoad();
};
onMounted(() => {
  getSortlist();
  console.log('store中的菜品数据:', app.dishFoodList)

  // 初始化选中数量，优先使用store中的数据
  if (app.dishFoodList && Array.isArray(app.dishFoodList) && app.dishFoodList.length > 0) {
    // 将store中的数组数据同步到dishes.dishesInfo
    dishes.dishesInfo = [...app.dishFoodList];
    console.log('从store初始化菜品数据:', dishes.dishesInfo);
  } else {
    // 如果store中没有数据，初始化为空数组
    dishes.dishesInfo = [];
    console.log('初始化为空菜品数据');
  }

  // 设置选中数量
  selectNum.value = dishes.dishesInfo.length;
  console.log('初始选中数量:', selectNum.value);
});

const onLoad = () => {
  postData.value.page++;
  finished.value = false;
  postData.value.keyword = keyword;
  postData.value.category_id = category_id || null;
  getDisheslist();
};

// 获取菜品分类列表
const getSortlist = () => {
  proxy
    .$get("dishes_category/get_all", {})
    .then((res) => {
      // console.log("菜品分类结果 --->", res);
      if (res.code == 200) {
        list.value = res.data;
        list.value.map((item) => {
          item.image =`/icons/${item.image}.png`
        });
        sortlist.value = list.value;
        // 获取分类列表后，立即获取每个分类的菜品数量
        getAllCategoryDishCounts();
        // postData.value.category_id = sortlist.value[0].id;
        // startLoading.value = true;
      } else {
        showFailToast(res.msg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 获取菜品分类项列表
const getDisheslist = () => {
  proxy
    .$get("dishes/get_list", postData.value)
    .then((res) => {
      if (res.code == 200) {
        res.data.items.forEach((el) => {
          // 检查菜品是否已被选中（从dishes.dishesInfo中查找）
          // 注意：这里需要根据实际的菜品ID字段进行匹配
          const selectedDish = dishes.dishesInfo.find(item => {
            // 如果store中的数据有dish_id字段，使用dish_id匹配
            if (item.dish_id) {
              return item.dish_id == el.id;
            }
            // 否则使用id字段匹配
            return item.id == el.id;
          });

          if (selectedDish) {
            el.checked = true;
            el.quota = selectedDish.quota;
            el.stock = selectedDish.stock;
            console.log(`菜品 ${el.title} 被标记为选中`);
          } else {
            el.checked = false;
          }
          disheslist.value.push(el);
        });
        loading.value = false;
        // 数据全部加载完成
        if (!res.data.items.length) {
          finished.value = true;
        }
      } else {
        loading.value = false;
        finished.value = true;
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 切换菜品分类
const switchSort = (id) => {
  category_id = id;
  disheslist.value = [];
  postData.value.page = 0;
  finished.value = false;

  // 加载对应分类的菜品数据
  onLoad();
};

// 打开高级设置
const advancedSettings = (index) => {
  seniorindex.value = index;
  showLeft.value = true;
  // console.log("查看选中项 --->", disheslist.value[seniorindex.value]);
  form.value = {
    isstock: false,
    stock: '',
    quota: ''
  };
  if (disheslist.value[seniorindex.value].quota) {
    form.value.quota = disheslist.value[seniorindex.value].quota;
  }
  if (disheslist.value[seniorindex.value].stock) {
    form.value.isstock = true;
    form.value.stock = disheslist.value[seniorindex.value].stock;
  }
};

// 确定高级设置
const onSubmit = () => {
  showLeft.value = false;
  if (form.value.quota) {
    disheslist.value[seniorindex.value].quota = form.value.quota;
  }
  if (form.value.isstock) {
    disheslist.value[seniorindex.value].stock = form.value.stock;
  }
  if (disheslist.value[seniorindex.value].checked) {
    dishes.dishesInfo.forEach((item) => {
      if (item.id == disheslist.value[seniorindex.value].id) {
        item.quota = disheslist.value[seniorindex.value].quota;
        item.stock = disheslist.value[seniorindex.value].stock;
      }
    });
  }
};

// 菜品是否被选择
const selectChange = (el) => {
  console.log("菜品选择状态变化 --->", el.title, el.checked);

  if (el.checked) {
    // 检查是否已经存在，避免重复添加
    const existingIndex = dishes.dishesInfo.findIndex(item => {
      return (item.dish_id && item.dish_id == el.id) || (item.id && item.id == el.id);
    });

    if (existingIndex === -1) {
      // 构造标准格式的菜品数据
      const dishData = {
        image: el.image || "",
        price: parseFloat(el.price) || 0,
        title: el.title,
        dish_id: el.id, // 使用dish_id字段
        quantity: 1, // 默认数量为1
        quota: el.quota || null,
        stock: el.stock || null
      };

      dishes.dishesInfo.push(dishData);
      selectNum.value = dishes.dishesInfo.length;
      // console.log("添加菜品:", dishData);
      // console.log("当前选中菜品列表:", dishes.dishesInfo);
    }
  } else {
    selectNum.value = selectNum.value - 1;
    // 菜品被取消 - 根据dish_id或id进行匹配删除
    const newList = dishes.dishesInfo.filter((item) => {
      if (item.dish_id) {
        return item.dish_id != el.id;
      }
      return item.id != el.id;
    });
    dishes.dishesInfo = newList;
    selectNum.value = dishes.dishesInfo.length;
    // console.log("移除菜品:", el.title);
    // console.log("当前选中菜品列表:", dishes.dishesInfo);
  }
};

// 跳转到详情页
const goRealseinfo = () => {
  // 打印选中的菜品
  // console.log("打印选中的菜品:", dishes.dishesInfo);
  if (!dishes.dishesInfo || dishes.dishesInfo.length === 0) {
    showToast("请选择菜品");
  } else {
    // 将选中的菜品保存到store中
    app.dishFoodList = [...dishes.dishesInfo]; // 创建副本避免引用问题
    // console.log("保存到store的菜品数据:", app.dishFoodList);
    router.push({ path: route.query.url, query: { id: route.query.id }});
  }
};
</script>

<style lang="scss">

.content-list-dish {
  position: relative;
  width: 100%;
  height: 100vh;
  // display: flex;
  overflow: hidden;
  background-color: #f2f3f4;
  .van-sidebar-item--select:before {
    height: 100%;
    border-radius: 1.5px;
  }
  .van-badge__wrapper {
    width: 100%;
  }

  ::-webkit-scrollbar {
    display: none;
  }
  .nav {
    // position: fixed;
    position: absolute;
    overflow: auto;
    width: 74px;

    .van-sidebar {
      width: 100%;
      height: calc(100vh - 150px);
      overflow: auto;

      .item {
        width: 100%;
        font-size: 14px;
        padding: 0;
        padding-top: 16px;

        .title {
          width: 100%;
          padding: 0 4px;
          // height: 87px;
          text-align: center;
          img {
            width: 30px;
            height: 30px;
            margin-bottom: 4px;
          }
          .title {
            width: 100%;
            margin-bottom: 10px;
          }
        }

        // 自定义分类badge样式
        .category-item {
          position: relative;
          width: 100%;

          .custom-badge {
            position: absolute;
            top: -15px;
            right: -0px; // 左移位置
            min-width: 16px;
            height: 16px;
            padding: 0 4px;
            font-size: 10px;
            line-height: 16px;
            border-radius: 8px;
            background-color: #ff4444;
            color: #fff;
            // border: 1px solid #fff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
          }
        }
      }
    }
  }

  .list {
    overflow-y: auto;
    width: 100%;
    height: calc(100vh - 54px);
    padding-left: 74px;
    padding-bottom: 100px;

    // width: calc(100% -74px);
    .van-list {
      background-color: #fff;
      .card {
        display: flex;
        justify-content: space-between;
        padding: 12px;

        .left {
          display: flex;

          .imgs {
            margin-right: 10px;
            .img {
              width: 90px;
              height: 90px;
              border-radius: 8px;
              line-height: 90px;
              text-align: center;
              background-color: #007fff;
              color: #fff;
              font-size: 37px;
            }
          }

          .desc {
            font-size: 10px;
            .title {
              // height: 60px; 
              font-size: 14px;
            }

            .info {
              margin-top: 8px;
              color: #a2a3a5;
            }

            .btn {
              margin-top: 18px;
              font-family: PingFang SC;
              font-size: 17px;
              font-weight: normal;
              line-height: 23px;
              letter-spacing: normal;
              color: #323233;
              // border-radius: 10px;
              // border: 1px solid #007fff;
              // color: #007fff;
            }
          }
        }

        .right {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          font-size: 12px;
          color: #ff5219;

          .select {
            margin-top: 20px;
          }
        }
      }
    }
  }

  .settle-account {
    position: fixed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    bottom: 26px;
    left: 5%;
    padding: 0 16px;
    padding-right: 10px;
    width: 90%;
    height: 58px;
    border-radius: 29px;
    background-color: #fff;
    box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
    font-size: 15px;

    .left {
      display: flex;
      align-items: center;
      img {
        margin-right: 16px;
        width: 44px;
        height: 36px;
      }
    }

    .right {
      width: 90px;
      height: 46px;
      border-radius: 23px;
      color: #fff;
      background-color: #007fff;
      text-align: center;
      line-height: 46px;
    }
  }
}
</style>
