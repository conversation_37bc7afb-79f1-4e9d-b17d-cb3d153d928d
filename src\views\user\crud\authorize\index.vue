<template>
  <div class="yp_qr_auth">
    <div class="icon">
      <van-image
        width="68"
        height="68"
        radius="4"
        src="https://filetest.eykj.cn/tool/mett.svg"
      />
    </div>
    <div class="sub-text">云一消费 {{ app.env.VITE_APP_VERSION }}</div>
    <van-cell
      title="授权状态 "
      style="border-radius: 8px; margin:0 14px;margin-bottom: 18px; width: auto"
    >
    
    <span>{{info.number}}人</span>
    </van-cell>
    <div class="capacity">
      <van-cell
        title="容量使用"
        @click="onClick(1)"
        :border="false"
        style="border-radius: 8px; width: auto"
      >
        <span style="color: #007FFF;">去扩容</span>
      </van-cell>
      <div class="progress-container">
        <div class="progress-title">
         <span>短信容量</span>
        <div class="progress-text">
            {{ info.user_count }}/{{ info.device_count }}
        </div>
        </div>
        <van-progress :percentage="devicePercentage" stroke-width="10" />
      </div>
      <div class="progress-container">
        <div class="progress-title">
         <span>授权人数</span>
        <div class="progress-text">
            {{ info.user_auth_count }}/{{ info.auth_count }}
        </div>
        </div>
        <van-progress :percentage="capacityPercentage" color="#00B042" stroke-width="10" />
        <div v-if="isCapacityInsufficient" class="warning-text">容量不足，建议及时扩容</div>
      </div>
    </div>
    <div class="capacity">
      <van-cell
        title="容量使用"
        :border="false"
        style="border-radius: 8px; width: auto"
      >
      </van-cell>
      <div class="text">
          <div>当前已用：{{info.user_count}}</div>
          <div>剩余容量：{{info.device_count - info.user_count}}</div>
          <div>到期时间：{{info.end_date}}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, computed } from "vue";
import { showToast } from "vant";
import { useLoginStore } from "@/store/dingLogin";

const app = useLoginStore();
const router = useRouter();
const route = useRoute();
let loading = ref(false);
let info = ref(
  {
    number:400,
    user_count:80,
    device_count:100,
    user_auth_count:499,
    auth_count:500,
    end_date:"2025-12-31"
  }
);
let { proxy } = getCurrentInstance();
// 计算容量使用百分比
const capacityPercentage = computed(() => {
  if (!info.value.auth_count || info.value.auth_count === 0) return 0;
  return (info.value.user_auth_count / info.value.auth_count) * 100;
});
const devicePercentage = computed(() => {
  if (!info.value.user_count || info.value.device_count === 0) return 0;
  return (info.value.user_count / info.value.device_count) * 100;
});
// 判断容量是否不足（超过80%显示警告）
const isCapacityInsufficient = computed(() => {
  console.log("capacityPercentage.value", capacityPercentage.value);
  return capacityPercentage.value >= 100;
});

let config = {
  button: {
    // sub_text: "授权后大屏可以登录",
    but_text: "设备授权",
  },
};
let onClick = (id) => {
  proxy.$_dd.biz.util.openLink({
      url: `https://page.dingtalk.com/wow/dingtalk/act/serviceconversation?wh_biz=tm&showmenu=false&goodsCode=${
        app.env.VITE_APP_DT_GOODS
      }&corpId=${app.corpId}&token=${
        app.env.VITE_APP_DT_GOODS_TOKEN
      }`,
      onSuccess: function (result) {
        console.log(result);
      },
      onFail: function (err) {
        console.log(err);
      },
    })
};
let getInfo = () => {
  loading.value = true;
  proxy
    .$get("auth/get_detail", {})
    .then((res) => {
      if (!res.errcode) {
        info.value=res.result
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      showToast(err);
    })
    .finally(() => {
      setTimeout(() => {
        loading.value = false;
      }, 1500);
    });
};
// getInfo()
</script>

<style scoped lang="scss">
.yp_qr_auth {
  .icon {
    margin-top: 24px;
    text-align: center;
  }
  .sub-text {
    font-size: 17px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: normal;
    color: #171A1D;
    margin: 16px;
    margin-bottom: 24px;
    text-align: center;
  }
  .capacity{
    margin:0 14px;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 18px;
    .text{
      padding:  16px;
      padding-top:0;
      font-size: 14px;
font-weight: normal;
line-height: 20px;
letter-spacing: normal;
color: #9E9E9E;
div{
  margin-bottom: 4px;
}
    }
    .progress-container {
      position: relative;
      padding: 0 16px 16px 16px;
      .progress-title{
        font-size: 15px;
font-weight: normal;
line-height: 20px;
text-align: right;
letter-spacing: normal;
color: #9E9E9E;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }

      .progress-text {
        font-size: 15px;
font-weight: normal;
line-height: 20px;
text-align: right;
letter-spacing: normal;
color: #9E9E9E;
      }

      .warning-text {
        margin-top: 10px;
        font-size: 12px;
        font-weight: normal;
        line-height: 14px;
        color: #FF3B30;
      }

      // 自定义进度条样式
      :deep(.van-progress) {
        .van-progress__portion {
          background: #007FFF;
          border-radius: 4px;
        }

        .van-progress__pivot {
          display: none;
        }
      }

      :deep(.van-progress__track) {
        background: #E5E5EA;
        border-radius: 4px;
      }
    }
  }
  .van-cell {
    padding: 16px;
    padding-bottom: 12px;
    font-size: 17px;
font-weight: normal;
line-height: 20px;
letter-spacing: normal;
color: #171A1D;
    span{
      font-size: 14px;
      line-height: 17px;
    }
  }
}
</style>
