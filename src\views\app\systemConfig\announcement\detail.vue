<template>
  <div class="announcement-detail-container">
    <!-- 导航栏 -->
    <van-nav-bar title="公告详情" left-arrow @click-left="goBack">
      <template #right>
        <van-icon name="ellipsis" @click="showActionSheet = true" />
      </template>
    </van-nav-bar>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" size="24px">加载中...</van-loading>
    </div>
    
    <!-- 详情内容 -->
    <div v-else class="detail-content">
      <!-- 公告头部信息 -->
      <div class="detail-header">
        <div class="header-top">
          <h1 class="announcement-title">{{ announcementDetail.title }}</h1>
          <div class="status-badge">
            <van-tag :type="getStatusType(announcementDetail.status)" size="large">
              {{ getStatusText(announcementDetail.status) }}
            </van-tag>
            <van-tag v-if="announcementDetail.isTop" type="danger" size="large">
              置顶
            </van-tag>
          </div>
        </div>
        
        <div class="header-meta">
          <div class="meta-item">
            <van-icon name="clock-o" size="14" />
            <span>发布时间：{{ announcementDetail.createTime }}</span>
          </div>
          <div v-if="announcementDetail.updateTime" class="meta-item">
            <van-icon name="edit" size="14" />
            <span>更新时间：{{ announcementDetail.updateTime }}</span>
          </div>
        </div>
      </div>

      <!-- 公告内容 -->
      <div class="detail-body">
        <div class="content-section">
          <h3 class="section-title">公告内容</h3>
          <div class="content-text">{{ announcementDetail.content }}</div>
        </div>

        <!-- 附件列表 -->
        <div v-if="announcementDetail.attachments && announcementDetail.attachments.length > 0" class="attachment-section">
          <h3 class="section-title">附件下载</h3>
          <div class="attachment-list">
            <div v-for="(file, index) in announcementDetail.attachments" :key="index" 
                 class="attachment-item" @click="downloadFile(file)">
              <div class="file-info">
                <van-icon name="description" size="20" color="#1989fa" />
                <div class="file-details">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-size">{{ file.size }}</div>
                </div>
              </div>
              <van-icon name="arrow" size="16" />
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button type="default" block @click="onEditClick">
          编辑公告
        </van-button>
      </div>
    </div>

    <!-- 操作菜单 -->
    <van-action-sheet v-model:show="showActionSheet" :actions="actionSheetActions" @select="onActionSelect" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'

const router = useRouter()
const route = useRoute()

// 状态
const loading = ref(true)
const showActionSheet = ref(false)

// 公告详情数据
const announcementDetail = reactive({
  id: '',
  title: '',
  content: '',
  status: '',
  isTop: false,
  createTime: '',
  updateTime: '',
  attachments: []
})

// 操作菜单
const actionSheetActions = computed(() => {
  const actions = [
    { name: '编辑公告', value: 'edit' },
    { name: announcementDetail.isTop ? '取消置顶' : '置顶公告', value: 'toggle_top' }
  ]
  
  if (announcementDetail.status !== 'offline') {
    actions.push({ name: '下线公告', value: 'offline' })
  }
  
  actions.push({ name: '删除公告', value: 'delete', color: '#ee0a24' })
  
  return actions
})

// 模拟数据
const mockData = {
  1: {
    id: 1,
    title: '系统维护通知',
    content: `为了提升系统性能，我们将于本周六凌晨2:00-6:00进行系统维护，期间系统将暂停服务，请提前做好相关准备。

维护内容包括：
1. 数据库性能优化
2. 服务器硬件升级
3. 安全补丁更新
4. 新功能部署测试

维护期间如有紧急情况，请联系客服热线：400-123-4567

感谢您的理解与支持！`,
    status: 'published',
    isTop: true,
    createTime: '2025-07-15 10:30:00',
    updateTime: '2025-07-15 14:20:00',
    attachments: [
      { name: '维护计划详细说明.pdf', size: '2.5MB', url: '#' },
      { name: '服务恢复时间表.xlsx', size: '1.2MB', url: '#' }
    ]
  },
  2: {
    id: 2,
    title: '新功能上线公告',
    content: `我们很高兴地宣布，新的移动端功能已经正式上线！

新增功能：
• 扫码支付：支持微信、支付宝扫码付款
• 余额查询：实时查看账户余额和消费记录
• 充值优惠：新用户首次充值享受9.5折优惠
• 消息推送：重要通知及时推送到手机

这些新功能将为用户带来更加便捷的使用体验，欢迎大家体验并提出宝贵意见。

如有任何问题，请联系客服：<EMAIL>`,
    status: 'published',
    isTop: false,
    createTime: '2025-07-14 15:20:00',
    updateTime: '',
    attachments: []
  }
}

// 方法
const loadData = () => {
  const id = route.query.id
  if (id && mockData[id]) {
    Object.assign(announcementDetail, mockData[id])
  }
  
  setTimeout(() => {
    loading.value = false
  }, 800)
}

const goBack = () => {
  router.back()
}

const getStatusType = (status) => {
  const statusMap = {
    published: 'success',
    draft: 'warning',
    offline: 'default'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status) => {
  const statusMap = {
    published: '已发布',
    draft: '草稿',
    offline: '已下线'
  }
  return statusMap[status] || '未知'
}

const downloadFile = (file) => {
  showToast(`正在下载：${file.name}`)
  // 这里实现文件下载逻辑
  console.log('下载文件:', file)
}

const onEditClick = () => {
  router.push({
    path: '/systemConfig/announcement/edit',
    query: { id: announcementDetail.id }
  })
}

const onActionSelect = async (action) => {
  showActionSheet.value = false
  
  switch (action.value) {
    case 'edit':
      onEditClick()
      break
      
    case 'toggle_top':
      try {
        const actionText = announcementDetail.isTop ? '取消置顶' : '置顶'
        await showConfirmDialog({
          title: '确认操作',
          message: `确定要${actionText}这条公告吗？`
        })
        
        announcementDetail.isTop = !announcementDetail.isTop
        showToast(`${actionText}成功`)
      } catch (error) {
        // 用户取消
      }
      break
      
    case 'offline':
      try {
        await showConfirmDialog({
          title: '确认下线',
          message: '下线后用户将无法查看此公告，确定要下线吗？'
        })
        
        announcementDetail.status = 'offline'
        showToast('公告已下线')
      } catch (error) {
        // 用户取消
      }
      break
      
    case 'delete':
      try {
        await showConfirmDialog({
          title: '确认删除',
          message: '删除后无法恢复，确定要删除这条公告吗？'
        })
        
        showToast('删除成功')
        router.back()
      } catch (error) {
        // 用户取消
      }
      break
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.announcement-detail-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  padding-bottom: 80px; // 为底部按钮留空间
}

.detail-header {
  background: #fff;
  padding: 20px 16px;
  margin-bottom: 12px;
  
  .header-top {
    margin-bottom: 16px;
    
    .announcement-title {
      font-size: 20px;
      font-weight: 600;
      color: #323233;
      line-height: 28px;
      margin: 0 0 12px 0;
    }
    
    .status-badge {
      display: flex;
      gap: 8px;
    }
  }
  
  .header-meta {
    .meta-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      color: #646566;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      span {
        margin-left: 6px;
      }
    }
  }
}

.detail-body {
  .content-section {
    background: #fff;
    padding: 20px 16px;
    margin-bottom: 12px;
    
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #323233;
      margin: 0 0 16px 0;
    }
    
    .content-text {
      font-size: 15px;
      color: #323233;
      line-height: 24px;
      white-space: pre-line;
    }
  }
  
  .attachment-section {
    background: #fff;
    padding: 20px 16px;
    margin-bottom: 12px;
    
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #323233;
      margin: 0 0 16px 0;
    }
    
    .attachment-list {
      .attachment-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #ebedf0;
        cursor: pointer;
        
        &:last-child {
          border-bottom: none;
        }
        
        &:active {
          background: #f2f3f5;
        }
        
        .file-info {
          display: flex;
          align-items: center;
          flex: 1;
          
          .file-details {
            margin-left: 12px;
            
            .file-name {
              font-size: 14px;
              color: #323233;
              margin-bottom: 4px;
            }
            
            .file-size {
              font-size: 12px;
              color: #969799;
            }
          }
        }
      }
    }
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: #fff;
  border-top: 1px solid #ebedf0;
}

:deep(.van-nav-bar__title) {
  font-weight: 500;
}
</style>
