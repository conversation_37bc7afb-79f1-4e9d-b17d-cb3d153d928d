<template>
  <div class="wrapper">
    <!-- 弹出层 -->
    <van-popup v-model:show="data.isPicker" position="bottom" round @close="confirmOn">
      <van-picker ref="picker" title="请选择时间" :columns="data.columns" :columns-top="true" @change="onChange"
        @cancel="cancelOn" @confirm="onConfirm" v-model="data.selectedValues" />
    </van-popup>
  </div>
</template>
<script setup>
import { reactive, watch, ref, getCurrentInstance } from "vue";

// 组件内部状态管理
const data = reactive({
  isPicker: false,           // 是否显示弹出层 (布尔值) - 控制选择器弹窗的显示状态
  columns: [],               // 所有时间列 (数组) - 选择器的列数据，包含年月日时分等
  selectedValues: [],        // 控件选择的时间值 (数组) - 当前选中的各列值
  lastConfirmedValue: null,  // 最后确认的值 (字符串) - 存储用户最后确认的时间值，用于取消时恢复
});

// Props定义 - 日期时间选择器组件配置
const props = defineProps({
  // 弹窗显示状态
  showPicker: {
    type: Boolean,           // 是否显示选择器弹窗 (布尔值) - true: 显示选择器, false: 隐藏选择器
  },

  // 选择器类型
  type: {
    type: String,            // 选择器类型 (字符串) - "datetime": 日期时间, "date": 日期, "time": 时间
  },

  // 日期值
  values: {
    type: String,            // 当前日期值 (字符串) - 格式如"2023-09-05 19:28"，用于初始化选择器
  },
});

// 事件定义 - 向父组件传递的事件
const emit = defineEmits([
  "changeValue",             // 值变化事件 - 当选择器值发生变化时触发
  "confirm"                  // 确认事件 - 当用户点击确认按钮时触发
]);

// 组件功能说明:
// - 支持日期时间的选择，精确到分钟
// - 支持取消操作，可恢复到上次确认的值
// - 动态生成选择器列数据
// - 支持多种时间格式的显示

watch(
  () => props.showPicker,
  (val) => {
    data.isPicker = val;
    if (val) { // 当打开弹窗时
      data.columns = [];
      data.selectedValues = []; // 清空选中值
      // 优先使用最后确认的值，如果没有则使用props.values
      const valueToUse = data.lastConfirmedValue || props.values;
      getcolumns(valueToUse);
    }
  },
  {
    immediate: true,
  }
);

function onChange ({ selectedValues, columnIndex }) {
  // 当选择了年份或月份时，需要更新天数
  if (columnIndex === 0 || columnIndex === 1) {  // 年份或月份列发生变化
    const selectedYear = parseInt(selectedValues[0]);
    const selectedMonth = parseInt(selectedValues[1]);

    // 获取新的天数
    const newDays = getCountDays(selectedYear, selectedMonth);

    // 生成新的天数数组
    const newDayColumn = Array.from({ length: newDays }, (_, i) => {
      const dayNum = i + 1;
      const value = dayNum.toString().padStart(2, '0');
      return { text: value, value: value };
    });

    // 更新天数列
    if (props.type === 'datetime' || props.type === 'date') {
      const currentDay = parseInt(selectedValues[2] || '0');

      // 更新天数列
      data.columns[2] = newDayColumn;

      // 如果日期无效或超过最大天数，设置为1号
      if (!currentDay || currentDay > newDays) {
        data.selectedValues[2] = '01';
        // 强制更新选中值
        data.selectedValues = [...data.selectedValues];
      }
    }
  }
}

function getcolumns (valueToUse) {
  console.log("传入数据-----------", valueToUse);
  let dateVaules;
  let Y, M, D, h, m, s;

  // 处理日期时间类型
  if (props.type === "datetime" || props.type === "date") {
    if (valueToUse) {
      // 修正日期解析方式
      dateVaules = new Date(valueToUse.replace(/-/g, '/'));
      if (isNaN(dateVaules.getTime())) {
        dateVaules = new Date();
      }
    } else {
      dateVaules = new Date();
    }
    Y = dateVaules.getFullYear();
    M = dateVaules.getMonth() + 1;
    D = dateVaules.getDate();
    if (props.type === "datetime") {
      h = dateVaules.getHours();
      m = dateVaules.getMinutes();
      s = dateVaules.getSeconds();
    }
  }
  // 处理完整时间类型（时分秒）
  else if (props.type === "time" || props.type === "time-full") {
    if (valueToUse) {
      const timeParts = valueToUse.split(':');
      h = parseInt(timeParts[0]) || 0;
      m = parseInt(timeParts[1]) || 0;
      s = parseInt(timeParts[2]) || 0;
    } else {
      dateVaules = new Date();
      h = dateVaules.getHours();
      m = dateVaules.getMinutes();
      s = dateVaules.getSeconds();
    }
  }
  // 处理简化时间类型（仅时分）
  else if (props.type === "time-short") {
    if (valueToUse) {
      const timeParts = valueToUse.split(':');
      h = parseInt(timeParts[0]) || 0;
      m = parseInt(timeParts[1]) || 0;
      s = 0; // 简化时间不包含秒
    } else {
      dateVaules = new Date();
      h = dateVaules.getHours();
      m = dateVaules.getMinutes();
      s = 0; // 简化时间不包含秒
    }
  }

  let year = []; //获取前后十年数组
  let Currentday = new Date().getFullYear();
  for (let i = Currentday - 10; i < Currentday + 10; i++) {
    year.push({ text: i.toString(), value: i });
  }
  // 找到当前年份的索引
  const currentYearIndex = year.findIndex(item => item.value === Y);
  year.defaultIndex = currentYearIndex >= 0 ? currentYearIndex : 10; // 默认选中当前年份

  // 个位数补0
  const _M = M ? (M < 10 ? `0${M}` : M.toString()) : undefined; //月份比实际获取的少1，所以要加1
  const _D = D ? (D < 10 ? `0${D}` : D.toString()) : undefined;
  const _h = h ? (h < 10 ? `0${h}` : h.toString()) : undefined;
  const _m = m ? (m < 10 ? `0${m}` : m.toString()) : undefined;
  const _s = s ? (s < 10 ? `0${s}` : s.toString()) : undefined;

  // 获取当月的天数
  let days = getCountDays(Y, M);
  // 确保选择的日期不超过当月最大天数，如果没有选中值则使用1号
  if (!D) {
    D = 1;
    data.selectedValues[2] = '01';
  } else if (D > days) {
    D = days;
    data.selectedValues[2] = D.toString().padStart(2, '0');
  }

  let day = []; //创建当月天数数组
  day = Object.keys(Array.apply(null, { length: days })).map(function (item) {
    const dayNum = +item + 1;
    if (dayNum < 10) {
      return { text: "0" + dayNum, value: "0" + dayNum };
    } else {
      return {
        text: dayNum.toString(),
        value: dayNum.toString()
      };
    }
  });

  // 修改生成年月日时分秒时间值的逻辑
  data.selectedValues = [];
  if (props.type === "datetime" || props.type === "date") {
    const maxDays = getCountDays(Y, M);
    // 如果日期超过最大天数，设置为1号
    if (D > maxDays) {
      D = 1;
    }

    data.selectedValues.push(Y);
    data.selectedValues.push(M < 10 ? `0${M}` : M.toString());
    data.selectedValues.push(D < 10 ? `0${D}` : D.toString());
    if (props.type === "datetime") {
      data.selectedValues.push(h < 10 ? `0${h}` : h.toString());
      data.selectedValues.push(m < 10 ? `0${m}` : m.toString());
      data.selectedValues.push(s < 10 ? `0${s}` : s.toString());
    }
  } else if (props.type === "time" || props.type === "time-full") {
    data.selectedValues.push(h < 10 ? `0${h}` : h.toString());
    data.selectedValues.push(m < 10 ? `0${m}` : m.toString());
    data.selectedValues.push(s < 10 ? `0${s}` : s.toString());
  } else if (props.type === "time-short") {
    data.selectedValues.push(h < 10 ? `0${h}` : h.toString());
    data.selectedValues.push(m < 10 ? `0${m}` : m.toString());
    // time-short 类型不包含秒
  }

  // 根据类型生成对应的列
  if (props.type === "datetime" || props.type === "date") {
    Y && data.columns.push(year); //生成年列

    let month = []; //获取12月数组
    month = Object.keys(Array.apply(null, { length: 12 })).map(function (item) {
      const monthNum = +item + 1;
      if (monthNum < 10) {
        return { text: "0" + monthNum, value: "0" + monthNum };
      } else {
        return {
          text: monthNum.toString(),
          value: monthNum.toString()
        };
      }
    });
    _M && data.columns.push(month); //生成月列

    day && data.columns.push(day); //生成日列
  }

  // 生成时间相关的列（小时、分钟、秒）
  if (props.type === "datetime" || props.type === "time" || props.type === "time-full" || props.type === "time-short") {
    // 修正小时数组生成逻辑
    let hour = Array.from({ length: 24 }, (_, i) => {
      const value = i.toString().padStart(2, '0');
      return { text: value, value: value };
    });

    // 修正分钟数组生成逻辑
    let mi = Array.from({ length: 60 }, (_, i) => {
      const value = i.toString().padStart(2, '0');
      return { text: value, value: value };
    });

    // 修正秒钟数组生成逻辑（仅在需要秒的类型中使用）
    let ss = Array.from({ length: 60 }, (_, i) => {
      const value = i.toString().padStart(2, '0');
      return { text: value, value: value };
    });

    _h && data.columns.push(hour); //生成小时列
    _m && data.columns.push(mi); //生成分钟列

    // 只有完整时间类型才包含秒
    if (props.type === "datetime" || props.type === "time" || props.type === "time-full") {
      _s && data.columns.push(ss); //生成秒钟列
    }
  }

  console.log("列数据----------》", data);
}

function getCountDays (year, month) {
  if (!year || !month || month < 1 || month > 12) {
    console.warn('Invalid year or month:', year, month);
    return 31; // 默认返回31天
  }
  let day = new Date(year, month, 0);
  return day.getDate();
}

// 关闭弹框
function confirmOn () {
  data.isPicker = false;  // 直接关闭弹窗
  emit("changeValue");
}

// 时间选择器确定 值改变
function onConfirm ({ selectedValues }) {
  let endval;
  try {
    if (props.type === "datetime" || props.type === "date") {
      const year = parseInt(selectedValues[0]);
      const month = parseInt(selectedValues[1]);
      const day = parseInt(selectedValues[2]);

      // 验证日期的有效性
      const maxDays = getCountDays(year, month);
      // 如果日期超过最大天数，使用1号
      const finalDay = day > maxDays ? '01' : selectedValues[2];

      if (props.type === "datetime") {
        const formattedDate = `${year}-${selectedValues[1]}-${finalDay} ${selectedValues[3]}:${selectedValues[4]}:${selectedValues[5]}`;
        const date = new Date(formattedDate.replace(/-/g, '/'));

        if (isNaN(date.getTime())) {
          console.warn('Invalid date selected');
          return;
        }

        endval = formattedDate;
      } else {
        endval = `${year}-${selectedValues[1]}-${finalDay}`;
      }
    } else if (props.type === "time" || props.type === "time-full") {
      // 完整时间格式：HH:mm:ss
      endval = `${selectedValues[0]}:${selectedValues[1]}:${selectedValues[2]}`;
    } else if (props.type === "time-short") {
      // 简化时间格式：HH:mm
      endval = `${selectedValues[0]}:${selectedValues[1]}`;
    }

    data.lastConfirmedValue = endval;
    data.isPicker = false;
    emit("confirm", endval);
    emit("changeValue");
  } catch (error) {
    console.error('Error in date confirmation:', error);
  }
}

// 时间选择器关闭 值不改变并关闭弹框
function cancelOn () {
  data.isPicker = false;  // 直接关闭弹窗
  emit("changeValue");
}
</script>
<style lang="scss" scoped>
.wrapper {}
</style>
