<template>
  <div class="detail">
    <div class="top-block">
      <div class="title" v-if="state_list[info.order_status]"
        style="border-bottom: 1px solid #f6f6f6; padding-bottom: 10px; cursor: pointer;" @click="showOrderProgress">
        <div style="height: 10px;"></div>
        <text>订单{{ state_list[info.order_status].title }} </text> <img src="../../../../static/img/路径 6.svg"
          mode="aspectFit" />
      </div>
      <div class="base-flex icon-block" v-if="info.order_status === 1 || info.order_status === 0">
        <div class="" @click="edit_click(1)">
          <img src="../../../../static/img/quxiao.svg" mode="aspectFit" />
          <div>取消预定</div>
        </div>
        <div class="" @click="edit_click(2)">
          <img src="../../../../static/img/确认.svg" mode="aspectFit" />
          <div>确认核销</div>
        </div>
        <div class="" @click="edit_click(3)">
          <img src="../../../../static/img/daiwo.svg" mode="aspectFit" />
          <div>代我核销</div>
        </div>
      </div>
      <div class="base-flex icon-block" style="padding-left: 0; color: rgba(23, 26, 29, 0.6)"
        v-if="info.order_status == 3">
        用餐结束，感谢您的支持，厉行节约，拒绝浪费
      </div>
      <div class="base-flex icon-block" style="padding-left: 0; color: rgba(23, 26, 29, 0.6)"
        v-if="info.order_status == 4">
        您已取消了本次订单，退款金额24小时内原路返回
      </div>
      <div style="padding-left: 0; color: #07C160; font-size: 13px; margin-top: 12px;" v-if="info.order_status == 5">
        退款成功{{ info.pay_amount }}元
      </div>
    </div>
    <div class="dish-block">
      <div class="header">
        菜品信息
      </div>
      <div class="title">
        {{ info.dininghall_title }}-{{ info.mealtime_title }}
      </div>
      <div class="list-block" v-if="info.dishess">
        <div class="base-flex dish-item" v-for="(el, index) in displayedDishes" :key="index + el.title">
          <div class="dish-img" v-if="el.image">
            <img :src="el.image" alt="" />
          </div>
          <div class="dish-img_wu" v-else>
            <div>{{ el.title ? el.title.slice(0, 1) : "无" }}</div>
          </div>
          <div class="dish-text">
            <div class="dish-name">
              {{ el.title }}
              <div class="base-flex dish-price" v-if="el.price">
                ¥{{ el.price * el.quantity }}
              </div>
            </div>
            <div class="dish-count">
              <span>x{{ el.quantity }}</span><span v-if="el.price && el.quantity > 1">￥{{ el.price }}</span>
            </div>
            <div class="dish-count">
              <div>
                <span style="font-size: 13px" v-show="el.weight">重量：{{ el.weight }}g</span>
              </div>
              <!-- <span
                :class="{
                  tag1: el.evaluate * 1,
                  tag: !(el.evaluate * 1),
                }"
                @click="onEvaluateClick(el)"
                >{{ el.evaluate * 1 ? "已评价" : "评价" }}</span
              > -->
            </div>
          </div>
        </div>

        <!-- 展开/收起按钮 -->
        <div :class="['expand-toggle', { 'expanded': !isDishesCollapsed }]" @click="toggleDishesExpand()"
          v-if="info.dishess && info.dishess.length > 3">
          <span class="toggle-content">
            <span class="toggle-text">{{ isDishesCollapsed ? `展开菜品共${info.dishess.length}条数据` : '收起部分菜品' }}</span>
            <img src="../../../../static/img/icon_arrow.png" class="toggle-icon"
              :class="{ 'rotated': !isDishesCollapsed }" />
          </span>
        </div>
      </div>
      <!-- 实付款 - 放在最前面 -->
      <div class="base-flex sub-row discount-summary">
        <div class="discount-left">
          <text style="font-size: 16px;">实付款</text>
        </div>
        <text space="ensp" style="font-size: 15px;">¥{{ info.pay_amount }}</text>
      </div>
      <!-- 总优惠 - 可点击展开 -->
      <div class="base-flex sub-row discount-summary" @click="toggleDiscountDetail">
        <div class="discount-left">
          <text>总优惠</text>
          <img src="../../../../static/img/icon_arrow.png" class="discount-arrow"
            :class="{ 'rotated': isDiscountExpanded }" />
        </div>
        <text space="ensp" style="color: #EE0A24;font-size: 15px;">-¥{{ info.discount_amount.toFixed(2) }}</text>
      </div>

      <!-- 优惠明细 - 展开时显示 -->
      <div class="discount-details" v-if="isDiscountExpanded">
        <div class="base-flex sub-row detail-item">
          <text>总价</text>
          <text space="ensp" style="font-size: 12px;color: #323233;">¥{{ info.total_amount.toFixed(2) }}</text>
        </div>
        <div class="base-flex sub-row detail-item">
          <text>减免金额</text>
          <text space="ensp" style="font-size: 12px;color: #EE0A24;">-¥{{ info.deduction_amount.toFixed(2) }}</text>
        </div>
        <div class="base-flex sub-row detail-item">
          <text>优惠券折扣</text>
          <text space="ensp" style="font-size: 12px;color: #EE0A24;">-¥{{ info.coupon_amount.toFixed(2) }}</text>
        </div>
      </div>
    </div>
    <div class="text-block">
      <div class="title">订单信息</div>
      <div class="base-flex text-item" v-for="(item, i) in filteredTextList" :key="i" style="margin-top: 10px;">
        <text class="left">{{ item.title }}</text>
        <text class="right">{{
          (item.list ? item.list[info[item.key]] : info[item.key])
        }}</text>
      </div>
    </div>

    <!-- 订单进度弹窗 -->
    <van-popup v-model:show="showProgressPopup" position="bottom" :style="{ height: '372px' }" round closeable
      @close="closeProgressPopup">
      <div class="progress-popup">
        <div class="progress-header">
          <h3>订单追踪</h3>
        </div>
        <div class="progress-content">
          <van-steps :active="currentStepIndex + 1" direction="vertical">
            <van-step v-for="(step, index) in orderSteps" :key="index">
              <template #active-icon>
                <img src="../../../../static/img/椭圆形.svg" alt="">
              </template>
              <template #inactive-icon>
                <img src="../../../../static/img/tuoyuan.svg" alt="">
              </template>
              <div class="div">
                <div class="text">
                  {{ step.title }}
                </div>
                <div class="text-1">
                  {{ step.time }}
                </div>
              </div>
            </van-step>
          </van-steps>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, onBeforeMount, getCurrentInstance, createApp, computed } from "vue";
import { showDialog, showToast, showConfirmDialog } from "vant";
const route = useRoute();
const router = useRouter();
let { proxy } = getCurrentInstance();
const obj = ref({});
import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();
// 菜品展开/收起状态控制
const isDishesCollapsed = ref(true); // true表示收起状态，只显示前3个菜品

// 优惠明细展开/收起状态控制
const isDiscountExpanded = ref(false);

// 订单进度弹窗控制
const showProgressPopup = ref(false);

// 计算属性：控制显示的菜品数量
const displayedDishes = computed(() => {
  if (!info.value.dishess || info.value.dishess.length <= 3) {
    return info.value.dishess || [];
  }
  if (isDishesCollapsed.value) {
    return info.value.dishess.slice(0, 3);
  }
  return info.value.dishess;
});

// 计算属性：过滤空值的订单信息列表
const filteredTextList = computed(() => {
  return text_list.value.filter(item => {
    const value = info.value[item.key];
    // 检查值是否存在且不为空
    if (value === null || value === undefined || value === '') {
      return false;
    }
    // 如果有list选项，还需要检查对应的选项是否存在
    if (item.list && (value < 0 || value >= item.list.length)) {
      return false;
    }
    return true;
  });
});

// 计算属性：订单步骤数据
const orderSteps = computed(() => {
  // 根据订单状态定义步骤
  // 0: 待付款 1: 待使用 2: 待评价 3: 已完成 4: 已取消 5: 已退款

  const status = info.value.order_status || 0;
  const steps = [];

  // 所有状态都从订单提交成功开始
  steps.push({
    title: "订单提交成功",
    time: info.value.order_date || info.value.created_at || "",
    status: "finished"
  });

  // 待付款状态（0）：显示订单提交成功 + 订单待付款
  steps.push({
    title: "订单待付款",
    time: status >= 1 ? (info.value.pay_time || "") : "",
    status: status >= 1 ? "finished" : "process"
  });

  if (status === 0) {
    return steps;
  }

  // 待使用状态（1）及以上：显示订单已支付
  steps.push({
    title: "订单已支付",
    time: status >= 1 ? (info.value.pay_time || "") : "",
    status: status >= 1 ? "finished" : "waiting"
  });

  // 待使用状态（1）：显示订单待使用
  steps.push({
    title: "订单待使用",
    time: status >= 2 ? (info.value.use_time || info.value.complete_time || "") : "",
    status: status >= 2 ? "finished" : (status === 1 ? "process" : "waiting")
  });

  if (status === 1) {
    return steps;
  }

  // 待评价状态（2）及以上：显示订单已使用
  steps.push({
    title: "订单已使用",
    time: status >= 2 ? (info.value.use_time || info.value.complete_time || "") : "",
    status: status >= 2 ? "finished" : "waiting"
  });

  if (status === 2) {
    return steps;
  }

  // 已完成状态（3）及以上：显示订单已完成
  steps.push({
    title: "订单已完成",
    time: status >= 3 ? (info.value.evaluate_time || info.value.complete_time || "") : "",
    status: status >= 3 ? "finished" : "waiting"
  });

  if (status === 3) {
    return steps;
  }

  // 已取消状态（4）：显示订单已取消
  if (status === 4) {
    steps.push({
      title: "订单已取消",
      time: info.value.cancel_time || "",
      status: "finished"
    });
    return steps;
  }

  // 已退款状态（5）：显示订单已取消 + 订单已退款
  if (status === 5) {
    steps.push({
      title: "订单已取消",
      time: info.value.cancel_time || "",
      status: "finished"
    });
    steps.push({
      title: "订单已退款",
      time: info.value.refund_time || "",
      status: "finished"
    });
    return steps;
  }

  return steps;
});

// 计算属性：当前步骤索引
const currentStepIndex = computed(() => {
  // 根据订单状态计算当前激活的步骤索引
  // 0: 待付款 1: 待使用 2: 待评价 3: 已完成 4: 已取消 5: 已退款

  const status = info.value.order_status || 0;

  // 各状态对应的当前激活步骤索引：
  // 0: 待付款 -> 2个步骤，当前激活步骤1（订单待付款）
  // 1: 待使用 -> 4个步骤，当前激活步骤3（订单待使用）
  // 2: 待评价 -> 5个步骤，当前激活步骤4（订单已使用）
  // 3: 已完成 -> 6个步骤，当前激活步骤5（订单已完成）
  // 4: 已取消 -> 7个步骤，当前激活步骤6（订单已取消）
  // 5: 已退款 -> 8个步骤，当前激活步骤7（订单已退款）

  const stepIndexMap = {
    0: 1, // 待付款：当前激活"订单待付款"
    1: 3, // 待使用：当前激活"订单待使用"
    2: 4, // 待评价：当前激活"订单已使用"
    3: 5, // 已完成：当前激活"订单已完成"
    4: 6, // 已取消：当前激活"订单已取消"
    5: 7  // 已退款：当前激活"订单已退款"
  };

  return stepIndexMap[status] || 0;
});

// 切换菜品展开/收起状态
const toggleDishesExpand = () => {
  isDishesCollapsed.value = !isDishesCollapsed.value;
};

// 切换优惠明细展开/收起状态
const toggleDiscountDetail = () => {
  isDiscountExpanded.value = !isDiscountExpanded.value;
};

// 显示订单进度弹窗
const showOrderProgress = () => {
  showProgressPopup.value = true;
};

// 关闭订单进度弹窗
const closeProgressPopup = () => {
  showProgressPopup.value = false;
};

const info = ref({
  number: "",
  dininghall_title: "",
  repast_title: "",
  pay_type: "",
  order_status: null,
  money: 60,
  subsidy_money: 8,
  virtual_balance: 0,
  real_balance: 200,
  created_at: "",
  dishess: [],
});
const text_list = ref([
  {
    title: '配送地址',
    key: 'delivery_address'
  },
  {
    title: '配送时间',
    key: 'estimated_delivery_time'
  },
  {
    title: '份数',
    key: 'total_qty'
  },
  {
    title: '备注',
    key: 'remark'
  },
  {
    title: '取餐码',
    key: 'pickup_code'
  },
  {
    title: '就餐方式',
    key: "dine_type",
    list: ["堂食", "留餐", "打包", "外卖"],
  },
  {
    title: '支付方式',
    key: 'pay_method',
    list: ["账户余额支付", "支付宝在线支付", "微信在线支付"],
  },
  {
    title: '订单号码',
    key: 'order_no'
  },
  {
    title: '下单时间',
    key: 'created_at'
  },
  {
    title: '就餐日期',
    key: 'dining_date'
  },
]);

//0: 待付款 1: 待使用 2: 待评价 3: 已完成 4: 已取消 5: 已退款
const state_list = ref([
  {
    title: "待付款",
    color: "rgba(23, 26, 29, 0.60)",
  },
  {
    title: "待使用",
    color: "rgba(23, 26, 29, 0.60)",
  },
  {
    title: "待评价",
    color: "rgba(22, 120, 255, 100)",
  },
  {
    title: "已完成",
    color: "rgba(23, 26, 29, 0.60)",
  },
  {
    title: "已取消",
    color: "rgba(23, 26, 29, 0.60)",
  },
  {
    title: "已退款",
    color: "rgba(23, 26, 29, 0.60)",
  },
]);
onBeforeMount(() => {
  get_info();
});
const get_info = () => {
  proxy
    .$get("order/get_info", route.query)
    .then((res) => {
      if (res.code === 200) {
        res.data.dishess = JSON.parse(res.data.order_items);
        info.value = res.data;
      } else {
        throw res.msg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    });
};
const back_money = (url, obj) => {
  proxy
    .$post(url, obj)
    .then((res) => {
      if (res.code === 200) {
        get_info();
      } else {
        throw res.msg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    });
};
// const onEvaluateClick = (dish) => {
//   if (!(dish.evaluate * 1)) {
//     router.push({
//       path: "/bill_evaluate",
//       query: { bill_id: route.query.id, dishes_id: dish.id },
//     });
//   }
// };
const edit_click = (id) => {
  switch (id) {
    case 1:
      showConfirmDialog({
        title: '取消预定',
        message: '确定将立即取消，退款原路返回，此操作不可逆，是否继续？',
        confirmButtonText: '确认取消',
        cancelButtonText: '取消',
        confirmButtonColor: '#ee0a24',
      })
        .then(() => {
          let obj = {
            id: route.query.id,
            order_status: 4
          }
          back_money("order/post_modify", obj);
        })
        .catch(() => {
        });

      break;
    case 2:
      showConfirmDialog({
        title: '确认核销',
        message: '确定将立即完成，是否确认核销订单，此操作不可逆，是否继续？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        confirmButtonColor: '#1989FA',
      })
        .then(() => {
          let obj = {
            id: route.query.id,
            order_status: 3
          }
          back_money("order/post_modify", obj);
        })
        .catch(() => {
        });
      break;
    case 3:
        showDialog({
          title: "代餐码",
          message: `您的代餐码为：${info.value.pickup_code
            }，注意请勿随意泄露`,
          confirmButtonText: "发送同事",
          showCancelButton: true,
        }).then(() => {
          proxy.$_dd.biz.ding.create({
            users: [],
            corpId: app.corpId,
            type: 1,
            alertType: 2,
            alertDate: {
              format: "yyyy-MM-dd HH:mm",
              value: "",
            },
            attachment: {
              images: [],
            },
            text: `您的代餐码为：${info.value.pickup_code
              }，注意请勿随意泄露`,
            bizType: 0,
            taskInfo: {},
            confInfo: {},
            onSuccess: function (res) {
              // 调用成功时回调
              // console.log(res)
            },
            onFail: function (err) {
              // 调用失败时回调
              // console.log(err)
            },
          });
        });
      break;
  }
};
</script>
<style lang="scss" scoped>
.detail {
  padding-bottom: 60px;

  .top-block {
    background: #ffffff;
    margin: 14px 16px 15px 16px;
    padding: 7px 1px 16px 15px;
    border-radius: 8px;

    image {
      width: 18px;
      height: 18.18px;
    }

    .icon-block {
      color: rgba(23, 26, 29, 100);
      font-size: 12px;
      text-align: center;
      margin-top: 13px;
      padding: 0 25px;

      img {
        width: 24px;
        height: 22px;
        margin-bottom: 4px;
      }
    }
  }

  .dish-block {
    background-color: #ffffff;
    margin: 16px;
    border-radius: 8px;
    padding: 0 16px;
    border-bottom: 1px solid #f6f6f6;

    .dish-item {
      margin: 16px 0;

      /* 菜品项之间的分隔线样式 - 只在多个菜品时显示 */
      &:not(:last-child) {
        border-bottom: 1px solid rgba(126, 134, 142, 0.16);
        padding-bottom: 16px;
      }

      /* 单个菜品时不显示分隔线 */
      &:only-child {
        border-bottom: none;
        padding-bottom: 0;
      }

      .dish-img {
        margin-right: 8px;

        img {
          width: 57px;
          height: 57px;
          border-radius: 8px;
        }
      }

      .dish-img_wu {
        margin-right: 8px;

        div {
          width: 57px;
          height: 57px;
          font-size: 30px;
          border-radius: 8px;
          line-height: 58px;
          text-align: center;
          color: #ffffff;
          background: #1678ff;
        }
      }
    }

    // .list-block {
    //   padding-bottom: -10px;
    //   // border-bottom: 1px solid rgba(126, 134, 142, 0.16);
    // }

    // 展开/收起按钮样式
    .expand-toggle {
      text-align: center;
      // padding: 12px 0;
      cursor: pointer;
      background: #fff;
      transition: all 0.3s ease;
      // border-top: 1px solid rgba(126, 134, 142, 0.16);
      // margin-top: 12px;

      .toggle-content {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: rgba(23, 26, 29, 0.6);
        font-size: 13px;

        .toggle-text {
          margin-right: 6px;
        }

        .toggle-icon {
          width: 7px;
          height: 11px;
          transform: rotate(90deg);
          transition: transform 0.3s ease;

          &.rotated {
            transform: rotate(270deg);
          }
        }
      }

      &:active {
        .toggle-content {
          opacity: 0.8;
        }
      }
    }

    .dish-text {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .dish-name {
        color: rgba(23, 26, 29, 100);
        font-size: 13px;
        display: flex;
        justify-content: space-between;
      }

      .dish-count {
        color: rgba(23, 26, 29, 0.6);
        font-size: 12px;
        display: flex;
        justify-content: space-between;

        // .tag {
        //   display: inline-block;
        //   border-radius: 4px;
        //   border: 1px solid rgba(0, 0, 0, 0.18);
        //   font-size: 13px;
        //   color: #000;
        //   padding: 4px 8px;
        // }

        // .tag1 {
        //   display: inline-block;
        //   border-radius: 4px;
        //   border: none;
        //   font-size: 13px;
        //   color: rgba(0, 0, 0, 0.48);
        //   padding: 4px 8px;
        //   background: rgba(0, 0, 0, 0.07);
        // }
      }
    }

    .dish-price {
      align-items: center;
      font-size: 14px;
    }

    .sub-row {
      color: rgba(23, 26, 29, 100);
      font-size: 12px;
      margin: 13px 0;
    }

    .total-row {
      color: rgba(23, 26, 29, 100);
      font-size: 12px;
      text-align: right;
      margin: 13px 0;
    }

    /* 优惠明细相关样式 */
    .discount-summary {
      cursor: pointer;
      // padding: 8px 12px;
      border-radius: 6px;
      // transition: background-color 0.3s ease;

      &:active {
        // background-color: rgba(246, 246, 246, 0.8) !important;
      }

      .discount-left {
        display: flex;
        align-items: center;

        .discount-arrow {
          width: 7px;
          height: 11px;
          margin-left: 6px;
          transform: rotate(90deg);
          transition: transform 0.3s ease;

          &.rotated {
            transform: rotate(270deg);
          }
        }
      }
    }

    .discount-details {
      background-color: #f7f8fa;
      border-radius: 6px;
      padding: 8px 12px;
      margin: 10px 0;
      // padding-bottom: -10px;
      // margin-bottom: ;

      .detail-item {
        margin: 8px 0;
        font-size: 12px;
        color: rgba(23, 26, 29, 0.8);

        &:first-child {
          margin-top: 4px;
        }

        &:last-child {
          margin-bottom: 4px;
        }
      }
    }
  }

  .text-block {
    background: #ffffff;
    margin: 16px;
    padding: 0 16px;
    border-radius: 8px;
    // border: 1px solid #f6f6f6;

    .title {
      color: rgba(25, 31, 37, 100);
      font-size: 16px;
      font-weight: 500;
      padding: 16px 0 12px 0;
      border-bottom: 1px solid rgba(126, 134, 142, 0.16);
      margin-bottom: 0;
    }

    .text-item {
      font-size: 14px;
      // padding: 4px 0;
      margin: 0;
      // border-bottom: 1px solid rgba(126, 134, 142, 0.08);

      &:last-child {
        border-bottom: none;
        padding-bottom: 16px;
      }

      .left {
        color: rgba(23, 26, 29, 0.6);
        font-size: 14px;
        line-height: 20px;
        min-width: 80px;
      }

      .right {
        color: rgba(23, 26, 29, 1);
        font-size: 14px;
        line-height: 20px;
        //word-wrap: break-all;
        max-width: 70%;
        // text-align: right;
        flex: 1;
      }
    }
  }

  .header {
    padding-top: 12px;
    padding-bottom: 12px;
    font-size: 16px;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: 0px;
    border-bottom: 1px solid rgba(126, 134, 142, 0.16);
  }

  .dish-block .title {
    color: rgba(25, 31, 37, 100);
    font-size: 16px;
    padding: 12px 0;
    // border-bottom: 1px solid rgba(126, 134, 142, 0.16);
  }

  .titleA {
    color: rgba(25, 31, 37, 100);
    font-size: 16px;
    margin: 14px 16px 15px 16px;

    // padding: 7px 1px 16px 15px;
    // padding: 12px 0;
    // border-bottom: 1px solid rgba(126, 134, 142, 0.16);
  }
}

.base-flex {

  display: flex;
  justify-content: space-between;
}

.dish-price {
  align-items: center;
  font-size: 15px;
}

.dish-count {
  color: rgba(23, 26, 29, 0.6);
  font-size: 5px;
}

/* 订单进度弹窗样式 */
.progress-popup {
  padding: 24px 20px 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .progress-header {
    text-align: center;
    margin-bottom: 24px;
    // border-bottom: 1px solid #f0f0f0;
    // padding-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 17px;
      font-weight: 500;
      color: #323233;
    }
  }

  .progress-content {
    flex: 1;
    overflow-y: auto;
    // padding: 0 0px;

    .div {
      width: 320px;
      height: 44px;
      display: flex;
      flex-direction: column;
      justify-content: start;
      align-items: start;
      row-gap: 8px;
      column-gap: 8px;

      .text {
        flex-shrink: 0;
        align-self: stretch;
        width: 320px;
        height: 20px;
        color: #323233;
        font-family: "PingFang SC";
        font-size: 14px;
        line-height: 20px;
      }

      .text-1 {
        flex-shrink: 0;
        align-self: stretch;
        width: 320px;
        height: 16px;
        color: #969799;
        font-family: "PingFang SC";
        font-size: 12px;
        line-height: 16px;
      }
    }

  }
}

/* 为订单状态标题添加点击效果 */
.title {
  transition: background-color 0.2s ease;

  &:active {
    background-color: rgba(0, 0, 0, 0.05);
  }
}
</style>
