# yhc-segmented-control 分段器组件

一个功能丰富的分段器组件，支持单选、多选、图标、徽标等多种功能，以及条件显示子表单。

## 功能特性

- ✅ 支持单选和多选模式
- ✅ 支持图标显示
- ✅ 支持徽标显示
- ✅ 支持滑动指示器
- ✅ 支持禁用状态
- ✅ 支持多种尺寸和类型
- ✅ 支持自定义样式
- ✅ 响应式设计
- ✅ 完整的事件支持
- ✅ 支持条件显示子表单
- ✅ 与 yhc-form 组件体系完全兼容

## 基础用法

### 简单用法
```javascript
// 基础配置
const config = {
  key: "segment",
  options: ["选项1", "选项2", "选项3", "选项4"]
};

// 表单数据
const form = {
  segment: "选项1"
};
```

### 对象数据
```javascript
const config = {
  key: "segment",
  options: [
    { text: "选项1", value: "option1" },
    { text: "选项2", value: "option2" },
    { text: "选项3", value: "option3" },
    { text: "选项4", value: "option4" }
  ]
};
```

### 带图标
```javascript
const config = {
  key: "segment",
  options: [
    { text: "首页", value: "home", icon: "home-o" },
    { text: "订单", value: "bill", icon: "records-o" },
    { text: "工作台", value: "app", icon: "setting-o" },
    { text: "我的", value: "user", icon: "user-o" }
  ]
};
```

### 多选模式
```javascript
const config = {
  key: "segments",
  multiple: true,
  options: ["选项1", "选项2", "选项3", "选项4"]
};

// 表单数据
const form = {
  segments: ["选项1", "选项3"]
};
```

## 配置参数

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| label | 标题文本 | String | - |
| key | 表单字段key | String | - |
| disabled | 是否禁用 | Boolean | false |
| block | 是否为块级元素 | Boolean | false |
| multiple | 是否支持多选 | Boolean | false |
| options | 选项数据 | Array | [] |
| textKey | 文本字段key | String | "text" |
| valueKey | 值字段key | String | "value" |
| iconKey | 图标字段key | String | "icon" |
| badgeKey | 徽标字段key | String | "badge" |
| disabledKey | 禁用字段key | String | "disabled" |
| size | 尺寸 | String | "normal" |
| type | 类型 | String | "default" |
| round | 是否圆角 | Boolean | false |
| showIndicator | 是否显示滑动指示器 | Boolean | true |
| iconSize | 图标尺寸 | String | "16px" |
| activeColor | 激活颜色 | String | "#1989fa" |
| inactiveColor | 非激活颜色 | String | "#969799" |
| backgroundColor | 背景颜色 | String | "#f7f8fa" |
| onChange | 变化回调 | Function | null |
| description | 描述文本 | String | - |
| required | 是否必填 | Boolean | false |
| rules | 验证规则 | Array | [] |

## 尺寸类型

### size 尺寸
- `small`: 小尺寸
- `normal`: 正常尺寸（默认）
- `large`: 大尺寸

### type 类型
- `default`: 默认类型
- `primary`: 主要类型
- `success`: 成功类型
- `warning`: 警告类型
- `danger`: 危险类型

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 选择变化时触发 | (value, item, index) |

## 使用示例

### 在表单中使用
```vue
<template>
  <yhc-form :config="formConfig" :form="form" />
</template>

<script setup>
const form = reactive({
  segment: "option1"
});

const formConfig = {
  form: [
    {
      type: "yhc-segmented-control",
      key: "segment",
      label: "选择选项",
      options: [
        { text: "选项1", value: "option1" },
        { text: "选项2", value: "option2" },
        { text: "选项3", value: "option3" },
        { text: "选项4", value: "option4" }
      ]
    }
  ]
};
</script>
```

### 直接使用
```vue
<template>
  <yhc-segmented-control :config="config" :form="form" @change="handleChange" />
</template>

<script setup>
const form = reactive({
  segment: "option1"
});

const config = {
  key: "segment",
  label: "分段选择",
  options: [
    { text: "4个选项(受控)", value: "option1" },
    { text: "默认选中第二个", value: "option2" },
    { text: "选项3", value: "option3" },
    { text: "选项4", value: "option4" }
  ],
  onChange: (value, item, index) => {
    console.log("选择变化:", value, item, index);
  }
};

const handleChange = (value, item, index) => {
  console.log("事件回调:", value, item, index);
};
</script>
```

## 高级用法

### 带徽标的分段器
```javascript
const config = {
  key: "segment",
  options: [
    { text: "选项1", value: "option1", badge: "新" },
    { text: "选项2", value: "option2", badge: 5 },
    { text: "选项3", value: "option3" },
    { text: "选项4", value: "option4", badge: "热" }
  ]
};
```

### 禁用某些选项
```javascript
const config = {
  key: "segment",
  options: [
    { text: "选项1", value: "option1" },
    { text: "选项2", value: "option2", disabled: true },
    { text: "选项3", value: "option3" },
    { text: "选项4", value: "option4" }
  ]
};
```

### 自定义样式
```javascript
const config = {
  key: "segment",
  size: "large",
  type: "primary",
  round: true,
  activeColor: "#07c160",
  backgroundColor: "#f0f0f0",
  options: ["选项1", "选项2", "选项3", "选项4"]
};
```

## 子表单功能

组件支持根据选中的值条件显示不同的子表单，类似于 `yhc-switch` 组件的功能。

### 使用 map 配置不同选项对应的子表单

```javascript
{
  label: "分段器",
  key: "segmented-key",
  component: "yhc-segmented-control",
  options: [
    { text: "选项1", value: "option1" },
    { text: "选项2", value: "option2" },
    { text: "选项3", value: "option3" }
  ],
  child: {
    map: {
      "option1": [
        // 当选中"选项1"时显示的子表单配置
        {
          label: "子表单项1",
          key: "sub-field-1",
          component: "yhc-input",
          // ...其他配置
        }
      ],
      "option2": [
        // 当选中"选项2"时显示的子表单配置
      ],
      "option3": [
        // 当选中"选项3"时显示的子表单配置
      ]
    }
  }
}
```

### 使用 showMode 配置条件显示子表单

```javascript
{
  label: "分段器",
  key: "segmented-key",
  component: "yhc-segmented-control",
  options: [
    { text: "显示更多", value: "show" },
    { text: "隐藏更多", value: "hide" }
  ],
  child: {
    showMode: "show", // 当选中值为"show"时显示子表单
    form: [
      // 当条件满足时显示的子表单配置
      {
        label: "附加信息",
        key: "additional-info",
        component: "yhc-input",
        // ...其他配置
      }
    ]
  }
}
```

## 注意事项

1. 组件会自动注册到全局，可以直接在模板中使用
2. 支持 v-model 双向绑定
3. 多选模式下返回数组，单选模式下返回单个值
4. 滑动指示器仅在单选模式下显示
5. 子表单功能仅在单选模式下可用
5. 组件完全响应式，支持动态更新配置和数据
