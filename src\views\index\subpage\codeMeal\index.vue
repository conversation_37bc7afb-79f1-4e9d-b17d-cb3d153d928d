<template>
  <div class="yp_qr_auth" v-if="isShow">
    <!-- 页面模式切换：fixed为固定金额，custom为自定义金额 -->
    <div v-if="mode === 'fixed'">
      <div>
        <div
          style="
            background: #fff;
            padding: 16px 0;
            padding-top: 40px;
            border-radius: 8px;
            margin-bottom: 24px;
          "
        >
          <div style="font-size: 16px; color: #999">支付金额</div>
          <div style="font-size: 32px; font-weight: bold; margin: 16px 0">
            ￥{{ info.money }}
          </div>
          <div
            style="
              display: flex;
              justify-content: space-between;
              padding: 0 32px;
              font-size: 15px;
            "
          >
            <span>餐厅</span><span>{{ info.dininghall_title }}</span>
          </div>
          <div
            style="
              display: flex;
              justify-content: space-between;
              padding: 0 32px;
              font-size: 15px;
              margin-top: 8px;
            "
          >
            <span>窗口</span><span>{{ info.window_title }}</span>
          </div>
          <div
            style="
              display: flex;
              justify-content: space-between;
              padding: 0 32px;
              font-size: 15px;
              margin-top: 8px;
            "
          >
            <span>余额</span><span>￥{{ info.balance }}</span>
          </div>
        </div>
        <van-button
          type="primary"
          style="margin-bottom: 12px; width: 92%"
          :disabled="loading"
          @click="payFixed"
          >确认支付</van-button
        >
        <van-button
          type="default"
          style="margin-bottom: 12px; width: 92%"
          @click="cancelPay"
          >取消交易</van-button
        >
      </div>
    </div>
    <div v-else>
      <div class="custom-pay-page">
        <div class="bank-info">
          <van-image width="34" height="34" radius="4" src="/index/dindn.png" />
          <span class="bank-name"
            >{{ info.dininghall_title }}({{ info.window_title }})</span
          >
        </div>
        <!-- 输入金额区域 -->
        <div class="amount-input-area">
          <div class="amount-input-label">付款金额</div>
          <div class="amount-input-box" @click="showKeypad = true">
            <span class="amount-input-cny">￥</span>
            <span v-if="inputAmount.length" class="amount-input-value">{{
              inputAmount
            }}</span>
            <span v-else class="amount-input-placeholder">请输入金额</span>
          </div>
        </div>

        <!-- 弹出式数字键盘和遮罩 -->
        <div v-if="showKeypad" class="keypad-overlay">
          <div class="keypad-mask" @click="closeKeypad"></div>
          <div class="popup-keypad">
            <!-- Row 1 -->
            <button class="key-btn" @click="onKeyPress('1')">1</button>
            <button class="key-btn" @click="onKeyPress('2')">2</button>
            <button class="key-btn" @click="onKeyPress('3')">3</button>
            <button class="key-btn delete-key-btn" @click="onKeyPress('⌫')">
              <van-image width="34" height="34" radius="4" src="/index/del.png" />
            </button>
            <!-- Row 2 -->
            <button class="key-btn" @click="onKeyPress('4')">4</button>
            <button class="key-btn" @click="onKeyPress('5')">5</button>
            <button class="key-btn" @click="onKeyPress('6')">6</button>
            <!-- Confirm button (spans 3 rows) -->
            <button
              class="confirm-btn"
              :disabled="!inputAmount"
              @click="payCustomAndClose"
            >
              付款
            </button>
            <!-- Row 3 -->
            <button class="key-btn" @click="onKeyPress('7')">7</button>
            <button class="key-btn" @click="onKeyPress('8')">8</button>
            <button class="key-btn" @click="onKeyPress('9')">9</button>
            <!-- Row 4 -->
            <button class="key-btn zero-btn" @click="onKeyPress('0')">0</button>
            <button class="key-btn" @click="onKeyPress('.')">.</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import { showToast } from "vant";
import { useLoginStore } from "@/store/dingLogin";
import { useRouter, useRoute } from "vue-router";

const app = useLoginStore();
const router = useRouter();
const route = useRoute();
let loading = ref(false);
let isShow = ref(false);
let info = ref({
  money: 0,
  balance: 0,
});
let { proxy } = getCurrentInstance();

let getInfo = () => {
  loading.value = true;
  proxy
    .$post("dine/post_dine", {
      userid: app.loginData.user_info.userid,
      ...route.query,
      dine_pay_type: 15,
    })
    .then((res) => {
      if (!res.errcode) {
        info.value = res.result;
        let arr = ["fixed", "custom", "confirm"];
        mode.value = arr[res.result.notice_type];
        if (res.result.notice_type == 200) {
          router.push({
            path: "/codeMealSuccess",
            query: res.result,
          });
        }else{
         isShow.value = true;
        }
      } else {
        router.push({
        path: "/codeMealError",
        query: { err: res.errmsg },
      });
      }
    })
    .catch((err) => {
      showToast(err);
    })
    .finally(() => {
      setTimeout(() => {
        loading.value = false;
      }, 1500);
    });
};
localStorage.clear();

 if (!app.token) {
   router.push({ path: "/login", query: route.query });
 } else {
   getInfo();
 }
let confirm = (url, data) => {
  loading.value = true;
  proxy
    .$post(url, data)
    .then((res) => {
      if (!res.errcode) {
        inputAmount.value = "";
        if (res.result.notice_type == 200) {
          router.push({
            path: "/codeMealSuccess",
            query: res.result,
          });
        }
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      // showToast(err);
      router.push({
        path: "/codeMealError",
        query: { err: err },
      });
    })
    .finally(() => {
      setTimeout(() => {
        loading.value = false;
      }, 1500);
    });
};

// 页面模式：fixed-固定金额，custom-自定义金额
const mode = ref("fixed"); // 默认改为自定义金额模式方便调试

const inputAmount = ref("");
const showKeypad = ref(true); // 控制键盘弹出

// 固定金额支付
function payFixed() {
  confirm("dine/post_confirm_dine", {
    sub_code: info.value.sub_code,
    deviceSn: route.query.deviceSn,
  });
}
// 取消支付
function cancelPay() {
  showToast("已取消交易");
  proxy.$_dd.biz.navigation.close({
    message: "quit message", //退出信息，传递给openModal或者openSlidePanel的onSuccess函数的result参数
    onSuccess: function (result) {
      /**/
    },
    onFail: function () {},
  });
}
// 自定义金额支付
function payCustom() {
  if (!inputAmount.value || isNaN(Number(inputAmount.value))) {
    showToast("请输入正确金额");
    return;
  }
  confirm("dine/post_diy_dine", {
    sub_code: info.value.sub_code,
    deviceSn: route.query.deviceSn,
    money: inputAmount.value,
  });
}
// 数字键盘输入
function onKeyPress(key) {
  if (key === "⌫") {
    inputAmount.value = inputAmount.value.slice(0, -1);
  } else if (key === ".") {
    if (!inputAmount.value.includes(".")) inputAmount.value += ".";
  } else {
    if (inputAmount.value === "0" && key !== ".") {
      inputAmount.value = key;
    } else {
      inputAmount.value += key;
    }
  }
  if (inputAmount.value.includes(".")) {
    let arr = inputAmount.value.split(".");
    arr[1] = arr[1].slice(0, 2);
    inputAmount.value = arr.join('.')
  }
}
// 关闭键盘
function closeKeypad() {
  showKeypad.value = false;
}
// 自定义金额支付并关闭键盘
function payCustomAndClose() {
  payCustom();
  closeKeypad();
}
</script>

<style scoped lang="scss">
.yp_qr_auth {
  text-align: center;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0 0;
  .custom-pay-page {
    margin-top: 40px;
    padding: 0 16px;
  }
  .bank-info {
    display: flex;
    align-items: center;
    // justify-content: center;
    margin-bottom: 16px;
  }
  .bank-color-dot {
    width: 12px;
    height: 12px;
    background: #ff9800;
    border-radius: 2px;
    margin-right: 8px;
  }
  .bank-name {
    font-size: 16px;
    color: #333;
    margin-left: 8px;
  }

  // 输入金额区域样式
  .amount-input-area {
    background: #fff;
    border-radius: 8px;
    padding: 24px 20px 16px 20px;
    margin-bottom: 24px;
    text-align: left;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  }
  .amount-input-label {
    font-size: 15px;
    color: #999;
    margin-bottom: 8px;
  }
  .amount-input-box {
    display: flex;
    align-items: center;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    user-select: none;
    min-height: 40px;
  }
  .amount-input-cny {
    color: #333;
    font-size: 28px;
    margin-right: 4px;
  }
  .amount-input-value {
    color: #222;
    font-size: 28px;
    font-weight: bold;
  }
  .amount-input-placeholder {
    color: #ccc;
    font-size: 28px;
    font-weight: normal;
  }

  // 弹出式数字键盘样式
  .keypad-overlay {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    z-index: 1000;
  }
  .keypad-mask {
    flex: 1;
    // background: rgba(0, 0, 0, 0.3);
  }
  .popup-keypad {
    width: 100%;
    max-width: 430px; /* 适配移动端最大宽度 */
    background: #f6f6f6; /* Grid gaps color */
    border-top: 1px solid #dcdcdc;
    border-radius: 0; /* 移除顶部圆角 */
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.08);
    display: grid; /* Changed to Grid layout */
    grid-template-columns: repeat(4, 1fr); /* 4 columns */
    grid-template-rows: repeat(4, 66px); /* 4 rows, each 54px height */
    gap: 1px; /* Gap for borders */
    overflow: hidden; /* Ensure rounded corners clip content */
  }
  .key-btn {
    font-size: 22px;
    border: none;
    background: #fff;
    border-radius: 0; /* 移除按钮圆角 */
    box-shadow: none;
    cursor: pointer;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    &:active {
      background: #f2f2f2;
    }
  }
  .key-btn.empty-background-btn {
    background: #f5f5f5; /* Match page background color */
    box-shadow: none;
    cursor: default;
    border-radius: 0; /* 移除按钮圆角 */
  }
  .key-btn.zero-btn {
    grid-column: 1 / span 2; /* Spans 2 columns */
    border-radius: 0; /* 移除按钮圆角 */
  }
  .key-btn.delete-key-btn {
    background: #f5f5f5; /* Match empty background */
    border-radius: 0; /* 移除按钮圆角 */
  }
  .confirm-btn {
    grid-column: 4 / 5;
    grid-row: 2 / span 3;
    background: #1989fa;
    color: #fff;
    font-weight: bold;
    border-radius: 0;
    font-size: 20px;
    box-shadow: none;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    transition: background 0.2s;
  }
  .confirm-btn:disabled {
    background: #b3d8fd;
    color: #fff;
  }
}
</style>
