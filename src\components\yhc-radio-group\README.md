# yhc-radio-group 单选框组组件

一个基于 Vant UI 的可复用单选框组组件，封装了 van-field 和 van-radio-group 的组合。

## 功能特性

- 支持双向数据绑定（v-model）
- 可配置单选框选项和标签文本
- 支持水平和垂直布局方向
- 支持单个选项禁用和整体禁用
- 支持表单验证规则
- 支持条件显示子表单（showMode 机制）
- 支持动态子表单映射（map 配置）
- 保持与现有 yhc 组件风格一致

## 配置参数

### 基础配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| label | String | "单选框组" | 字段标签文本 |
| key | String | "" | 字段名称，用于表单数据绑定 |
| required | Boolean | false | 是否必填 |
| disabled | Boolean | false | 是否禁用整个组件 |
| border | Boolean | true | 是否显示边框 |
| labelWidth | String | "" | 标签宽度 |
| rules | Array | [] | 表单验证规则 |

### 单选框组配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| direction | String | "horizontal" | 布局方向，可选值：horizontal、vertical |
| shape | String | "circle" | 单选框形状，可选值：circle、square |
| options | Array | [] | 选项数组，格式见下方说明 |

### options 选项数组格式

```javascript
[
  {
    value: "option1",    // 选项值（必填）
    label: "选项1",      // 选项显示文本（必填）
    disabled: false      // 是否禁用此选项（可选，默认false）
  },
  // ... 更多选项
]
```

也支持简化格式：
```javascript
["选项1", "选项2", "选项3"] // 会自动转换为对象格式
```

## 使用示例

### 基本用法

```vue
<template>
  <yhc-form :config="formConfig" @onSubmit="onSubmit" />
</template>

<script setup>
import { reactive } from 'vue'

const formConfig = {
  form: [
    {
      label: "适用人员",
      key: "applicable_users",
      component: "yhc-radio-group",
      required: true,
      rules: [{ required: true, message: "请选择适用人员" }],
      options: [
        { value: "all", label: "全部人员" },
        { value: "specific", label: "指定人员" }
      ]
    }
  ]
}

const onSubmit = (data) => {
  console.log('表单数据:', data)
  // data.applicable_users 将是选中的值，如："all"
}
</script>
```

### 垂直布局

```javascript
{
  label: "餐时选择",
  key: "meal_time",
  component: "yhc-radio-group",
  direction: "vertical", // 垂直布局
  options: [
    { value: "breakfast", label: "早餐" },
    { value: "lunch", label: "午餐" },
    { value: "dinner", label: "晚餐" },
    { value: "supper", label: "夜宵" }
  ]
}
```

### 带禁用选项

```javascript
{
  label: "权限级别",
  key: "permission_level",
  component: "yhc-radio-group",
  options: [
    { value: "read", label: "只读权限" },
    { value: "write", label: "读写权限" },
    { value: "admin", label: "管理员权限", disabled: true } // 禁用此选项
  ]
}
```

### 方形单选框

```javascript
{
  label: "选择类型",
  key: "type",
  component: "yhc-radio-group",
  shape: "square", // 方形单选框
  options: [
    { value: "type1", label: "类型一" },
    { value: "type2", label: "类型二" }
  ]
}
```

## 条件显示子表单

组件支持根据选中的值条件显示不同的子表单，类似于 `yhc-switch` 和 `yhc-segmented-control` 组件的功能。

### 使用 showMode 配置条件显示

```javascript
{
  label: "适用餐时",
  key: "meal_time_type",
  component: "yhc-radio-group",
  options: [
    { value: "all", label: "全部餐时" },
    { value: "specific", label: "指定餐时" }
  ],
  child: {
    showMode: "specific", // 当选中值为"specific"时显示子表单
    form: [
      // 当条件满足时显示的子表单配置
      {
        label: "指定餐时",
        key: "specific_meal_times",
        component: "yhc-checkbox-group",
        options: [
          { value: "breakfast", label: "早餐" },
          { value: "lunch", label: "午餐" },
          { value: "dinner", label: "晚餐" }
        ]
      }
    ]
  }
}
```

### 使用 map 配置不同选项对应的子表单

```javascript
{
  label: "适用人员",
  key: "user_type",
  component: "yhc-radio-group",
  options: [
    { value: "all", label: "全部人员" },
    { value: "department", label: "指定部门" },
    { value: "user", label: "指定用户" }
  ],
  child: {
    map: {
      "department": [
        // 当选中"指定部门"时显示的子表单配置
        {
          label: "选择部门",
          key: "selected_departments",
          component: "yhc-select-department"
        }
      ],
      "user": [
        // 当选中"指定用户"时显示的子表单配置
        {
          label: "选择用户",
          key: "selected_users",
          component: "yhc-select-user"
        }
      ]
    }
  }
}
```

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 单选框组值变化时触发 | { component: "yhc-radio-group", key: 字段名, value: 选中值 } |

## 数据格式

组件的值是一个字符串或数字，表示选中项的 value 值：

```javascript
// 示例数据
{
  meal_time_type: "specific" // 选中了"指定餐时"选项
}
```

## 样式定制

组件支持通过 CSS 变量或深度选择器自定义样式：

```scss
// 自定义单选框间距
:deep(.van-radio-group--horizontal .van-radio) {
  margin-right: 20px; // 水平间距
  margin-bottom: 8px;
}

:deep(.van-radio-group--vertical .van-radio) {
  margin-bottom: 12px; // 垂直间距
}

// 自定义标签颜色
:deep(.van-radio__label) {
  color: #1989fa;
}

// 自定义选中状态颜色
:deep(.van-radio__icon--checked) {
  color: #07c160;
}
```

## 注意事项

1. `options` 数组中每个选项的 `value` 必须唯一
2. 组件会自动初始化表单值为空字符串 `""`
3. 支持表单验证，可配置 `required` 和 `rules`
4. 禁用状态下用户无法进行任何操作
5. 子表单功能支持平滑的过渡动画效果
6. 组件名称和所有文本内容使用中文，符合项目规范
7. 与 yhc-form 组件体系完全兼容，支持自动注册和使用
