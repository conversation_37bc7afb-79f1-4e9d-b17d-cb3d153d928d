<template>
  <div class="cost-center-container">
    <yhc-list :key="listConfig.key || 'default'" :config="listConfig" @onButClick="onAddClick" ref="listRef">
      <template #header>
        <div class="add-button-container">
          <div class="add-button" @click="onAddClick">
            <img src="/img/add.svg" alt="新增" class="add-icon" />
            <span>新增成本中心</span>
          </div>
        </div>
      </template>

      <template #default="{ item, index }">
        <div class="demo-item" @click.stop="onItemClick(item)">
          <div class="item-content">
            <div class="item-header">
              <div class="item-title">{{ item.title }}</div>
            </div>
            <div class="item-desc">内部员工：{{ item.userlst }}</div>
            <div class="item-desc">外部人员：{{ item.outsiderlst }}</div>
          </div>
        </div>
      </template>
    </yhc-list>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'

const { proxy } = getCurrentInstance()
const router = useRouter()

// 骨架屏配置
const skeletonConfig = reactive({
  isShow: true,
  count: 3,
  row: 2,
  rowWidth: ['100%', '60%', '80%'],
  avatar: false,
  title: true,
  titleWidth: '50%',
  duration: 500
})

// 列表组件引用
const listRef = ref(null)
// 列表配置
const listConfig = reactive({
  curl: {
    ls: '/cost_center/get_ls' // 留空，使用模拟数据
  },
  postData: {},
  search: {
    isShow: false, // 关闭搜索功能
    isShowPopup: false
  },
  tabs: {
    isShow: false
  },
  button: {
    isShow: false,
  },
  skeleton: skeletonConfig,
  // 模拟数据格式化
  format: (data) => {
    console.log('格式化数据:', data)
  },
})

// 设置页面标题
const setPageTitle = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: '成本中心',
  })
}
setPageTitle()

// 新增按钮点击事件
const onAddClick = () => {
  console.log('点击新增成本中心')
  router.push('/costCenter/add')
}

// 列表项点击事件
const onItemClick = (item) => {
  console.log('点击成本中心项:', item)
  router.push({
    path: '/costCenter/detail',
    query: { id: item.id }
  })
}
</script>

<style lang="scss" scoped>
.cost-center-container {
  padding-bottom: 16px;
  background-color: #f7f8fa;
  min-height: 100%;
}

.add-button-container {
  padding: 16px;
  padding-bottom: 0;

  .add-button {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 8px;

    .add-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: normal;
      line-height: 22px;
      color: #323233;
    }
  }
}

.demo-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin:16px;
  background: #fff;
  border-radius: 8px;
  .item-content {
    flex: 1;
    min-width: 0; /* 确保flex子项能够收缩 */

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items:center;
      margin-bottom: 8px;
    }

    .item-title {
      font-size: 17px;
      font-weight: 500;
      line-height: 23px;
      letter-spacing: normal;
      color: #171A1D;
      flex: 1;
    }

    .item-expire {
      background: #ED6A0C;
      color: #FFFFFF;
      font-size: 12px;
      line-height: 16px;
      padding: 0.5px 5px;
      border-radius: 22px;
    }

    .item-desc {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #9E9E9E;
      margin-bottom: 8px;
    }

    .item-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .tag-item {
        // background: #E8F4FD;
        color: #1989FA;
        font-size: 12px;
        line-height: 16px;
        padding: 2.5px 5px;
        border-radius: 2px;
        display: inline-block;
        border: 0.5px solid #1989FA;
      }
    }

    .item-time {
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #9E9E9E;
    }
  }

  .item-action {
    margin-left: 12px;
  }
}
</style>
