<template>
  <div class="online-book-wrapper">
    <van-form @submit="onSubmit" class="form-content">
      <!-- 基础配置区域 -->
      <BasicConfigSection 
        :formData="formData"
        @showTypeSelector="showDiningTypeDrawer"
        @showDiningModeSelector="showDiningModeSelector"
      />

      <!-- 预定截止时间配置区域 -->
      <BookingDeadlineSection 
        :formData="formData"
        @showBookingDeadlineSelector="showBookingDeadlineSelector"
        @showUnifiedTimeSelector="showUnifiedTimeSelector"
        @showMealTimeSelector="showMealTimeSelector"
      />

      <!-- 高级配置区域 -->
      <AdvancedConfigSection 
        :formData="formData"
        @showCancelBookingDeadlineSelector="showCancelBookingDeadlineSelector"
        @showCancelUnifiedTimeSelector="showCancelUnifiedTimeSelector"
        @showCancelMealTimeSelector="showCancelMealTimeSelector"
        @showChargeMethodSelector="showChargeMethodSelector"
      />
    </van-form>

    <!-- 固定在底部的保存按钮 -->
    <div class="fixed-button-wrapper">
      <van-button type="primary" block native-type="submit" :loading="uiState.loading" @click="handleSave">
        保存
      </van-button>
    </div>

    <!-- 就餐方式多选抽屉 -->
    <van-popup
      v-model:show="showDiningTypeDrawerPopup"
      position="bottom"
      :style="{ height: '60%' }"
      round
      :closeable="false"
    >
      <div class="dining-type-drawer">
        <div class="top-block">
          <span @click="cancelDiningTypeSelection" class="cancel-btn">取消</span>
          <span class="title-text">就餐方式</span>
          <span @click="confirmDiningTypeSelection" class="confirm-btn">确认</span>
        </div>
        
        <div class="list-content">
          <div class="multi-select-list">
            <div
              v-for="(option, index) in diningTypeOptions"
              :key="option.value"
              class="multi-select-item"
              :class="{ disabled: option.disabled }"
              @click="toggleDiningType(option)"
            >
              <div class="item-content">
                <div class="item-main">
                  <span class="item-text">{{ option.text }}</span>
                  <span class="item-detail">{{ option.detail }}</span>
                </div>
                <van-checkbox
                  :model-value="tempSelectedDiningTypes.includes(option.value)"
                  :disabled="option.disabled"
                  shape="square"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 选择器弹窗组件 -->
    <SelectorPopups 
      :uiState="uiState"
      :formData="formData"
      :OPTIONS="OPTIONS"
      @diningModeSelect="onDiningModeSelect"
      @bookingDeadlineSelect="onBookingDeadlineSelect"
      @cancelBookingDeadlineSelect="onCancelBookingDeadlineSelect"
      @chargeMethodSelect="onChargeMethodSelect"
      @timeSelectionConfirm="confirmTimeSelection"
      @timeSelectionCancel="cancelTimeSelection"
    />
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { showToast } from 'vant'
import BasicConfigSection from './components/BasicConfigSection.vue'
import BookingDeadlineSection from './components/BookingDeadlineSection.vue'
import AdvancedConfigSection from './components/AdvancedConfigSection.vue'
import SelectorPopups from './components/SelectorPopups.vue'

// 获取当前实例
const { proxy } = getCurrentInstance()

// ==================== 表单数据 ====================
const formData = reactive({
  // 基础字段
  type: ['dine_in'], // 改为数组格式，默认包含堂食
  type_text: '堂食', // 默认显示堂食
  temporary: '', // 选餐模式值
  temporary_text: '', // 选餐模式显示文本
  preset_menu_type: 'multiple', // 预设套餐类型：multiple-预定多份，proxy-代预定
  
  // 预定截止时间相关
  booking_deadline_type: '', // 预定截止时间类型
  booking_deadline_type_text: '', // 预定截止时间显示文本
  custom_deadline_type: 'unified', // 自定义截止类型：unified-统一截止，separate-分餐截止
  unified_deadline_time: '', // 统一截止时间
  unified_deadline_time_text: '', // 统一截止时间显示文本
  advance_days: 0, // 提前天数
  
  // 分餐截止时间
  separate_meal_times: [
    { name: '早餐', time: '', time_text: '' },
    { name: '午餐', time: '', time_text: '' },
    { name: '晚餐', time: '', time_text: '' }
  ],
  advance_days_separate: 0, // 分餐提前天数
  
  // 提前预定上限
  advance_booking_limit: 7, // 提前预定上限天数
  
  // 取消预定截止时间相关
  cancel_booking_deadline_type: '', // 取消预定截止时间类型
  cancel_booking_deadline_type_text: '', // 取消预定截止时间显示文本
  cancel_custom_deadline_type: 'unified', // 取消预定自定义截止类型
  cancel_unified_deadline_time: '', // 取消预定统一截止时间
  cancel_unified_deadline_time_text: '', // 取消预定统一截止时间显示文本
  cancel_advance_days: 0, // 取消预定提前天数
  
  // 取消预定分餐截止时间
  cancel_separate_meal_times: [
    { name: '早餐', time: '', time_text: '' },
    { name: '午餐', time: '', time_text: '' },
    { name: '晚餐', time: '', time_text: '' }
  ],
  cancel_advance_days_separate: 0, // 取消预定分餐提前天数
  
  // 预定时核销
  verification_on_booking: false, // 是否预定时核销
  verification_methods: ['active'], // 核销方式：active-主动，meal_serve-出餐，meal_end-闭餐
  
  // 扣费方式
  charge_method: '', // 扣费方式值
  charge_method_text: '', // 扣费方式显示文本
  
  // 其他字段
  specific_open: [],
  specific_open_text: ''
})

// 选择器相关
const showPickerPopup = ref(false)
const currentPickerType = ref('')
const currentPickerColumns = ref([])

// 就餐方式抽屉相关
const showDiningTypeDrawerPopup = ref(false)
const selectedDiningTypes = ref(['dine_in']) // 默认选中堂食
const tempSelectedDiningTypes = ref(['dine_in']) // 临时选中状态，用于取消时恢复

// ==================== 响应式数据 ====================
// 统一的UI状态管理
const uiState = reactive({
  // 弹窗状态
  popups: {
    diningMode: false,
    bookingDeadline: false,
    cancelBookingDeadline: false,
    chargeMethod: false,
    timePicker: false
  },
  // 临时选择值
  temp: {
    diningMode: '',
    bookingDeadlineType: '',
    cancelBookingDeadlineType: '',
    chargeMethod: ''
  },
  // 时间选择器
  timePicker: {
    selectedTime: ['12', '00', '00'],
    title: '选择时间',
    currentField: '',
    currentMealIndex: -1
  },
  // 加载状态
  loading: false
})

// 就餐方式选项数据
const diningTypeOptions = [
  { text: '堂食', value: 'dine_in', disabled: true, detail: '在餐厅内用餐' }, // 堂食锁定不可取消
  { text: '外卖', value: 'takeout', disabled: false, detail: '外送到指定地址' },
  { text: '打包', value: 'package', disabled: false, detail: '打包带走' },
  { text: '自提', value: 'pickup', disabled: false, detail: '到店自取' }
]

// ==================== 选项配置 ====================
const OPTIONS = {
  diningMode: [
    { text: '自助选餐', value: 'self_service' },
    { text: '预设套餐', value: 'preset_menu' }
  ],
  bookingDeadline: [
    { text: '开餐时截止', value: 'meal_start' },
    { text: '闭餐时截止', value: 'meal_end' },
    { text: '自定义截止', value: 'custom' }
  ],
  cancelBookingDeadline: [
    { text: '不可取消', value: 'no_cancel' },
    { text: '开餐时截止', value: 'meal_start' },
    { text: '闭餐时截止', value: 'meal_end' },
    { text: '自定义截止', value: 'custom' }
  ],
  chargeMethod: [
    { text: '核销时扣费', value: 'charge_on_verification' },
    { text: '预定时扣费', value: 'charge_on_booking' }
  ]
}

// ==================== 方法 ====================
// 显示就餐方式抽屉
const showDiningTypeDrawer = () => {
  showDiningTypeDrawerPopup.value = true
  tempSelectedDiningTypes.value = [...formData.type]
}

// 显示选餐模式选择器
const showDiningModeSelector = () => {
  uiState.popups.diningMode = true
  uiState.temp.diningMode = formData.temporary
}

// 显示预定截止时间选择器
const showBookingDeadlineSelector = () => {
  uiState.popups.bookingDeadline = true
  uiState.temp.bookingDeadlineType = formData.booking_deadline_type
}

// 显示取消预定截止时间选择器
const showCancelBookingDeadlineSelector = () => {
  uiState.popups.cancelBookingDeadline = true
  uiState.temp.cancelBookingDeadlineType = formData.cancel_booking_deadline_type
}

// 显示扣费方式选择器
const showChargeMethodSelector = () => {
  uiState.popups.chargeMethod = true
  uiState.temp.chargeMethod = formData.charge_method
}

// 通用选择确认方法
const confirmSelection = (type, fieldName, options) => {
  const selectedValue = uiState.temp[type]
  formData[fieldName] = selectedValue
  
  const selectedOption = options.find(opt => opt.value === selectedValue)
  formData[`${fieldName}_text`] = selectedOption?.text || ''
  
  uiState.popups[type] = false
}

// 选择器方法
const onDiningModeSelect = (item) => {
  uiState.temp.diningMode = item.value
  confirmSelection('diningMode', 'temporary', OPTIONS.diningMode)
}

const onBookingDeadlineSelect = (item) => {
  uiState.temp.bookingDeadlineType = item.value
  confirmSelection('bookingDeadline', 'booking_deadline_type', OPTIONS.bookingDeadline)
}

const onCancelBookingDeadlineSelect = (item) => {
  uiState.temp.cancelBookingDeadlineType = item.value
  confirmSelection('cancelBookingDeadline', 'cancel_booking_deadline_type', OPTIONS.cancelBookingDeadline)
}

const onChargeMethodSelect = (item) => {
  uiState.temp.chargeMethod = item.value
  confirmSelection('chargeMethod', 'charge_method', OPTIONS.chargeMethod)
}

// 时间选择器方法
const showUnifiedTimeSelector = () => {
  uiState.timePicker.currentField = 'unified_deadline_time'
  uiState.timePicker.title = '选择截止时间'
  const timeParts = formData.unified_deadline_time ? formData.unified_deadline_time.split(':') : ['12', '00', '00']
  uiState.timePicker.selectedTime = timeParts.length === 3 ? timeParts : ['12', '00', '00']
  uiState.popups.timePicker = true
}

const showMealTimeSelector = (index) => {
  uiState.timePicker.currentMealIndex = index
  uiState.timePicker.currentField = 'meal_time'
  uiState.timePicker.title = `选择${formData.separate_meal_times[index].name}时间`
  const mealTime = formData.separate_meal_times[index].time
  const timeParts = mealTime ? mealTime.split(':') : ['12', '00', '00']
  uiState.timePicker.selectedTime = timeParts.length === 3 ? timeParts : ['12', '00', '00']
  uiState.popups.timePicker = true
}

const showCancelUnifiedTimeSelector = () => {
  uiState.timePicker.currentField = 'cancel_unified_deadline_time'
  uiState.timePicker.title = '选择截止时间'
  const timeParts = formData.cancel_unified_deadline_time ? formData.cancel_unified_deadline_time.split(':') : ['12', '00', '00']
  uiState.timePicker.selectedTime = timeParts.length === 3 ? timeParts : ['12', '00', '00']
  uiState.popups.timePicker = true
}

const showCancelMealTimeSelector = (index) => {
  uiState.timePicker.currentMealIndex = index
  uiState.timePicker.currentField = 'cancel_meal_time'
  uiState.timePicker.title = `选择${formData.cancel_separate_meal_times[index].name}时间`
  const mealTime = formData.cancel_separate_meal_times[index].time
  const timeParts = mealTime ? mealTime.split(':') : ['12', '00', '00']
  uiState.timePicker.selectedTime = timeParts.length === 3 ? timeParts : ['12', '00', '00']
  uiState.popups.timePicker = true
}

const cancelTimeSelection = () => {
  uiState.popups.timePicker = false
  uiState.timePicker.currentField = ''
  uiState.timePicker.currentMealIndex = -1
}

const confirmTimeSelection = () => {
  const timeString = uiState.timePicker.selectedTime.join(':')
  const field = uiState.timePicker.currentField
  const mealIndex = uiState.timePicker.currentMealIndex
  
  // 时间字段映射
  const timeFieldMap = {
    'unified_deadline_time': () => {
      formData.unified_deadline_time = timeString
      formData.unified_deadline_time_text = timeString
    },
    'meal_time': () => {
      if (mealIndex >= 0) {
        formData.separate_meal_times[mealIndex].time = timeString
        formData.separate_meal_times[mealIndex].time_text = timeString
      }
    },
    'cancel_unified_deadline_time': () => {
      formData.cancel_unified_deadline_time = timeString
      formData.cancel_unified_deadline_time_text = timeString
    },
    'cancel_meal_time': () => {
      if (mealIndex >= 0) {
        formData.cancel_separate_meal_times[mealIndex].time = timeString
        formData.cancel_separate_meal_times[mealIndex].time_text = timeString
      }
    }
  }
  
  timeFieldMap[field]?.()
  cancelTimeSelection()
}

// 就餐方式相关方法
const toggleDiningType = (option) => {
  if (option.disabled) return // 堂食不能取消选中

  const index = tempSelectedDiningTypes.value.indexOf(option.value)
  if (index > -1) {
    tempSelectedDiningTypes.value.splice(index, 1)
  } else {
    tempSelectedDiningTypes.value.push(option.value)
  }
}

const cancelDiningTypeSelection = () => {
  tempSelectedDiningTypes.value = [...formData.type]
  showDiningTypeDrawerPopup.value = false
}

const confirmDiningTypeSelection = () => {
  formData.type = [...tempSelectedDiningTypes.value]
  
  // 更新显示文本
  const selectedTexts = diningTypeOptions
    .filter(opt => tempSelectedDiningTypes.value.includes(opt.value))
    .map(opt => opt.text)
  formData.type_text = selectedTexts.join('、')
  
  showDiningTypeDrawerPopup.value = false
}

// 表单处理方法
const handleFormSubmit = async (event) => {
  event?.preventDefault()
  
  if (!validateFormData()) return
  
  try {
    uiState.loading = true
    console.log('提交表单数据:', formData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    showToast('保存成功')
    
  } catch (error) {
    handleError(error, 'handleFormSubmit')
  } finally {
    uiState.loading = false
  }
}

const handleSave = () => {
  const formElement = document.querySelector('.form-content')
  if (formElement) {
    formElement.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }))
  }
}

// 表单提交
const onSubmit = async (values) => {
  uiState.loading = true

  try {
    // 构建提交数据
    const submitData = {
      ...formData
    }

    console.log('提交数据:', submitData)
    
    // 这里调用实际的API
    // await api.saveOnlineBookRule(submitData)
    
    showToast('保存成功')
  } catch (error) {
    console.error('提交失败:', error)
    showToast('保存失败')
  } finally {
    uiState.loading = false
  }
}

// 工具方法
const handleError = (error, context = '') => {
  console.error(`Error in ${context}:`, error)
  showToast(`操作失败: ${error.message || '未知错误'}`)
}

const validateFormData = () => {
  const requiredFields = [
    { field: 'type_text', message: '请选择就餐方式' },
    { field: 'temporary_text', message: '请选择选餐模式' }
  ]
  
  return requiredFields.every(({ field, message }) => {
    if (!formData[field]) {
      showToast(message)
      return false
    }
    return true
  })
}
</script>

<style lang="scss" scoped>
// ==================== 页面布局样式 ====================
.online-book-wrapper {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px; // 为固定按钮留出空间
}

.form-content {
  padding: 16px 0;
}

// ==================== 固定按钮样式 ====================
.fixed-button-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: #fff;
  border-top: 1px solid #ebedf0;
  z-index: 100;
}

// ==================== 就餐方式抽屉样式 ====================
.dining-type-drawer {
  background: #fff;
  
  .top-block {
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebedf0;
    
    .cancel-btn {
      color: #969799;
      font-size: 14px;
    }
    
    .confirm-btn {
      color: #007fff;
      font-size: 14px;
    }
    
    .title-text {
      color: #333;
      font-weight: 500;
      font-size: 17px;
    }
  }
  
  .list-content {
    padding: 0 20px;
    
    .multi-select-list {
      .multi-select-item {
        padding: 16px 0;
        border-bottom: 1px solid #ebedf0;
        
        &:last-child {
          border-bottom: none;
        }
        
        &.disabled {
          opacity: 0.6;
        }
        
        .item-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .item-main {
            flex: 1;
            
            .item-text {
              display: block;
              color: #323233;
              font-size: 16px;
              margin-bottom: 4px;
            }
            
            .item-detail {
              display: block;
              color: #969799;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
</style>
