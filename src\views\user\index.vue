<template>
  <div class="wrapper-user" v-if="isShow">
    <div class="user-info">
    <div style="height: 48px;display: flex;justify-content: space-between;align-items: center;padding-top:30px;">
      <div class="left" style="height: 48px;display: flex;align-items: top;">
        <van-image round width="48" height="48" :src="user_info.avatar" />
        <div style="margin-left: 8px;">
          <div  style="font-size: 17px;line-height: 22px;color: #FFFFFF;">
            {{ user_info.name }}
            <!-- <van-tag
              style="margin-left: 12px"
              round
              size="medium"
              type="primary"
              color="rgba(0, 127, 255, 0.1)"
              text-color="rgba(0, 127, 255, 1)"
              >用户端</van-tag> -->
          </div>
          <div
            class=""
            style="
              font-size: 12px;
              color: #FFFFFF;
              margin-top: 8px;
              line-height: 18px;
            "
          >
            {{ user_info.dept_name }}
          </div>
        </div>
      </div>
      <div class="right flex" @click="showPaymentCode">
        <van-icon size="26" name="qr" color="#FFFFFF" />
      </div>
      <!-- <van-button type="primary" round size="small">切换身份</van-button> -->
    </div>
    </div>
    <div class="account">
      <div class="title flex" style="">
       <div>
            <div class="text">总资产(元)</div>
            <div class="num">{{money}}</div>
        </div>
        <div>
          <div class="btn">提现</div>
        </div>
        <!--<van-icon
          @click="isShowMoney = !isShowMoney"
          size="20px"
          :name="isShowMoney ? 'eye-o' : 'closed-eye'"
        /> -->
      </div>
      <div class="money-block flex">
        <div>
        <div class="money_title">补贴账户</div>
          <div class="money">
            {{ isShowMoney ?( account_info.subsidy_balance*1).toFixed(2) : "****" }}
          </div>
        </div>
        <div>
        <div class="money_title">储蓄账户</div>
          <div class="money">
            {{
              isShowMoney ? (account_info.savings_balance * 1).toFixed(2) : "****"
            }}
          </div>
        </div>
        <div>
          <div class="money_title">待还</div>
          <div class="money">
            {{
              isShowMoney ? (account_info.debt_amount * 1).toFixed(2) : "****"
            }}
          </div>
        </div>
      </div>
      <div class="flex">
        <div class="item" @click="rechargeClick">
          <div style="color: #1989FA;">充值</div>
        </div>
        <div :class="`item ${!config.balance_refund&&'noclick'}`" @click="moneybutton()">
          <div style="">转账</div>
        </div>
      </div>
    </div>
    <van-cell-group
      inset
      v-for="(item, i) in list"
      :key="i"
      style="margin-top: 16px"
    >
      <van-cell
        :title="it.title"
        :icon="`/images/common/${it.icon}.svg`"
        is-link
        v-for="(it, i) in item"
        :key="it.title"
        @click="onClick(it)"
      >
      </van-cell>
    </van-cell-group>
    <div style="padding-top: 12px">
      <!-- <van-button type="primary" round size="small">切换身份</van-button> -->
    </div>

    <!-- {{ AURA-X: Add - 付款码弹窗 }} -->
    <van-popup v-model:show="showPaymentPopup" position="center" :style="{ width: '90%', borderRadius: '15px' }">
      <div class="payment-popup">
        <div class="popup-header">
          <span></span>
          <div class="popup-title">用户付款码</div>
          <van-icon name="cross" @click="showPaymentPopup = false" class="close-icon" />
        </div>
        <div class="popup-body">王佳文</div>
        <div class="popup-content">
          <div class="qr-container" @touchstart="handleTouchStart" @touchend="handleTouchEnd">
            <vue-qr
              :text="paymentUrl"
              margin="0"
              logoSrc="/icons/logo.png"
              qid="paymentQr"
              :logoScale="logoScale"
              :logoMargin="2"
            />
          </div>
          <div class="payment-info">
            <div class="payment-label">长摁二维码保存到相册</div>
            
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  showToast,
  showDialog,
  showLoadingToast,
  showFailToast,
  showSuccessToast,
  showImagePreview,
} from "vant";
import { useLoginStore } from "@/store/dingLogin";

// {{ AURA-X: Add - 导入二维码组件用于弹窗显示付款码 }}
import vueQr from "vue-qr/src/packages/vue-qr.vue";


const { proxy } = getCurrentInstance();
const router = useRouter();

const money=ref(0);
// 静态用户信息数据
const user_info = ref({
  name: "张三",
  avatar: "https://img.yzcdn.cn/vant/cat.jpeg",
  department_name: "技术部"
});

// 静态账户信息数据
let account_info = ref({
  account_id: "ACC001",
  account_name: "张三",
  account_type: "个人账户",
  money: 1580.50,        // 总金额
  balance: 800.30,       // 虚拟余额
  real_blance: 780.20,   // 账户余额
});

let isShowMoney = ref(true);
const app = useLoginStore();

let list = [
  [
    {
      title: "卡券(2)",
      icon: "card",
      url: "/couponlist",
    },
  ],
  [
    {
      title: "需求墙(2)",
      icon: "demandWall",
      url: "/about",
    },
    {
      title: "产品咨询",
      icon: "help",
      url: "/help",
    },
  ],
  [
    {
      title: "关于",
      icon: "about",
      url: "/help",
    },
    
  ],
];
// if(!sysConfig.bring){
//   list.shift()
// }
const rechargeClick = () => {
  router.push("/recharge");
};
let moneybutton = () => {
  if (!config.value.balance_refund) {
    showToast({position:'top',message:"该功能尚未开启，请联系管理员开启后使用"});
    return;
  }
  showDialog({
    title: "余额退款",
    message: "确认退款，余额将在2个小时内原路返还",
    confirmButtonText: "确认退款",
    showCancelButton: true,
  }).then(() => {
    console.log("提现");
    showLoadingToast({
      message: "退款中...",
      forbidClick: true,
    });

    // {{ AURA-X: Modify - 使用模拟退款逻辑替代真实接口调用 }}
    // 模拟退款成功
    setTimeout(() => {
      showSuccessToast("退款成功");
      // 模拟退款后更新账户余额
      account_info.value.real_blance = 0;
      account_info.value.money = account_info.value.balance * 1 + account_info.value.real_blance * 1;
    }, 2000);

    // 注释掉真实的退款接口调用
    // proxy
    //   .$post(
    //     "account/post_withdraw",
    //     {}
    //   )
    //   .then((res) => {
    //     if (!res.errcode) {
    //       showSuccessToast("退款成功");
    //       getAccount();
    //     }
    //   })
    //   .catch((err) => {
    //     showFailToast("退款失败");
    //   })
    //   .finally(() => {});
  });
};
// 静态配置信息数据
let config = ref({
  balance_refund: true,  // 是否允许余额退款
  bring: true,          // 是否支持代餐功能
  // 其他配置项...
});

let isShow = ref(false);

// {{ AURA-X: Add - 付款码弹窗状态管理 }}
const showPaymentPopup = ref(false);
const paymentUrl = ref('');
const logoScale = ref(0.13);

// {{ AURA-X: Add - 长按保存功能相关状态 }}
let touchTimer = null;
const longPressDelay = 800; // 长按触发时间（毫秒）

// 获取账户信息
const getOnfo = () => {
  proxy
    .$get("/user_profile/get_info", {})
    .then((res) => {
      console.log(res);
      user_info.value = res.data;
    })
    .finally(() => {
      isShow.value = true;
    });
};
getOnfo();
//获取账户余额
const getWallet = () => {
  proxy
    .$get("/user_wallet/get_info", {})
    .then((res) => {
      console.log(res);
      account_info.value = res.data;
      money.value = res.data.subsidy_balance+res.data.savings_balance+res.data.debt_amount;
    })
    .finally(() => {
      isShow.value = true;
    });
};
getWallet();
// 使用静态数据初始化
const initStaticData = () => {
  // 模拟接口调用完成，显示页面
  isShow.value = true;
};

// {{ AURA-X: Add - 组件挂载时初始化静态数据 }}
// 组件挂载时初始化静态数据
onMounted(() => {
  initStaticData();
});
let onClick = (item) => {
  if (item.title == "代餐码") {
    // {{ AURA-X: Modify - 使用静态代餐码数据替代真实接口调用 }}
    // 模拟代餐码数据
    const mockMealCode = "MC" + Date.now().toString().slice(-6);

    showDialog({
      title: "代餐码",
      message: `您的代餐码为：${mockMealCode}，注意请勿随意泄露`,
      confirmButtonText: "发送同事",
      showCancelButton: true,
    }).then(() => {
      // 模拟钉钉发送功能（实际环境中需要钉钉SDK支持）
      showToast("代餐码发送功能需要在钉钉环境中使用");

      // 注释掉真实的钉钉API调用
      // proxy.$_dd.biz.ding.create({
      //   users: [],
      //   corpId: app.corpId,
      //   type: 1,
      //   alertType: 2,
      //   alertDate: {
      //     format: "yyyy-MM-dd HH:mm",
      //     value: "",
      //   },
      //   attachment: {
      //     images: [],
      //   },
      //   text: `您的代餐码为：${mockMealCode}，注意请勿随意泄露`,
      //   bizType: 0,
      //   taskInfo: {},
      //   confInfo: {},
      //   onSuccess: function (res) {
      //     // 调用成功时回调
      //     // console.log(res)
      //   },
      //   onFail: function (err) {
      //     // 调用失败时回调
      //     // console.log(err)
      //   },
      // });
    });

    // 注释掉真实的代餐码接口调用
    // proxy.$post("bring/get_info", {}).then((res) => {
    //   showDialog({
    //     title: "代餐码",
    //     message: `您的代餐码为：${
    //       res.result.code || res.result
    //     }，注意请勿随意泄露`,
    //     confirmButtonText: "发送同事",
    //     showCancelButton: true,
    //   }).then(() => {
    //     // ... 钉钉API调用代码
    //   });
    // });
  } else if (item.url == "/help") {
    showToast({
      message: "该功能为Beta版，如有疑问请使用产品咨询联系客服",
      duration: 2000,
      mask: true,
    });
    setTimeout(() => {
      router.push(item.url);
    }, 2000);
  } else if (item.icon == "chanpin") {
    end_time_but_click();
  } else {
    router.push(item.url);
  }
};
const end_time_but_click = () => {
  proxy.$_dd.biz.util.openLink({
    url: `https://page.dingtalk.com/wow/dingtalk/act/serviceconversation?wh_biz=tm&showmenu=false&goodsCode=${app.env.VITE_APP_DT_GOODS}&corpId=${app.corpId}&token=${app.env.VITE_APP_DT_GOODS_TOKEN}`,
    onSuccess: function (result) {
      console.log(result);
    },
    onFail: function (err) {
      console.log(err);
    },
  });
};
// {{ AURA-X: Add - 显示付款码弹窗功能 }}
const showPaymentCode = () => {
  showPaymentPopup.value = true;
  // 获取付款码
  proxy
    .$get("/payment_code/get_info")
    .then((res) => {
      paymentUrl.value = res.result;
    })
    .catch((err) => {
      console.log(err);
      showToast("获取付款码失败");
    });
};

// {{ AURA-X: Add - 长按开始处理 }}
const handleTouchStart = () => {
  touchTimer = setTimeout(() => {
    saveQrCodeToAlbum();
  }, longPressDelay);
};

// {{ AURA-X: Add - 长按结束处理 }}
const handleTouchEnd = () => {
  if (touchTimer) {
    clearTimeout(touchTimer);
    touchTimer = null;
  }
};

// {{ AURA-X: Add - 保存二维码到相册功能 }}
const saveQrCodeToAlbum = () => {
  try {
    // 获取二维码canvas元素
    const qrCanvas = document.querySelector('#paymentQr canvas');
    if (!qrCanvas) {
      showToast('二维码未加载完成，请稍后再试');
      return;
    }

    // 将canvas转换为图片数据URL
    const imageDataUrl = qrCanvas.toDataURL('image/png');

    // 使用Vant的图片预览组件显示保存选项
    showImagePreview({
      images: [imageDataUrl],
      showIndex: false,
      closeable: true,
      onClose: () => {
        // 尝试保存到相册（在支持的环境中）
        if (navigator.share) {
          // 如果支持Web Share API
          fetch(imageDataUrl)
            .then(res => res.blob())
            .then(blob => {
              const file = new File([blob], 'payment-qr.png', { type: 'image/png' });
              navigator.share({
                files: [file],
                title: '付款码',
                text: '我的付款码'
              }).catch(err => {
                console.log('分享失败:', err);
                showToast('请长按图片手动保存');
              });
            });
        } else {
          // 创建下载链接
          const link = document.createElement('a');
          link.download = 'payment-qr.png';
          link.href = imageDataUrl;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          showSuccessToast('二维码已保存');
        }
      }
    });
  } catch (error) {
    console.error('保存二维码失败:', error);
    showToast('保存失败，请稍后再试');
  }
};

const setRight = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: "我的",
    color: "#fff",
    backgroundColor: "#000",

  });

};
setRight();

</script>
<style lang="scss" scoped>
.wrapper-user {
  width: 100%;
  // min-height: 100vh;
  // margin-bottom: 40px;
  box-sizing: border-box;
  .van-cell{
    padding:16px;
    display: flex;
    align-items: center;
    font-size: 15px;
line-height: 20px;
color: #323233;
:deep(.van-cell__left-icon, .van-cell__right-icon){
    line-height: 54px !important;
    font-size: 22px;
  }
  }
 
  .van-cell__left-icon {
    display: flex;
    
    align-items: center;

    justify-content: center;
    padding:16px;
    // margin-right: 8px;
    .van-icon__image {
      width: 32px;
      height: 32px;
    }
  }
  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    padding-top: 16px;
    .item {
      flex: 1;
      text-align: center;
      padding: 14px 0;
      font-size: 14px;
      margin-top: 16px;
      border: 1px solid #EBEDF0;
      color: #323233;
      font-size: 16px;
      line-height: 18px;
      border-radius: 22px;
    }
    
    .noclick{
      color: rgba(0, 0, 0, 0.3);
    }
  }
  .user-info {
    height: 195px;
    background: linear-gradient(180deg, #007FFF 0%, rgba(0, 119, 255, 0.5905) 68%, rgba(22, 120, 255, 0) 100%);
    padding:0 16px;
  }
  .account {
    border-radius: 8px;
    background: #ffffff;
    margin: 16px;
    margin-top:-100px;
    padding: 16px;
    .title {
      font-size: 15px;
      margin-bottom: 8px;
      line-height: 18px;
      color: #323233;
    }
    .text{
font-size: 15px;
line-height: 18px;
color: #323233;
margin-bottom: 8px;
    }
    .num{
      font-size: 32px;
font-weight: 500;
line-height: 36px;
color: #323233;
    }
    .btn{
      padding:2px 12px; 
      border: 1px solid #DCDEE0;
      border-radius: 22px;
      line-height: 16px;
      font-size: 12px;
      color: #323233;
    }
    .money-block {
      text-align: center;
      padding: 0 16px;
      .money_title{
        font-size: 13px;
        line-height: 18px;
        color: #969799;
        margin-bottom: 8px;
      }
      .money {
        font-size: 16px;
        line-height: 18px;
        color: #323233;
      }
    }
  }
}

/* {{ AURA-X: Add - 付款码弹窗样式 }} */
.payment-popup {
  background: white;
  overflow: hidden;
  .popup-header {
    display: flex;
    justify-content: space-between;
    // align-items: center;
    padding: 26px 16px 36px 36px;
    text-align: center;
    .popup-title {
      flex:1;
      text-align: center;
      font-size: 17px;
      font-weight: 500;
      line-height: 22px;
      color: #323233;
      // margin-bottom:36px;
    }
    .close-icon {
      font-size: 20px;
      color: #999;
      cursor: pointer;
    }
  }
.popup-body{
      // flex:1;
      width: 100%;
      text-align: center;
      font-size: 18px;
      line-height: 22px;
      color: #323233;
      margin-bottom:26px;
    }
  .popup-content {
    // padding: 20px;
    text-align: center;

    .qr-container {
      display: flex;
      justify-content: center;
      // margin-bottom: 30px;
      user-select: none;
      -webkit-user-select: none;
      -webkit-touch-callout: none;

      canvas {
        border-radius: 8px;
        transition: transform 0.1s ease;
      }

      &:active canvas {
        transform: scale(0.98);
      }
    }

    .payment-info {
      padding:26px 0;
      .payment-label {
        text-align: center;
        font-size: 14px;
        line-height: 22px;
        color: #969799;
        // margin-bottom: 8px;
      }

      
    }
  }
}
</style>
