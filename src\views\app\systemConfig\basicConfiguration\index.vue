<template>
  <div class="basic-config-container">
    <!-- 页面标题 -->
    <!-- <van-nav-bar title="基础配置" left-arrow @click-left="$router.go(-1)" /> -->

    <!-- 支付配置 -->
    <van-cell-group inset>
      <van-cell title="固定付款码" label="开启后用户付款码内容固定不变" style="height: 70px; align-items: center;" :itle-style="{
        fontSize: '14px'
      }">
        <template #right-icon>
          <van-switch v-model="config.fixed_payment_code_enabled" @change="saveConfig" :active-value="1" :inactive-value="0" />
        </template>
      </van-cell>

      <van-cell title="补贴账户转账" label="开启后用户可对补贴账户余额转给他人" style="height: 70px; align-items: center;">
        <template #right-icon>
          <van-switch :active-value="1" :inactive-value="0" v-model="config.subsidy_transfer_enabled" @change="saveConfig" />
        </template>
      </van-cell>

      <van-cell title="匿名评价" label="开启后用户评价不可显示姓名" style="height: 70px; align-items: center;">
        <template #right-icon>
          <van-switch :active-value="1" :inactive-value="0" v-model="config.anonymous_review_enabled" @change="saveConfig" />
        </template>
      </van-cell>
    </van-cell-group>
    <van-cell-group inset>
      <van-cell title="储值账户充值" style="height: 70px; align-items: center;">
        <template #right-icon>
          <van-switch :active-value="1" :inactive-value="0" v-model="config.savings_recharge_enabled" @change="saveConfig" />
        </template>
      </van-cell>

      <van-cell title="储值账户提现" style="height: 70px; align-items: center;">
        <template #right-icon>
          <van-switch :active-value="1" :inactive-value="0" v-model="config.savings_withdrawal_enabled" @change="saveConfig" />
        </template>
      </van-cell>
    </van-cell-group>
    <van-cell-group inset>
      <van-cell title="先享后付" label="开启：用户可使用先享后付提前消费" style="height: 70px; align-items: center;">
        <template #right-icon>
          <van-switch :active-value="1" :inactive-value="0" v-model="config.enjoy_first_pay_later_enabled" @change="saveConfig" />
        </template>
      </van-cell>
    </van-cell-group>

    <!-- 限额配置 -->
    <van-cell-group inset>
      <van-cell title="补贴账户上限" style="height: 70px; align-items: center;">
        <template #right-icon>
          <!-- <van-switch v-model="config.subsidy_limit_type" @change="saveConfig" /> -->
          <van-radio-group v-model="config.subsidy_limit_type" direction="horizontal" @change="saveConfig">
            <van-radio :name="0">有上限</van-radio>
            <van-radio :name="1">自定义上线</van-radio>
          </van-radio-group>
        </template>
      </van-cell>

      <!-- 补贴账户上限金额设置 -->
      <!-- <van-cell v-if="config.subsidy_limit_type == 1" title="上限" style="height: 70px; align-items: center;"> -->
      <van-field v-model="config.subsidy_limit_amount" type="number" style="height: 70px; align-items: center;" @change="saveConfig"  placeholder="请输入金额" v-if="config.subsidy_limit_type == 1"
        label="上限">
        <template #button>
          元
        </template>
      </van-field>
      <!-- </van-cell> -->
    </van-cell-group>
    <div style="margin: 10px 28px;font-size: 12px;color: #969799;">例：填100元，用户补贴账户最多100元</div>
    <!-- :label="config.subsidy_limit_type ? `有上限：${config.subsidy_limit_amount}元，用户补贴账户最多${config.subsidy_limit_amount}元` : '关闭：无限制'" -->
    <van-cell-group inset>
      <van-cell title="充值最少金额"
        style="height: 70px; align-items: center;">
        <template #right-icon>
          <!-- <van-switch v-model="config.recharge_min_type" @change="saveConfig" /> -->
          <van-radio-group v-model="config.recharge_min_type" direction="horizontal" @change="saveConfig">
            <van-radio :name="0">有上限</van-radio>
            <van-radio :name="1">自定义上线</van-radio>
          </van-radio-group>
        </template>
      </van-cell>

      <!-- 充值最少金额设置 -->
      <!-- <van-cell v-if="config.recharge_min_type==1" title="最少金额(元)" is-link :value="config.recharge_min_amount + '元'"
        @click="showMinRechargePicker = true" style="height: 70px; align-items: center;" >
      
      </van-cell> -->
      <van-field v-model="config.recharge_min_amount" @change="saveConfig" type="number" style="height: 70px; align-items: center;"  placeholder="请输入金额" v-if="config.recharge_min_type == 1"
        label="最少">
        <template #button >
          <span style="color: #323233;">
            元
          </span>
        </template>
      </van-field>
    </van-cell-group>
    <div style="margin: 10px 28px;font-size: 12px;color: #969799;">例：填100元，用户三次充值最少100元</div>



  </div>
</template>

<script setup>
import { ref, reactive, onMounted,getCurrentInstance } from 'vue'
import { showToast } from 'vant'
const { proxy } = getCurrentInstance();
// 配置数据
const config = ref({
  fixed_payment_code_enabled: 0,
  subsidy_transfer_enabled: 0,
  anonymous_review_enabled: 0,
  savings_recharge_enabled: 0,
  savings_withdrawal_enabled: 0,
  enjoy_first_pay_later_enabled: 0, // 先享后付
  subsidy_limit_type: 0,
  subsidy_limit_amount: 0,
  recharge_min_type: 0,
  recharge_min_amount: 0
})


function getDetail() {
  proxy.$get('/basic_config/get_info').then((res) => {
    if (res.code === 200) {
      Object.assign(config.value, res.data)
      console.log(config.value,74878)
    }
  })
}
getDetail()

function saveConfigAll() {
  proxy.$post('/basic_config/post_modify', config.value).then((res) => {
    if (res.code === 200) {
      showToast('配置已保存')
    } else {
      showToast('保存失败: ' + res.message)
    }
  }).catch((error) => {
    console.error('保存配置失败:', error)
    showToast('保存配置失败')
  })
}
// 保存配置
const saveConfig = () => {
  const subsidyAmount = parseInt(config.value.subsidy_limit_amount);
  const rechargeAmount = parseInt(config.value.recharge_min_amount);
  
  // 只有当转换结果有效时才更新值
  if (!isNaN(subsidyAmount)) {
    config.value.subsidy_limit_amount = subsidyAmount;
  }
  
  if (!isNaN(rechargeAmount)) {
    config.value.recharge_min_amount = rechargeAmount;
  }
  console.log('保存配置:', config.value)
  console.log('551',typeof config.value.subsidy_limit_amount)
  saveConfigAll()
  // 这里应该调用接口保存配置
}









// 页面加载时获取当前配置
onMounted(() => {
  loadCurrentConfig()
})

// 加载当前配置
const loadCurrentConfig = async () => {
  try {
    console.log('正在加载当前配置...')

    // 实际项目中，这里应该调用接口获取配置
    // const response = await proxy.$get('/api/system/config/info')
    // if (response.code === 200) {
    //   Object.assign(config, response.data)
    // }

    console.log('配置加载完成')
  } catch (error) {
    console.error('加载配置失败:', error)
    showToast('加载配置失败')
  }
}
</script>

<style lang="scss" scoped>
.basic-config-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 20px;

  .placeholder-text {
    color: #969799;
    font-size: 14px;
  }

  .dialog-content {
    padding: 20px;

    h3 {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      text-align: center;
    }

    .dialog-actions {
      display: flex;
      gap: 12px;
      margin-top: 20px;

      .van-button {
        flex: 1;
      }
    }
  }
}

// 自定义 Vant 组件样式
:deep(.van-cell-group) {
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  .van-cell-group__title {
    padding: 12px 16px;
    background: #f8f9fa;
    font-weight: 600;
    color: #646566;
    border-bottom: 1px solid #ebedf0;
  }
}

:deep(.van-switch--on) {
  background-color: #1989fa;
}

:deep(.van-image) {
  border-radius: 8px;
  border: 1px solid #ebedf0;
}

:deep(.van-popup) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.van-picker) {
  .van-picker__toolbar {
    background: #f8f9fa;
  }
}
</style>