<template>
  <div class="corporate-culture-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>企业文化与驾驶舱</h2>
      <p class="subtitle">配置企业文化展示和系统界面相关设置</p>
    </div>

    <!-- 功能模块网格 -->
    <div class="function-modules">
      <!-- Logo & Slogan 配置 -->
      <div class="module-section">
        <div class="section-title">
          <van-icon name="setting-o" />
          <span>Logo & Slogan 配置</span>
        </div>
        <van-grid :column-num="2" :border="false" :gutter="12">
          <van-grid-item
            class="module-item"
            @click="navigateTo('/systemConfig/corporateCulture/logo')"
          >
            <div class="item-content">
              <van-icon name="photo-o" size="24" color="#1989fa" />
              <span>Logo配置</span>
              <p>设置Logo尺寸</p>
            </div>
          </van-grid-item>
          <van-grid-item
            class="module-item"
            @click="navigateTo('/systemConfig/corporateCulture/slogan')"
          >
            <div class="item-content">
              <van-icon name="edit" size="24" color="#07c160" />
              <span>Slogan配置</span>
              <p>设置字数限制</p>
            </div>
          </van-grid-item>
        </van-grid>
      </div>

      <!-- 启动页配置 -->
      <div class="module-section">
        <div class="section-title">
          <van-icon name="apps-o" />
          <span>启动页配置</span>
        </div>
        <van-grid :column-num="1" :border="false" :gutter="12">
          <van-grid-item
            class="module-item wide"
            @click="navigateTo('/systemConfig/corporateCulture/startup')"
          >
            <div class="item-content">
              <van-icon name="photo" size="24" color="#ff976a" />
              <span>启动页图片</span>
              <p>上传图片并设置尺寸</p>
            </div>
          </van-grid-item>
        </van-grid>
      </div>

      <!-- 设备语音（高级） -->
      <div class="module-section">
        <div class="section-title">
          <van-icon name="volume-o" />
          <span>设备语音（高级）</span>
        </div>
        <van-grid :column-num="2" :border="false" :gutter="12">
          <van-grid-item
            class="module-item"
            @click="navigateTo('/systemConfig/corporateCulture/voice-payment')"
          >
            <div class="item-content">
              <van-icon name="volume-o" size="24" color="#07c160" />
              <span>付款成功</span>
              <p>语音播报配置</p>
            </div>
          </van-grid-item>
          <van-grid-item
            class="module-item"
            @click="navigateTo('/systemConfig/corporateCulture/voice-amount')"
          >
            <div class="item-content">
              <van-icon name="volume-o" size="24" color="#ffd21e" />
              <span>输入金额</span>
              <p>语音播报配置</p>
            </div>
          </van-grid-item>
          <van-grid-item
            class="module-item"
            @click="navigateTo('/systemConfig/corporateCulture/voice-booking')"
          >
            <div class="item-content">
              <van-icon name="volume-o" size="24" color="#1989fa" />
              <span>预定核销</span>
              <p>语音播报配置</p>
            </div>
          </van-grid-item>
          <van-grid-item
            class="module-item"
            @click="navigateTo('/systemConfig/corporateCulture/voice-consumption-count')"
          >
            <div class="item-content">
              <van-icon name="volume-o" size="24" color="#ee0a24" />
              <span>达到消费次数上限</span>
              <p>语音播报配置</p>
            </div>
          </van-grid-item>
          <van-grid-item
            class="module-item"
            @click="navigateTo('/systemConfig/corporateCulture/voice-consumption-amount')"
          >
            <div class="item-content">
              <van-icon name="volume-o" size="24" color="#ff976a" />
              <span>达到消费金额上限</span>
              <p>语音播报配置</p>
            </div>
          </van-grid-item>
          <van-grid-item
            class="module-item"
            @click="navigateTo('/systemConfig/corporateCulture/voice-online-success')"
          >
            <div class="item-content">
              <van-icon name="volume-o" size="24" color="#07c160" />
              <span>线上预定成功</span>
              <p>语音播报配置</p>
            </div>
          </van-grid-item>
          <van-grid-item
            class="module-item"
            @click="navigateTo('/systemConfig/corporateCulture/voice-balance')"
          >
            <div class="item-content">
              <van-icon name="volume-o" size="24" color="#ee0a24" />
              <span>余额不足</span>
              <p>语音播报配置</p>
            </div>
          </van-grid-item>
        </van-grid>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getCurrentInstance, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const { proxy } = getCurrentInstance()

// 页面导航函数
const navigateTo = (path) => {
  router.push(path)
}

// 设置页面标题
const setPageTitle = () => {
  if (proxy && proxy.$_dd) {
    proxy.$_dd.biz.navigation.setTitle({
      title: '企业文化与驾驶舱',
    })
  }
}

onMounted(() => {
  setPageTitle()
})
</script>

<style lang="scss" scoped>
.corporate-culture-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 16px;
}

.page-header {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  h2 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #323233;
  }

  .subtitle {
    margin: 0;
    font-size: 14px;
    color: #969799;
    line-height: 1.4;
  }
}

.function-modules {
  .module-section {
    background: white;
    border-radius: 12px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .section-title {
      display: flex;
      align-items: center;
      padding: 16px 20px 12px;
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      border-bottom: 1px solid #f2f3f5;

      .van-icon {
        margin-right: 8px;
        color: #1989fa;
      }
    }

    .van-grid {
      padding: 12px 16px 16px;
    }

    .module-item {
      background: #f8f9fa;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        background: #f2f3f5;
      }

      &.wide {
        .item-content {
          padding: 20px;
        }
      }

      .item-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16px 12px;
        text-align: center;

        .van-icon {
          margin-bottom: 8px;
        }

        span {
          font-size: 14px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }

        p {
          margin: 0;
          font-size: 12px;
          color: #969799;
          line-height: 1.3;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 375px) {
  .corporate-culture-container {
    padding: 12px;
  }

  .page-header {
    padding: 16px;

    h2 {
      font-size: 18px;
    }
  }
}
</style>