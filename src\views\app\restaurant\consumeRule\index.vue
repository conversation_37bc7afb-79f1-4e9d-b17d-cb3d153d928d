<template>
  <div class="skeleton-demo-container">
    <yhc-list :key="listConfig.key || 'default'" :config="listConfig" ref="listRef">
      <template #header>
        <div class="add-button-container">
          <div class="add-button" @click="onAddClick">
            <img src="/img/add.svg" class="add-icon" />
            <span>新增消费规则</span>
          </div>
        </div>
      </template>
      <template #default="{ item, index }">
        <div class="demo-item" @click.stop="onCardClick(item)">
          <div class="item-content">
            <div class="item-header">
              <div class="item-title">{{ item.title }}</div>
              <div class="item-expire" v-if="item.isExpired">过期</div>
            </div>
            <div class="item-desc">适用餐时：{{ item.apply_mealtimes }}</div>
            <div class="item-desc">适用档口：{{ item.apply_stalls }}</div>
            <!-- 显示其他标签 -->
            <div class="item-tags" v-if="item.otherTags && item.otherTags.length > 0">
              <span class="tag-item" v-for="tag in item.otherTags" :key="tag">{{ tag }}</span>
            </div>
          </div>
        </div>
      </template>
    </yhc-list>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();

const router = useRouter();
const skeletonConfig = reactive({
  isShow: false,
  count: 3,
  row: 2,
  rowWidth: ['100%', '60%', '80%'],
  avatar: true,
  avatarSize: '40px',
  avatarShape: 'round',
  title: true,
  titleWidth: '50%',
  duration: 500
})

const listRef = ref(null)
const listConfig = reactive({
  curl: {
    ls: '/consumption_rule/get_with_tags'
  },
  postData: {
    dininghall_id: localStorage.getItem('dininghall')
  },
  search: {
    isShow: true,
    isShowPopup: false
  },
  tabs: {
    isShow: false
  },
  button: {
    isShow: false,
  },
  skeleton: skeletonConfig,
  format: (data) => {
    // 数据格式化处理 - 检查tags中是否包含"已过期"和处理其他标签
    data.forEach(item => {
      if (item.tags && Array.isArray(item.tags)) {
        // 检查tags字段中是否包含"已过期"
        item.isExpired = item.tags.includes('已过期');

        // 获取除"已过期"之外的其他标签
        item.otherTags = item.tags.filter(tag => tag !== '已过期');
      } else {
        item.isExpired = false;
        item.otherTags = [];
      }
    });
  },
})
const onAddClick = () => {
  router.push('/consumeRuleAdd')
}
const onCardClick = (item) => {
  router.push({ path: "/consumeRuleDetail", query: { id: item.id } });
  console.log('proxy:2222222222', typeof item.id);
};
// console.log('proxy:2222222222', item.id);
const setRightA=() => { 
  proxy.$_dd.biz.navigation.setTitle({
    title:'消费规则'
  });
};
setRightA();
</script>

<style lang="scss" scoped>
.skeleton-demo-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.add-button-container {
  padding: 16px;
  padding-bottom: 0;
  .add-button {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    .add-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: normal;
      line-height: 22px;
      color: #323233;
    }
  }
}

.demo-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin:16px;
  background: #fff;
  border-radius: 8px;
  .item-content {
    flex: 1;
    min-width: 0; /* 确保flex子项能够收缩 */

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items:center;
      margin-bottom: 8px;
    }

    .item-title {
      font-size: 17px;
      font-weight: 500;
      line-height: 23px;
      letter-spacing: normal;
      color: #171A1D;
      flex: 1;
    }

    .item-expire {
      background: #ED6A0C;
      color: #FFFFFF;
      font-size: 12px;
      line-height: 16px;
      padding: 0.5px 5px;
      border-radius: 22px;
    }

    .item-desc {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #9E9E9E;
      margin-bottom: 8px;
    }

    .item-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .tag-item {
        // background: #E8F4FD;
        color: #1989FA;
        font-size: 12px;
        line-height: 16px;
        padding: 2.5px 5px;
        border-radius: 2px;
        display: inline-block;
        border: 0.5px solid #1989FA;
      }
    }

    .item-time {
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #9E9E9E;
    }
  }

  .item-action {
    margin-left: 12px;
  }
}
</style>
