<template>
  <div v-if="initStatus">
    <div>
      <van-popup v-model:show="show_expire" round :style="{ width: '250px', padding: '20px auto' }"
        close-on-click-overlay="false">
        <div class="notification">
          <view class="end-time" @click="end_time_click">
            <div class="end_img">
              <img src="../../static/common/lingdan.png" />
            </div>
            <p class="time-title">产品授权{{ expire_title }}</p>
            <p class="expiry-info">
              <text space="ensp">云一支付授权于
                <text class="date">{{ ` ${end_date} ` }}</text>
                日到期，请及时续费以免影响使用</text>
            </p>

            <p @click.stop="end_time_but_click">
              <van-button round type="primary" class="action-link">联系客服</van-button>
            </p>
          </view>
        </div>
      </van-popup>
    </div>
    <div class="close-icon" @click="closePopup" v-show="show_expire">
      <!-- 假设使用了一个图标字体或者图片作为关闭按钮 -->
      <img @click="show_expire = false" src="../../static/common/delchazi.png" />
    </div>
    <!-- <div v-for="(item, index) in appList" :key="index">
      {{ item }} {{ item.ctype + item.count }} {{ item.component }} {{ item.component + index }} {{ item }}
    </div> -->
    <yhc-supply-list
      :ref="supplylist.ctype + supplylist.count"
      :key="supplylist.component + index"
      :data="supplylist"
      @isStatus="isStatus"
    ></yhc-supply-list>

    <!-- <component
      v-for="(item, index) in appList"
      :is="item.component"
      :ref="item.ctype + item.count"
      :key="item.component + index"
      :data="item"
      @isStatus="isStatus"
    ></component> -->
  </div>
</template>
<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { showToast } from "vant";
import { handleRouter } from "./components/routerList";
import { useLoginStore } from "@/store/dingLogin";

const app = useLoginStore();
const { proxy } = getCurrentInstance();
const show = ref(true);

onMounted(() => { });

let appList = ref([]);
let initStatus = ref(false);
const is_expire = ref();
const expire_title = ref("");
const end_date = ref("");
let show_expire = ref(false);
const supplylist = ref({ "title": "应用组", "ctype": "supply_list", "notice_type": "", "count": 4, "data": null, "component": "yhc-supply-list" })

const getNotify = () => {
  proxy
    .$post("index/index_v3", {})
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        appList.value = res;
        res.forEach((el) => {
          el.component = `yhc-supply-list`;
          // if (el.ctype === "supply_list") {
          // } else {
          //   // if (el.ctype === "grid" || el.ctype === "app_group") {
          //   //   el.component = "yhc-grid";
          //   // } else {
          //   //   el.component = "yhc-" + el.ctype;
          //   // }
          // }
        });
        handleRouter(res);
        setTimeout(() => {
          console.log(111111);
          const { send } = window.__BIRD || {};
          typeof send === "function" && send();
          window.performance && window.performance.mark("FMP");
          // console.log(
          //   222222,
          //   send.toString(),
          //   window.__BIRD,
          //   window.performance
          // );
        }, 2000);
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      showToast(err);
    })
    .finally(() => {
      initStatus.value = true;
    });
};
const handle_expire = () => {
  let log_data = app.loginData;
  is_expire.value = log_data.is_expire;
  end_date.value = log_data.end_date;
  switch (log_data.is_expire) {
    case 1:
      // "到期提醒"
      expire_title.value = "即将到期";
      show_expire.value = true;
      break;
    case 0:
      // "系统已到期"
      // expire_title.value = "已到期";
      // show_expire.value = true;
      break;
  }
};
const end_time_click = () => {
  if (is_expire.value === 528) {
    return;
  } else {
    show_expire.value = false;
  }
};
const end_time_but_click = () => {
  show_expire.value = false;
  proxy.$_dd.biz.util.openLink({
    url: `https://page.dingtalk.com/wow/dingtalk/act/serviceconversation?wh_biz=tm&showmenu=false&goodsCode=${app.env.VITE_APP_DT_GOODS}&corpId=${app.corpId}&token=${app.env.VITE_APP_DT_GOODS_TOKEN}`,
    onSuccess: function (result) {
      console.log(result);
    },
    onFail: function (err) {
      console.log(err);
    },
  });
};
const getSysInfo = () => {
  proxy
    .$post("config/get_info", {})
    .then((res) => {
      app.sysConfig = res.result;
    })
    .catch((err) => {
      console.log(err);
    });
};
const isStatus = (op) => {
  getNotify();
  appList.value = [];
  initStatus.value = op;
};
// getSysInfo()
getNotify();
if (sessionStorage.getItem("is_expire")) {
  handle_expire();
} else {
}
</script>
<style lang="scss" scoped>
.close-icon {
  position: fixed;
  top: 75%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  /* 确保关闭图标在弹出层之上 */
  /* 添加一些样式来美化关闭图标 */
}

.close-icon img {
  width: 40px;
}

.notification {
  padding: 20px;
}

.end-time {
  text-align: center;
  /* 文本居中 */
}

.expiry-info {
  font-size: 14px;
  /* 常规文本字体大小 */
  color: #333;
  /* 文本颜色 */
  line-height: 20px;
}

.time-title {
  font-size: 24px;
  /* 标题字体大小 */
  font-weight: bold;
  /* 加粗 */
}

.expiry-info .date {
  font-weight: bold;
  /* 日期加粗 */
  color: #ff5722;
  /* 假设的红色，突出显示日期 */
}

.end_img {
  width: 172upx;
  height: 172upx;
}

.end_img img {
  width: 57.31px;
  overflow: hidden;
}

.action-link {
  width: 100%;
}

:deep .custom-popup .van-popup__wrapper {
  /* 可能需要根据你的具体布局和vant版本调整 */
  transform: translateY(-30px) !important;
  /* 注意使用 !important 可能会覆盖其他样式，需要谨慎 */
}
</style>
