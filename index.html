<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover, user-scalable=0">
  <title><%- title %></title>
  <script>
    !(function (t) {
      function e() {
        var e = this || self;
        (e.globalThis = e), delete t.prototype._T_;
      }
      "object" != typeof globalThis &&
        (this
          ? e()
          : (t.defineProperty(t.prototype, "_T_", {
              configurable: !0,
              get: e,
            }),
            _T_));
    })(Object);
  </script>
</head>

<body>
  <div id="app"></div>
  <script src="https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js" referrerpolicy="origin"></script>
  <script type="module" src="/src/main.js"></script>
  <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
  <script>
    // VConsole 默认会挂载到 `window.VConsole` 上
    var vConsole = new window.VConsole();
  </script>
</body>

</html>