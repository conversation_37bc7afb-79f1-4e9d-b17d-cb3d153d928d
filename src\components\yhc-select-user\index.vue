<template>
  <div class="wrapper">
    <!-- <van-cell center :title="config.label" :value="text" is-link @click="onClick"></van-cell> -->
    <van-field v-model="text" is-link readonly :name="config.key" :label="config.label"
      :placeholder="config.placeholder" :border="config.border" :rules="config.rules" :disabled="config.disabled"
      :input-align="config.inputAlign" @click="onClick" />
  </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance } from "vue";
import { deepAssign } from "@/untils";
import { showToast } from "vant";
import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();
const emits = defineEmits(["change"]);
const { proxy } = getCurrentInstance();
let config = {
  // 基础配置
  label: "选择用户",       // 字段标签 (字符串) - 显示在输入框左侧的标签文字
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"selected_users", "assignees"
  placeholder: "请选择",    // 占位符 (字符串) - 未选择用户时显示的提示文字
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用选择, false: 可正常选择
  rules: [],               // 验证规则 (数组) - 表单验证规则配置

  // 选择器配置
  type: "complexPicker",   // 选择器类型 (字符串) - "complexPicker": 复杂选择器(支持部门+用户)
  multiple: true,          // 是否多选 (布尔值) - true: 支持选择多个用户/部门, false: 单选模式

  // 样式配置
  border: true,            // 是否显示边框 (布尔值) - true: 显示下边框, false: 无边框
  inputAlign: "left",      // 输入内容对齐方式 (字符串) - "left": 左对齐, "center": 居中, "right": 右对齐
};
let text = ref("");
const props = defineProps({
  config: Object,
  form: Object,
});
props.config && deepAssign(config, props.config);
const setText = (departments, users) => {
  if (departments.length) {
    text.value = `已选择${departments.length}个部门、${users.length}个人员`;
  } else {
    if (users.length < 3 && app.browserEnv != "wx") {
      text.value = users.map((el) => el.name).toString();
    } else {
      text.value = `已选择${users.length}个人员`;
    }
  }
};
if (props.form[config.key]) {
  var { departments = [], users = [] } = props.form[config.key];
  console.log(888888, props.form);

  setText(departments, users);
}
const onClick = () => {
  let selectUsers = users ? users.map((item) => item.userid) : [];
  let selectDeparments = departments
    ? departments.map((item) => item.dept_id)
    : [];
  if (app.browserEnv == "wx") {
    proxy.$_dd.selectEnterpriseContact({
      fromDepartmentId: 0,
      mode: config.multiple ? "multi" : "single",
      type: ["department", "user"],
      selectedDepartmentIds: selectDeparments,
      selectedUserIds: selectUsers,
      success (res) {
        console.log(666666, res);
        res = res.result;
        users = res.userList.map((it) => {
          return {
            userid: it.id,
            name: "",
            avatar: "",
          };
        });
        departments = res.departmentList.map((it) => {
          return {
            dept_id: it.id,
            name: "",
          };
        });
        setText(departments, users);
        props.form[config.key] = {
          departments: departments,
          users: users,
        };
        emits("change", props.form);
      },
    });
  } else {
    proxy.$_dd.ready(function () {
      // proxy.$_dd.biz.contact[config.type]({
      proxy.$_dd.complexChoose({
        title: config.label, //标题
        corpId: app.corpId, //企业的corpId
        multiple: config.multiple, //是否多选
        maxUsers: 10000,
        pickedUsers: selectUsers, //已选用户
        pickedDepartments: selectDeparments, //已选部门
        appId: app.appid, //微应用的Id
        showOrgEcological: true,
        showLabelPick: true,
        rootPage: "CommonOrgContact",
        onSuccess: function (result) {
          //是否多选
          users = result.users.map((it) => {
            return {
              userid: it.emplId,
              name: it.name,
              avatar: it.avatar,
            };
          });
          departments = result.departments.map((it) => {
            return {
              dept_id: it.id,
              name: it.name,
            };
          });
          setText(departments, users);
          props.form[config.key] = {
            departments: departments,
            users: users,
          };
          emits("change", props.form);

        },
        onFail: function (err) {
          console.log(err);
        },
      }).catch((err) => {
        console.log(err);
      });
    });
  }
};
</script>
<style lang="scss" scoped>
.wrapper {
  .van-field {
    font-size: 16px;
  }
}
</style>
