<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="detail"
      :editRedirectConfig="editRedirectConfig"
      @onSubmit="onBasicSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'
const { proxy } = getCurrentInstance();

const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    id: route.query.id // 从路由获取id参数
  },
  curl: {
    info: '/dishes/get_info', // 获取详情接口
    del: '/dishes/post_del' // 删除接口
  },
  groupForm: [
    [0, 1],
    [1, 5]
  ],
form: [
    {
      label: "菜品名称",
      key: "title",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入",
      required: true,
      // 不能填
      disabled: true,
      rules: [{ required: true, message: "请填写菜品名称" }],
    },
    {
      label: "菜品分类",
      key: "select-picker",
      component: "yhc-picker",
            disabled: true,
      placeholder: "请选择",
      required: true,
      rules: [{ required: true, message: "请选择菜品分类" }],
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false
      },
      card: {
        category_id: "id",
        category_title: "title"
      },
      opts: {
        url: "/dishes_category/get_all",
        postData: {
              dininghall_id:localStorage.getItem('dininghall'),
        },
        merge: true,
        multiple: false,
        maxlength:32,
        text_key: "title",
        contrast_key: "id",
        keyMap: {
          category_id: "id",
          category_title: "title"
        },
        defaultList: []
      },
    },
    {
      label: "菜品价格",
      key: "price",
      disabled: true,
      component: "yhc-input",
      type: "number",
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请填写菜品价格" }],
    },
     {
      label: "菜品图片",
      key: "image",
      disabled: true,
      component: "yhc-select-image",
      "max-count": 1,
    },
      {
          label: "备注",
          disabled: true,
          key: "desc",
          component: "yhc-input",
          type: "textarea",
          placeholder: "请输入",
          autosize: true,
          "show-word-limit": true,
          maxlength: 200,
          desc: "支持多行文本输入，带字数限制"
        },
  ]
}

// 修改按钮跳转配置
const editRedirectConfig = {
  path: '/dishConfigAdd', // 跳转到新增页面进行编辑
  query: {
    id: route.query.id, // 传递id参数
    from: 'detail' // 标识来源
  }
}

// 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
setRight()
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: '分类详情' ,
  });
};
setRightA()
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
