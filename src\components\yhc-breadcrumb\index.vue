<template>
  <div class="yhc-breadcrumb">
    <!-- 标题显示 -->
    <div class="breadcrumb-label" v-if="config.label">
      {{ config.label }}
    </div>

    <!-- 面包屑主体 -->
    <div class="breadcrumb-wrapper" :class="{
      'breadcrumb-disabled': config.disabled,
      'breadcrumb-block': config.block
    }" :style="wrapperStyle">
      <div class="breadcrumb-item" v-for="(item, index) in breadcrumbItems" :key="getItemKey(item, index)" :class="{
        'breadcrumb-item-active': isLastItem(index),
        'breadcrumb-item-clickable': isClickable(item, index)
      }" @click="handleItemClick(item, index)">
        <!-- 图标显示 -->
        <van-icon v-if="item[config.iconKey]" :name="item[config.iconKey]" :size="config.iconSize"
          class="breadcrumb-icon" />

        <!-- 文本显示 -->
        <span class="breadcrumb-text">
          {{ item[config.textKey] || item }}
        </span>

        <!-- 分隔符 -->
        <span v-if="!isLastItem(index)" class="breadcrumb-separator" :style="separatorStyle">
          <van-icon v-if="config.separatorIcon" :name="config.separatorIcon" :size="config.separatorSize" />
          <span v-else>{{ config.separator }}</span>
        </span>
      </div>
    </div>

    <!-- 描述文本 -->
    <div class="breadcrumb-description" v-if="config.description">
      {{ config.description }}
    </div>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance } from "vue";
import { deepAssign } from "@/untils";

const { proxy } = getCurrentInstance();
const router = useRouter();

// 默认配置
let config = {
  // 基础配置
  label: "",               // 标题文本 (字符串) - 显示在面包屑上方的标签文字
  key: "",                 // 表单字段key (字符串) - 表单数据中的字段名
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用所有点击, false: 可正常点击
  block: false,            // 是否为块级元素 (布尔值) - true: 占满容器宽度, false: 内容宽度
  description: "",         // 描述文本 (字符串) - 显示在面包屑下方的说明文字

  // 数据配置
  items: [],               // 面包屑数据 (数组) - 面包屑项目，格式: [{text: "首页", path: "/", icon: "home-o"}]
  textKey: "text",         // 文本字段key (字符串) - 面包屑项显示文字对应的数据字段名
  valueKey: "value",       // 值字段key (字符串) - 面包屑项值对应的数据字段名
  pathKey: "path",         // 路径字段key (字符串) - 面包屑项路由路径对应的数据字段名
  iconKey: "icon",         // 图标字段key (字符串) - 面包屑项图标对应的数据字段名
  clickableKey: "clickable", // 可点击字段key (字符串) - 面包屑项是否可点击对应的数据字段名

  // 分隔符配置
  separator: ">",          // 分隔符文本 (字符串) - 面包屑项之间的分隔符，如">", "/", "·"
  separatorIcon: "",       // 分隔符图标 (字符串) - 使用图标作为分隔符，如"arrow"
  separatorSize: "12px",   // 分隔符尺寸 (字符串) - 分隔符的大小
  separatorColor: "#c8c9cc", // 分隔符颜色 (字符串) - 分隔符的颜色

  // 样式配置
  size: "normal",          // 尺寸 (字符串) - "small": 小尺寸, "normal": 正常尺寸, "large": 大尺寸
  iconSize: "14px",        // 图标尺寸 (字符串) - 面包屑项图标的大小

  // 颜色配置
  activeColor: "#323233",  // 激活颜色 (字符串) - 当前页面面包屑项的文字颜色
  inactiveColor: "#969799", // 非激活颜色 (字符串) - 不可点击面包屑项的文字颜色
  linkColor: "#1989fa",    // 链接颜色 (字符串) - 可点击面包屑项的文字颜色

  // 首页配置
  showHome: true,          // 是否显示首页 (布尔值) - true: 自动添加首页项, false: 不添加
  homeText: "首页",        // 首页文本 (字符串) - 首页面包屑项显示的文字
  homePath: "/",           // 首页路径 (字符串) - 首页面包屑项的路由路径

  // 高级配置
  maxItems: 0,             // 最大显示项数 (数字) - 限制显示的面包屑项数量，0表示不限制
  autoGenerate: false,     // 是否根据路由自动生成 (布尔值) - true: 根据当前路由自动生成面包屑

  // 事件配置
  onClick: null,           // 点击回调函数 (函数) - 面包屑项被点击时的回调函数
};

// Props定义
const props = defineProps({
  config: Object,
  form: Object,
});

// 合并配置
props.config && deepAssign(config, props.config);

// 计算属性
const breadcrumbItems = computed(() => {
  let items = [];

  if (config.autoGenerate) {
    // 自动根据路由生成面包屑
    items = generateFromRoute();
  } else {
    // 使用配置的items
    items = [...(config.items || [])];
  }

  // 添加首页
  if (config.showHome && items.length > 0) {
    const homeItem = {
      [config.textKey]: config.homeText,
      [config.pathKey]: config.homePath,
      [config.valueKey]: "home",
      [config.clickableKey]: true
    };
    items.unshift(homeItem);
  }

  // 限制显示数量
  if (config.maxItems > 0 && items.length > config.maxItems) {
    const start = items.length - config.maxItems + 1;
    items = [
      items[0], // 保留首页
      { [config.textKey]: "...", [config.clickableKey]: false },
      ...items.slice(start)
    ];
  }

  return items;
});

const wrapperStyle = computed(() => {
  const styles = {};

  // 尺寸样式
  if (config.size === "small") {
    styles.fontSize = "12px";
    styles.padding = "4px 0";
  } else if (config.size === "large") {
    styles.fontSize = "16px";
    styles.padding = "8px 0";
  } else {
    styles.fontSize = "14px";
    styles.padding = "6px 0";
  }

  return styles;
});

const separatorStyle = computed(() => {
  return {
    color: config.separatorColor,
    margin: "0 8px",
  };
});

// 方法定义
const getItemKey = (item, index) => {
  if (typeof item === "object" && item[config.valueKey] !== undefined) {
    return item[config.valueKey];
  }
  return index;
};

const isLastItem = (index) => {
  return index === breadcrumbItems.value.length - 1;
};

const isClickable = (item, index) => {
  if (config.disabled || isLastItem(index)) return false;

  if (typeof item === "object") {
    // 如果明确设置了clickable属性
    if (item[config.clickableKey] !== undefined) {
      return item[config.clickableKey];
    }
    // 如果有路径则默认可点击
    return !!item[config.pathKey];
  }

  return false;
};

const handleItemClick = (item, index) => {
  if (!isClickable(item, index)) {
    return;
  }

  const itemValue = getItemValue(item, index);
  const itemPath = getItemPath(item, index);

  // 更新表单值
  if (config.key && props.form) {
    props.form[config.key] = itemValue;
  }

  // 路由跳转
  if (itemPath && router) {
    router.push(itemPath);
  }

  // 触发点击事件
  if (typeof config.onClick === "function") {
    config.onClick(itemValue, item, index);
  }

  // 触发自定义事件
  emits("click", itemValue, item, index);
};

const getItemValue = (item, index) => {
  if (typeof item === "object" && item[config.valueKey] !== undefined) {
    return item[config.valueKey];
  }
  return typeof item === "string" ? item : index;
};

const getItemPath = (item, index) => {
  if (typeof item === "object" && item[config.pathKey] !== undefined) {
    return item[config.pathKey];
  }
  return null;
};

const generateFromRoute = () => {
  // 根据当前路由自动生成面包屑
  const route = router?.currentRoute?.value;
  if (!route) return [];

  const pathSegments = route.path.split('/').filter(segment => segment);
  const items = [];

  pathSegments.forEach((segment, index) => {
    const path = '/' + pathSegments.slice(0, index + 1).join('/');
    const text = segment.charAt(0).toUpperCase() + segment.slice(1);

    items.push({
      [config.textKey]: text,
      [config.pathKey]: path,
      [config.valueKey]: segment,
      [config.clickableKey]: true
    });
  });

  return items;
};

// 事件定义
const emits = defineEmits(["click"]);
</script>

<style lang="scss" scoped>
.yhc-breadcrumb {
  .breadcrumb-label {
    font-size: 14px;
    color: #323233;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .breadcrumb-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    line-height: 1.5;

    &.breadcrumb-disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &.breadcrumb-block {
      width: 100%;
    }
  }

  .breadcrumb-item {
    display: flex;
    align-items: center;
    color: #969799;

    &.breadcrumb-item-active {
      color: #323233;
      font-weight: 500;
    }

    &.breadcrumb-item-clickable {
      color: #1989fa;
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #0570d9;
      }

      &:active {
        color: #024a8a;
      }
    }
  }

  .breadcrumb-icon {
    margin-right: 4px;
  }

  .breadcrumb-text {
    white-space: nowrap;
  }

  .breadcrumb-separator {
    display: flex;
    align-items: center;
    color: #c8c9cc;
    font-size: 12px;
    user-select: none;
  }

  .breadcrumb-description {
    font-size: 12px;
    color: #969799;
    margin-top: 4px;
    line-height: 1.4;
  }
}

// 尺寸变体
.yhc-breadcrumb {
  .breadcrumb-wrapper.size-small {
    font-size: 12px;
    padding: 4px 0;

    .breadcrumb-separator {
      margin: 0 6px;
    }
  }

  .breadcrumb-wrapper.size-large {
    font-size: 16px;
    padding: 8px 0;

    .breadcrumb-separator {
      margin: 0 10px;
    }
  }
}
</style>
