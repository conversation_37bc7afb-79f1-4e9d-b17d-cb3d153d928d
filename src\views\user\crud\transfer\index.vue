<template>
  <div class="wrapper">
    <yhc-form :config="config" />
  </div>
</template>
<script setup>
import { ref } from "vue";
let config = {
  curl: {
    add: "/account/post_transfer",
    info: "",
    edit: "",
  },
  postData: {},
  search: {},

  groupForm: [
    [0, 2],
    [2, 6],
    [6, 20],
  ],
  form: [
    {
      label: "收款人员",
      key: "userlst",
      component: "yhc-select-user",
      rules: [{ required: true, message: "请选择收款人员" }],
    },
    {
      label: "转款金额",
      type: "number",
      key: "money",
      "right-icon": "/元",
      component: "yhc-input",
      rules: [{ required: true, message: "请填写转款金额" }],
    },
  ],
  button: {
    text: "转账",
  },
};
</script>
<style lang="scss" scoped>
.wrapper {
  width: 100%;
  min-height: calc(100vh - 1px);
  border-top: 1px solid #f2f3f4;
}
</style>
