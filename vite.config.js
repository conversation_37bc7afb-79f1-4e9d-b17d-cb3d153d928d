/*
 * @Author: qq <EMAIL>
 * @Date: 2024-12-03 08:55:10
 * @LastEditors: qq <EMAIL>
 * @LastEditTime: 2025-02-17 13:45:03
 * @FilePath: \eyc3_canyin_site\vite.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import vue from "@vitejs/plugin-vue";
import { defineConfig, loadEnv } from "vite";
import Components from "unplugin-vue-components/vite";
import { VantResolver } from "unplugin-vue-components/resolvers";
import { createHtmlPlugin } from "vite-plugin-html";
import AutoImport from "unplugin-auto-import/vite";

import path from "path";
function _resolve (dir) {
  return path.resolve(__dirname, dir);
}
const getViteEnv = (mode, target) => {
  return loadEnv(mode, process.cwd())[target];
};
export default (mode) => (
  {
    server: {
      host: "**********",
      port: 8088,
      hmr: true,
      proxy: {
        '/api': {
          target: 'http://files.qixuw.com/',
          changeOrigin: true,
          rewrite: (path) => {
            return path.replace(/^\/api/, '')
          }
        }
      }
    },
    build: {
      target: "es2020",
    },
    resolve: {
      alias: {
        "@": _resolve("src"),
      },
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
    },
    plugins: [
      vue(),
      createHtmlPlugin({
        inject: {
          data: {
            title: getViteEnv(mode, "VITE_APP_TITLE"),
          },
        },
      }),
      Components({
        resolvers: [
          VantResolver({
            importStyle: false,
          }),
        ],
        dirs: ["src/components"],
        extensions: ["vue"],
        // dts: 'src/components.d.ts'
      }),
      AutoImport({
        imports: ["vue", "vue-router", "pinia"],
      }),
    ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: ' @use "src/assets/styles/var.scss";',
        },
      },
    },
  }

)