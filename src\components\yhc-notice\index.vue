<template>
  <div class="wraper-notice" v-if="props.data.notice_type == 'banner' && isShow">
    <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
      <van-swipe-item v-for="(item, i) in list" :key="i" @click="onNoticeClick(item)">
        <van-image width="100%" height="100%" :alt="item.text" :src="item.image" />
      </van-swipe-item>
    </van-swipe>
  </div>
  <div v-if="props.data.notice_type == 'announce' && isShow">
    <van-notice-bar left-icon="volume-o" :scrollable="true" mode="closeable" color="#1989fa" background="#ecf9ff">
      <span v-for="(item, i) in list" :key="i" @click="onNoticeClick(item)">
        {{ item.text }}&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
      </span>
      <!-- <van-swipe
        vertical
        class="notice-swipe"
        :autoplay="3000"
        :touchable="false"
        :show-indicators="false"
      >
        <van-swipe-item
          @click="onNoticeClick(item)"
          v-for="(item, i) in list"
          :key="i"
          >{{ item.text }}</van-swipe-item>
      </van-swipe> -->
    </van-notice-bar>
  </div>
</template>
<script setup>
import { ref } from "vue";
import { showToast } from "vant";
const router = useRouter();
let { proxy } = getCurrentInstance();
// Props定义 - 通知组件配置
const props = defineProps({
  config: Object,          // 组件配置 (对象) - 预留的配置对象，暂未使用
  data: Object,            // 通知数据 (对象) - 通知组件的配置数据
});

// 通知数据结构说明:
// props.data = {
//   notice_type: "banner",  // 通知类型 (字符串) - "banner": 轮播图通知, "announce": 公告栏通知
//   count: 3,               // 显示数量 (数字) - 限制显示的通知数量，默认3条
//   autoplay: 3000,         // 自动播放间隔 (数字) - 轮播图自动切换时间，单位毫秒
// }

// 通知类型说明:
// - banner: 图片轮播形式的通知，适合重要公告和活动宣传
// - announce: 文字滚动形式的通知，适合一般性通知和提醒
const onNoticeClick = (item) => {
  router.push({
    path: "/noticeDetail",
    query: {
      id: item.id,
      hits: 1,
    },
  });
};
let list = ref([]);
let isShow = ref(false);
const getNotices = () => {
  proxy
    .$post("notice/get_all", {
      // page: 1,
      // per_page: props.data.count || 3,
      download: 0,
      status: 0,
    })
    .then((res) => {
      console.log(res);
      if (!res.errcode) {
        res = res.result.data || res.result;
        res.forEach((el) => {
          if (props.data.notice_type == "banner") {
            el.image = JSON.parse(el.image);
            list.value.push({
              image: Array.isArray(el.image) ? el.image[0] : el.image,
              id: el.id,
              text: el.title,
            });
          } else {
            list.value.push({
              text: el.title,
              id: el.id,
            });
          }
        });
        // list.value = res;
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    })
    .finally(() => {
      isShow.value = true;
    });
};
getNotices();
</script>
<style lang="scss">
.wraper-notice {
  margin: 16px;
  border-radius: 8px;
  background: #fff;
  color: #171a1d;
  font-size: 12px;
  overflow: hidden;

  .my-swipe .van-swipe-item {
    color: #fff;
    font-size: 20px;
    height: 162px;
    // background-color: #39a9ed;
  }
}

.notice-swipe {
  height: 40px;
  line-height: 40px;
}
</style>
