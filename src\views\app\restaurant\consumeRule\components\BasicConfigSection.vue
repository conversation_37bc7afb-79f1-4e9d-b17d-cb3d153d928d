<template>
  <section class="basic-config-section">
    <!-- 就餐方式 -->
    <van-cell-group inset>
      <van-field
        v-model="formData.type_text"
        name="type"
        label="就餐方式"
        input-align="left"
        placeholder="请选择就餐方式"
        readonly
        :is-link="!props.disabled"
        :disabled="props.disabled"
        @click="props.disabled ? null : handleTypeClick()"
      />
    </van-cell-group>

    <!-- 选餐模式 -->
    <van-cell-group inset>
      <van-field
        v-model="formData.temporary_text"
        name="meal_selection_mode"
        label="选餐模式"
        placeholder="请选择选餐模式"
        readonly
        :is-link="!props.disabled"
        :disabled="props.disabled"
        @click="props.disabled ? null : $emit('showDiningModeSelector')"
      />

      <!-- 预设套餐子表单 - 嵌套在父级卡片内部 -->
      <div v-if="formData.meal_selection_mode === 0" class="conditional-form">
        <van-field name="preset_menu_options" label="预设套餐" input-align="right" :disabled="props.disabled">
          <template #input>
            <van-checkbox-group v-model="formData.preset_menu_options" direction="horizontal" :disabled="props.disabled">
              <van-checkbox name="multi_booking_enabled" shape="square" :disabled="props.disabled">预定多份</van-checkbox>
              <van-checkbox name="proxy_booking_enabled" shape="square" :disabled="props.disabled">代预定</van-checkbox>
            </van-checkbox-group>
          </template>
        </van-field>
      </div>
    </van-cell-group>
  </section>
</template>

<script setup>
// Props
const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['showTypeSelector', 'showDiningModeSelector'])

// 处理就餐方式点击
const handleTypeClick = () => {
  emit('showTypeSelector')
}
</script>

<style lang="scss" scoped>
@import './common-styles.scss';
.basic-config-section { 
  .van-field {
    padding: 16px;
    font-size: 16px;
  }
}
</style>
