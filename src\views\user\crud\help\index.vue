<template>
  <!-- 点击链接后新开页面，浏览器回退时原页面不受影响 -->
   <div>
    <iframe :src="url" frameborder="0">
    </iframe>
  </div>
</template>

<script>
import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();
export default {
  data() {
    return {
      url:app.env.VITE_APP_HELP_HREF
    }
  },
  created(op) {
    console.log(app.env.VITE_APP_HELP_HREF,'qqq');
    
  },
  methods:{
    init(op){
      // console.log("页面初始化",op)
    },
  }
}
</script>

<style lang="scss">
iframe {
    width: 100vw;    /* 100%视口宽度 */
    height: 100vh;   /* 100%视口高度 */
    border: none;     /* 移除边框 */
    margin: 0;
    padding: 0;
    display: block;   /* 避免默认inline元素的空隙 */
  }
</style>
