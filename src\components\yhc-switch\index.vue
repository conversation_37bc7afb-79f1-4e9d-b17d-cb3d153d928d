<template>
  <div class="wrapper" :class="{ 'required-field': config.required }">
    <van-cell center :title="config.label" :title-style="`color:${config.disabled ? '#c8c9cc' : ''}`">
      <template #right-icon>
        <van-switch v-model="props.form[config.key]" :disabled="config.disabled" :size="config.size"
          :loading="config.loading" :name="config.key" @change="change" :active-value="1" :inactive-value="0" />
      </template>
    </van-cell>
  </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance } from "vue";
import { deepAssign } from "@/untils";
import { showToast } from "vant";
const { proxy } = getCurrentInstance();

let config = {
  // 基础配置
  label: "文本",           // 字段标签 (字符串) - 显示在开关左侧的标签文字
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"is_enabled", "status"
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段

  // 开关样式配置
  size: 22,                // 开关大小 (数字) - 开关的尺寸，单位为px，如20, 24, 30
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用开关, false: 可正常切换
  loading: false,          // 是否显示加载状态 (布尔值) - true: 显示加载动画, false: 正常状态

  // 值配置 (固定值，不建议修改)
  // active-value: 1,      // 开启时的值 - 固定为1
  // inactive-value: 0,    // 关闭时的值 - 固定为0
};
const props = defineProps({
  config: Object,
  form: Object,
});
props.config && deepAssign(config, props.config);
let emit = defineEmits(["change"]);
props.form[config.key] = props.form[config.key] || 0;
const change = (e) => {
  // console.log("yhc-switch更新了---->", e, props.form);
  emit("change", {
    components: "yhc-switch",
    key: props.config.key,
    value: e,
  });
};
</script>
<style lang="scss" scoped>
.wrapper {
  &.required-field {
    .van-cell {
      padding: 16px;
    }
  }

  .van-cell {
    font-size: 16px;
    height: 54px;
  }
}
</style>
