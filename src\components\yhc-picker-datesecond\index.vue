<template>
  <div class="wrapper">
    <!-- 弹出层 -->
    <van-popup v-model:show="data.isPicker" position="bottom" round @close="confirmOn">
      <van-picker ref="picker" title="请选择时间" :columns="data.columns" @change="onChange" @cancel="cancelOn"
        @confirm="onConfirm" v-model="data.selectedValues" />
    </van-popup>
  </div>
</template>
<script setup>
import { reactive, watch, ref, getCurrentInstance } from "vue";

const data = reactive({
  isPicker: false, //是否显示弹出层
  columns: [], //所有时间列
  selectedValues: [], //控件选择的时间值
});

// Props定义 - 日期秒选择器组件配置
const props = defineProps({
  // 弹窗显示状态
  showPicker: {
    type: Boolean,           // 是否显示选择器弹窗 (布尔值) - true: 显示选择器, false: 隐藏选择器
  },

  // 选择器类型
  type: {
    type: String,            // 选择器类型 (字符串) - "datetime": 日期时间, "date": 日期, "time": 时间, "time-full": 完整时间, "time-short": 短时间
  },

  // 日期值
  values: {
    type: String,            // 当前日期值 (字符串) - 格式如"2023-09-05 19:28:00"，用于初始化选择器
  },
});

// 事件定义 - 向父组件传递的事件
const emit = defineEmits([
  "changeValue",             // 值变化事件 - 当选择器值发生变化时触发
  "confirm"                  // 确认事件 - 当用户点击确认按钮时触发
]);

// 组件功能说明:
// - 支持精确到秒的日期时间选择
// - 支持多种时间格式的选择模式
// - 自动生成年月日时分秒的选择列
// - 支持动态更新和实时预览

watch(
  () => props.showPicker,
  (val) => {
    data.isPicker = val;
    data.columns = [];
    getcolumns();
  },
  {
    immediate: true, //立即监听--进入就会执行一次 监听显影状态
  }
);

function onChange () {
  // 无用的方法
}

function getcolumns () {
  console.log("传入数据-----------", props); // 2023-09-05 19:28:00
  // console.log(date); Wed Aug 09 2023 14:53:15 GMT+0800 (中国标准时间)
  let dateVaules;
  let Y, M, D, h, m, s;
  if (props.type === "datetime") {
    if (props.values) {
      let date = new Date(props.values.replace(/-/g, "/"));
      let timeVaules = date.getTime();
      dateVaules = new Date(timeVaules);
    } else {
      dateVaules = new Date(); //没有传入时间则默认当前时刻
    }
    Y = dateVaules.getFullYear();
    M = dateVaules.getMonth() + 1;
    D = dateVaules.getDate();
    h = dateVaules.getHours();
    m = dateVaules.getMinutes();
    s = dateVaules.getSeconds();
  } else if (props.type === "date") {
    if (props.values) {
      let date = new Date(props.values.replace(/-/g, "/"));
      let timeVaules = date.getTime();
      dateVaules = new Date(timeVaules);
    } else {
      dateVaules = new Date(); //没有传入时间则默认当前时刻
    }
    Y = dateVaules.getFullYear();
    M = dateVaules.getMonth() + 1;
    D = dateVaules.getDate();
  } else if (props.type === "time") {
    if (props.values) {
      let ls = props.values.split(":");
      h = ls[0];
      m = ls[1];
      s = ls[2];
      console.log(h, m, s);
    } else {
      dateVaules = new Date(); //没有传入时间则默认当前时刻
      h = dateVaules.getHours();
      m = dateVaules.getMinutes();
      s = dateVaules.getSeconds();
    }
  }

  let year = []; //获取前后十年数组
  year.values = [];
  let Currentday = new Date().getFullYear();
  for (let i = Currentday - 10; i < Currentday + 10; i++) {
    year.push({ text: i.toString(), value: i });
  }
  year.defaultIndex = year.values.indexOf(Y); //设置默认选项当前年

  // 个位数补0
  if (props.values) {
    const _M = M ? (M < 10 ? `0${M + 1}` : M.toString()) : undefined; //月份比实际获取的少1，所以要加1
    const _D = D ? (D < 10 ? `0${D}` : D.toString()) : undefined;
    const _h = h ? (h < 10 ? `${h}` : h.toString()) : undefined;
    const _m = m ? (m < 10 ? `${m}` : m.toString()) : undefined;
    const _s = s ? (s < 10 ? `${s}` : s.toString()) : undefined;
    // 生成年月日时分秒时间值
    Y && data.selectedValues.push(Y);
    _M && data.selectedValues.push(_M);
    _D && data.selectedValues.push(_D);
    _h && data.selectedValues.push(_h);
    _m && data.selectedValues.push(_m);
    _s && data.selectedValues.push(_s);

    Y && data.columns.push(year); //生成年列

    let month = []; //获取12月数组
    month = Object.keys(Array.apply(null, { length: 13 })).map(function (item) {
      if (+item + 1 <= 10) {
        return { text: "0" + item, value: "0" + item };
      } else if (+item + 1 == 11) {
        return { text: +item, value: +item };
      } else {
        return {
          text: (+item + 0).toString(),
          value: (+item + 0).toString(),
        };
      }
    });
    month.splice(0, 1);
    _M && data.columns.push(month); //生成月列

    //获取当月的天数
    let days = getCountDays(Y, M + 1);
    let day = []; //创建当月天数数组
    day = Object.keys(Array.apply(null, { length: days + 1 })).map(function (
      item
    ) {
      if (+item + 1 <= 10) {
        return { text: "0" + item, value: "0" + item };
      } else if (+item + 1 == 11) {
        return { text: +item, value: +item };
      } else {
        return {
          text: (+item + 0).toString(),
          value: (+item + 0).toString(),
        };
      }
    });
    day.splice(0, 1);
    _D && data.columns.push(day); //生成日列

    let hour = []; //创建小时数组
    hour = Object.keys(Array.apply(null, { length: 24 })).map(function (item) {
      if (+item + 1 <= 10) {
        return { text: "0" + item, value: "0" + item };
      } else if (+item + 1 == 11) {
        return { text: +item, value: +item };
      } else {
        return {
          text: (+item + 0).toString(),
          value: (+item + 0).toString(),
        };
      }
    });
    _h && data.columns.push(hour); //生成小时列

    let mi = []; //创建分钟数组
    mi = Object.keys(Array.apply(null, { length: 60 })).map(function (item) {
      if (+item + 1 <= 10) {
        return { text: "0" + item, value: "0" + item };
      } else if (+item + 1 == 11) {
        return { text: +item, value: +item };
      } else {
        return {
          text: (+item + 0).toString(),
          value: (+item + 0).toString(),
        };
      }
    });
    _m && data.columns.push(mi); //生成分钟列

    let ss = []; //创建秒数数组
    ss = Object.keys(Array.apply(null, { length: 60 })).map(function (item) {
      if (+item + 1 <= 10) {
        return { text: "0" + item, value: "0" + item };
      } else if (+item + 1 == 11) {
        return { text: +item, value: +item };
      } else {
        return {
          text: (+item + 0).toString(),
          value: (+item + 0).toString(),
        };
      }
    });
    _s && data.columns.push(ss); //生成秒钟列
    console.log("列数据----------》", data);
  } else {
    const _M = M ? (M < 10 ? `0${M + 1}` : M.toString()) : undefined; //月份比实际获取的少1，所以要加1
    const _D = D ? (D < 10 ? `0${D}` : D.toString()) : undefined;
    const _h = h ? (h < 10 ? `0${h}` : h.toString()) : undefined;
    const _m = m ? (m < 10 ? `0${m}` : m.toString()) : undefined;
    const _s = s ? (s < 10 ? `0${s}` : s.toString()) : undefined;
    // 生成年月日时分秒时间值
    Y && data.selectedValues.push(Y);
    _M && data.selectedValues.push(_M);
    _D && data.selectedValues.push(_D);
    _h && data.selectedValues.push(_h);
    _m && data.selectedValues.push(_m);
    _s && data.selectedValues.push(_s);

    Y && data.columns.push(year); //生成年列

    let month = []; //获取12月数组
    month = Object.keys(Array.apply(null, { length: 13 })).map(function (item) {
      if (+item + 1 <= 10) {
        return { text: "0" + item, value: "0" + item };
      } else if (+item + 1 == 11) {
        return { text: +item, value: +item };
      } else {
        return {
          text: (+item + 0).toString(),
          value: (+item + 0).toString(),
        };
      }
    });
    month.splice(0, 1);
    _M && data.columns.push(month); //生成月列

    //获取当月的天数
    let days = getCountDays(Y, M + 1);
    let day = []; //创建当月天数数组
    day = Object.keys(Array.apply(null, { length: days + 1 })).map(function (
      item
    ) {
      if (+item + 1 <= 10) {
        return { text: "0" + item, value: "0" + item };
      } else if (+item + 1 == 11) {
        return { text: +item, value: +item };
      } else {
        return {
          text: (+item + 0).toString(),
          value: (+item + 0).toString(),
        };
      }
    });
    day.splice(0, 1);
    _D && data.columns.push(day); //生成日列

    let hour = []; //创建小时数组
    hour = Object.keys(Array.apply(null, { length: 24 })).map(function (item) {
      if (+item + 1 <= 10) {
        return { text: "0" + item, value: "0" + item };
      } else if (+item + 1 == 11) {
        return { text: +item, value: +item };
      } else {
        return {
          text: (+item + 0).toString(),
          value: (+item + 0).toString(),
        };
      }
    });
    _h && data.columns.push(hour); //生成小时列

    let mi = []; //创建分钟数组
    mi = Object.keys(Array.apply(null, { length: 60 })).map(function (item) {
      if (+item + 1 <= 10) {
        return { text: "0" + item, value: "0" + item };
      } else if (+item + 1 == 11) {
        return { text: +item, value: +item };
      } else {
        return {
          text: (+item + 0).toString(),
          value: (+item + 0).toString(),
        };
      }
    });
    _m && data.columns.push(mi); //生成分钟列

    let ss = []; //创建秒数数组
    ss = Object.keys(Array.apply(null, { length: 60 })).map(function (item) {
      if (+item + 1 <= 10) {
        return { text: "0" + item, value: "0" + item };
      } else if (+item + 1 == 11) {
        return { text: +item, value: +item };
      } else {
        return {
          text: (+item + 0).toString(),
          value: (+item + 0).toString(),
        };
      }
    });
    _s && data.columns.push(ss); //生成秒钟列
    console.log("列数据----------》", data);
  }

}

function getCountDays (year, month) {
  //获取某年某月多少天
  let day = new Date(year, month, 0);
  return day.getDate();
}

// 关闭弹框
function confirmOn () {
  emit("changeValue");
}

//时间选择器关闭 值不改变并关闭弹框
function cancelOn ({ selectedValues }) {
  confirmOn();
}

// 时间选择器确定 值改变
function onConfirm ({ selectedValues }) {
  let endval;
  if (props.type == "datetime") {
    endval =
      selectedValues[0] +
      "-" +
      selectedValues[1] +
      "-" +
      selectedValues[2] +
      " " +
      selectedValues[3] +
      ":" +
      selectedValues[4] +
      ":" +
      selectedValues[5];
  } else if (props.type == "date") {
    endval =
      selectedValues[0] + "-" + selectedValues[1] + "-" + selectedValues[2];
  } else if (props.type == "time") {
    endval =
      selectedValues[0] + ":" + selectedValues[1] + ":" + selectedValues[2];
  }
  confirmOn();
  emit("confirm", endval);
}
</script>
<style lang="scss" scoped>
.wrapper {}
</style>
