<template>
  <div class="online-booking-section">
    <div class="form-group">
      <van-cell-group inset>
        <van-cell 
          title="线上预订" 
          :value="onlineBookingText"
          is-link
          :disabled="loading"
          @click="handleNavigateToOnlineBook"
        />
      </van-cell-group>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:formData', 'navigate-to-online-book'])

// 本地表单数据
const localFormData = computed({
  get: () => props.formData,
  set: (value) => emit('update:formData', value)
})

// 线上预订显示文本
const onlineBookingText = computed(() => {
  // 检查是否有线上预订相关配置
  const hasOnlineConfig = 
    localFormData.value.online_booking_enabled ||
    localFormData.value.meal_selection_mode ||
    localFormData.value.booking_deadline_type ||
    localFormData.value.charge_method ||
    localFormData.value.unified_deadline_time ||
    localFormData.value.cancel_unified_deadline_time ||
    (localFormData.value.mealtime_deadlines && localFormData.value.mealtime_deadlines.length > 0)
  
  return hasOnlineConfig ? '已配置' : '未配置'
})

// 处理导航到线上预订页面
const handleNavigateToOnlineBook = () => {
  emit('navigate-to-online-book')
}
</script>

<style lang="scss" scoped>
.online-booking-section {
  .form-group {
    margin-bottom: 12px;
  }
  
  .van-cell-group {
    margin: 0 16px;
  }
}
</style>
