<template>
  <div class="select-role-container">
    <!-- 搜索栏 -->
    <div class="search-header">
      <van-search
        v-model="searchValue"
        placeholder="搜索角色"
        background="transparent"
        shape="round" 
        :show-action="false"
      />
    </div>
    
    <!-- 角色列表 -->
    <div class="role-list">
      <van-checkbox-group v-model="selectedRoles">
        <template v-for="role in filteredRoles" :key="role.id">
          <div class="role-item">
            <van-checkbox :name="role.id" class="role-checkbox">
              <div class="role-info">
                <div class="role-name">{{ role.name }}</div>
                <div class="role-desc">{{ role.description || '暂无描述' }}</div>
                <div class="role-count">{{ role.memberCount || 0 }}人</div>
              </div>
            </van-checkbox>
          </div>
        </template>
      </van-checkbox-group>
      
      <!-- 空状态 -->
      <van-empty 
        v-if="filteredRoles.length === 0" 
        description="暂无角色数据"
        image="search"
      />
    </div>
    
    <!-- 底部操作栏 -->
    <van-action-bar class="action-bar">
      <van-action-bar-icon 
        icon="clear" 
        text="清空" 
        @click="clearSelection"
        v-if="selectedRoles.length > 0"
      />
      <van-action-bar-button 
        type="primary" 
        :text="`确定(${selectedRoles.length})`"
        @click="confirmSelection"
      />
    </van-action-bar>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useLoginStore } from '@/store/dingLogin'

const router = useRouter()
const app = useLoginStore()

// 搜索值
const searchValue = ref('')

// 选中的角色
const selectedRoles = ref([])

// 模拟角色数据
const roleList = ref([
  {
    id: '1',
    name: '管理员',
    description: '系统管理员角色',
    memberCount: 5
  },
  {
    id: '2',
    name: '财务',
    description: '财务管理角色',
    memberCount: 8
  },
  {
    id: '3',
    name: '人事',
    description: '人事管理角色',
    memberCount: 3
  },
  {
    id: '4',
    name: '销售',
    description: '销售人员角色',
    memberCount: 15
  },
  {
    id: '5',
    name: '技术',
    description: '技术开发角色',
    memberCount: 12
  },
  {
    id: '6',
    name: '运营',
    description: '运营管理角色',
    memberCount: 6
  },
  {
    id: '7',
    name: '客服',
    description: '客户服务角色',
    memberCount: 10
  },
  {
    id: '8',
    name: '市场',
    description: '市场推广角色',
    memberCount: 7
  }
])

// 过滤后的角色列表
const filteredRoles = computed(() => {
  if (!searchValue.value) {
    return roleList.value
  }
  return roleList.value.filter(role => 
    role.name.toLowerCase().includes(searchValue.value.toLowerCase()) ||
    (role.description && role.description.toLowerCase().includes(searchValue.value.toLowerCase()))
  )
})

// 页面加载
onMounted(() => {
  // 如果有之前选择的角色，恢复选择状态（使用清空欠款的状态）
  if (app.clearDebt.visiterList && app.clearDebt.visiterList.length > 0) {
    selectedRoles.value = app.clearDebt.visiterList.map(item => item.id || item.roleId)
  }
  
  // 加载角色列表
  loadRoleList()
})

// 加载角色列表
const loadRoleList = async () => {
  try {
    // 这里可以调用真实的API
    // const response = await proxy.$get('role/list')
    // roleList.value = response.data
    
    // 目前使用模拟数据
    console.log('角色列表加载完成')
  } catch (error) {
    console.error('加载角色列表失败:', error)
    showToast('加载角色列表失败')
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 清空选择
const clearSelection = () => {
  selectedRoles.value = []
}

// 确认选择
const confirmSelection = () => {
  if (selectedRoles.value.length === 0) {
    showToast({
      message: '请选择角色',
      type: 'text',
      duration: 2000
    })
    return
  }
  
  // 构造选中的角色数据
  const selectedRoleData = roleList.value.filter(role => 
    selectedRoles.value.includes(role.id)
  ).map(role => ({
    id: role.id,
    roleId: role.id,
    name: role.name,
    description: role.description,
    memberCount: role.memberCount
  }))
  
  // 保存到store（使用清空欠款的状态）
  app.clearDebt.visiterList = selectedRoleData
  
  showToast(`已选择${selectedRoles.value.length}个角色`)
  
  // 返回上一页
  setTimeout(() => {
    router.go(-1)
  }, 1000)
}
</script>

<style lang="scss" scoped>
.select-role-container {
  background: #f2f3f4;
  min-height: 100vh;
  padding-bottom: 60px;
}

.search-header {
  background: #fff;
  padding: 8px 16px;
  border-bottom: 1px solid #ebedf0;
  
  :deep(.van-search) {
    padding: 0;
    
    .van-search__content {
      background: #f7f8fa;
      border-radius: 20px;
    }
  }
}

.role-list {
  padding: 8px 0;
}

.role-item {
  background: #fff;
  margin: 8px 16px;
  border-radius: 8px;
  overflow: hidden;
  
  .role-checkbox {
    width: 100%;
    padding: 16px;
    
    :deep(.van-checkbox__label) {
      width: 100%;
      margin-left: 8px;
    }
  }
  
  .role-info {
    width: 100%;
  }
  
  .role-name {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 4px;
  }
  
  .role-desc {
    font-size: 14px;
    color: #969799;
    margin-bottom: 4px;
  }
  
  .role-count {
    font-size: 12px;
    color: #c8c9cc;
  }
}

.action-bar {
  :deep(.van-action-bar-button) {
    background: #1989fa;
  }
}
</style>
