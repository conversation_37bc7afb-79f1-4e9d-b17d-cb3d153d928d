<template>
  <div class="wrapper-home">
    <div class="top-block">
      <div class="dininghall-date">
        <van-dropdown-menu>
          <van-dropdown-item v-model="curDininghall" :options="option" @change="dingdingChange" />
        </van-dropdown-menu>
        <div class="report" :style="`${reportOrOrder !== 0 && 'color:rgba(255,255,255,0.5);'}`"
          @click="onOrderClick(0)">
          <van-icon style="margin-bottom: 4px" class-prefix="iconfont" name="diancan-2" class="van-haptics-feedback"
            size="16" />
          报餐
        </div>
        <div class="order" :style="`${reportOrOrder !== 1 && 'color:rgba(255,255,255,0.5)'}`" @click="onOrderClick(1)">
          <van-icon style="margin-bottom: 4px" class-prefix="iconfont" name="canting" class="van-haptics-feedback"
            size="16" />
          点餐
        </div>
      </div>
      <van-row>
        <van-col :class="`date-block ${i === curDateIndex ? 'cur-date' : ''}`" v-for="(item, i) in dateLs" :key="i"
          @click="onDateClick(i)">
          <div style="margin-bottom: 5px">{{ item.date }}</div>
          <div>{{ item.week }}</div>
        </van-col>
        <van-col class="date-block date">
          <van-popover v-model:show="showPopover" placement="bottom-end" overlay>
            <template #reference>
              <van-icon name="calendar-o" size="24" style="margin-bottom: 5px" />
              <div>日历</div>
            </template>
            <van-calendar title="日历" :default-date="new Date(nowDate)" :poppable="false" :show-confirm="true"
              :style="{ height: '460px', width: '310px' }" row-height="40" :formatter="formatter" @monthShow="monthShow"
              @select="onDateSelect" @confirm="showPopover = false" ref="calendar" />
          </van-popover>
        </van-col>
      </van-row>
    </div>
    <div v-if="isShow">
      <van-notice-bar left-icon="volume-o" :scrollable="true" mode="closeable" color="#007FFF" background="#E0EDFE">
        <span v-for="(item, i) in list" :key="i" @click="onNoticeClick(item)">
          {{ item.text }}&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
        </span>
      </van-notice-bar>
    </div>
    <div class="window-info">
      <div class="left">
        <div :class="`repast-time ${i === curMealtimeIndex ? 'active-repast' : ''
          }`" v-for="(item, i) in windowData" :key="i" @click="onRepastClick(i)">
          <!-- <div class="select-line" v-show="i === curMealtimeIndex"></div> -->
          <div class="text-info">
            <!-- <div class="icon">
              <van-icon name="qr" class="van-haptics-feedback" size="24" />
            </div> -->
            <div class="repast-text">{{ item.repast_title }}</div>
          </div>
        </div>
      </div>
      <div class="right" v-if="windowData[curMealtimeIndex]">
        <div class="window-dish" v-for="(item, i) in windowData[curMealtimeIndex].windows" :key="item.window_id + i">
          <div class="title-block">
            <div style="width: 80%">
              <span>{{ item.window_title + " " }}</span>
              <span style="white-space: nowrap">{{ item.repast_start_time }}-{{ item.repast_end_time }}</span>
              <span style="color: #ff9200">（{{ windowData[curMealtimeIndex].timeOutText }}）</span>
            </div>
            <span v-if="item.price" style="color: #ff9200; margin-left: 16px">￥{{ item.price }}</span>
          </div>
          <div class="dish-block" @click="onDishClick(item)" v-if="item.dishess.length">
            <div class="dish-item" v-for="(dish, i) in item.dishess" :key="'dish' + i">
              <van-image v-if="dish.image" width="50" height="50" radius="4" :src="dish.image ||
                'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
                " />
              <div v-else style="
                  width: 50px;
                  height: 50px;
                  border-radius: 4px;
                  background: #007fff;
                  color: #fff;
                  font-size: 25px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-bottom: 5px;
                ">
                {{ dish.title[0] }}
              </div>
              <div class="title">{{ dish.title }}</div>
            </div>
          </div>
          <div class="dish-block" v-else>
            <div class="dish-item">
              <div style="
                  width: 50px;
                  height: 50px;
                  border-radius: 4px;
                  background: #007fff;
                  color: #fff;
                  font-size: 25px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-bottom: 5px;
                ">
                {{ windowData[curMealtimeIndex].repast_title[0] }}
              </div>
              <div class="title">
                {{ windowData[curMealtimeIndex].repast_title }}
              </div>
            </div>
          </div>
          <div class="bottom-block">
            <div>
              <span v-if="item.apply_margin">剩余:{{ item.apply_margin || 0 }}份</span>
              <span v-if="item.apply_end_time">报餐结束：{{ item.apply_end_time }}</span>
            </div>

            <div>
              <van-button :plain="!!item.apply_status" hairline type="primary" size="small" :disabled="(!(item.apply * 1) && !reportOrOrder) ||
                (reportOrOrder && !(item.order * 1))
                " :loading="item.loading" @click="onReportClick(item)">{{
                  reportOrOrder ? "点餐" : item.apply_status ? "撤销" : "报餐"
                }}</van-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-dialog v-model:show="isShowInput" title="报餐数量" show-cancel-button @confirm="onInpuConfirm">
      <div style="margin: 16px; display: flex; align-items: center">
        <van-field v-model="reportNumber" label="" placeholder="请输入报餐数量">
          <template #button> /份 </template></van-field>
      </div>
    </van-dialog>
  </div>
</template>
<script setup>
import { ref, getCurrentInstance } from "vue";
import { showNotify, showToast, closeNotify, ActionSheet } from "vant";
import dayjs from "dayjs";
import { useLoginStore } from "@/store/dingLogin";
import { routerList, set_permission } from "@/views/home/<USER>/routerList";

const router = useRouter();
const app = useLoginStore();
app.item = null;
// console.log("全局数据-------》", app);
let { proxy } = getCurrentInstance();
var router_list = [
  {
    title: "窗口管理",
    url: "/manageWindow",
    icon: "console_window",
    app: "window",
    id: 2,
  },
  {
    title: "餐时添加",
    url: "/manageMealTime",
    icon: "console_mealtime",
    app: "repast",
    id: 4,
  },
  {
    title: "餐时组",
    url: "/manageMealTimeGroup",
    icon: "console_mealtimegroup",
    app: "repast_group",
    id: 5,
  },
  {
    title: "餐补方案",
    url: "/manageSubsidy",
    icon: "console_subsidy",
    app: "subsidy",
    id: 6,
  },
  {
    title: "考勤就餐",
    url: "/manageAttendance",
    icon: "console_attendance",
    app: "attendance",
    id: 7,
  },
  {
    title: "报餐方案",
    url: "/mealReporting",
    icon: "console_reportmeal",
    app: "apply",
    id: 8,
  },
  {
    title: "点餐方案",
    url: "/manageOrderPlan",
    icon: "console_order",
    app: "order",
    id: 9,
  },
  {
    title: "就餐方案",
    url: "/manageMealPlan",
    icon: "console_scheme",
    app: "plan",
    id: 10,
  },
  {
    title: "菜品分类",
    url: "/dishType",
    icon: "console_appi",
    app: "dishes_category",
    id: 11,
  },
  {
    title: "菜品添加",
    url: "/manageDish",
    icon: "console_Addto",
    app: "dishes",
    id: 12,
  },
  {
    title: "菜品发布",
    url: "/app/dishes_public",
    icon: "console_publish",
    app: "dishes_release",
    id: 13,
  },
  {
    title: "餐费设置",
    url: "/app/subpage/mealSetting",
    icon: "console_Meal",
    app: "repast_cost",
    id: 14,
  },
  {
    title: "点餐",
    url: "../index/subpage/order_index/order_index",
    icon: "console_order2",
    app: "diancan_online",
    id: 37,
  },
  {
    title: "访客配置",
    url: "/pages/work/sub_page/plan_visitor/plan_visitor",
    icon: "icon_Guest Settings",
    app: "visitor_config",
    id: 39,
  },
  {
    title: "库存管理",
    url: "/manage_stock",
    icon: "goods_stock",
    app: "erp_stock",
    id: 34,
  },
  {
    title: "商品管理",
    url: "/managementGoods",
    icon: "goods_manage",
    app: "erp_goods",
    id: 33,
  },
  {
    title: "商品类型",
    url: "/manage_type",
    icon: "goods_type",
    app: "erp_category",
    id: 31,
  },
  {
    title: "商品单位",
    url: "/manage_unit",
    icon: "goods_unit",
    app: "erp_unit",
    id: 32,
  },
  {
    title: "商品出库",
    url: "/product_outbound",
    icon: "goods_outlog",
    app: "erp_stock_out",
    id: 45,
  },
  {
    title: "商品入库",
    url: "/stock_in",
    icon: "goods_inlog",
    app: "erp_stock_enter",
    id: 35,
  },
  {
    title: "设备管理",
    url: "/app/manage_dev",
    icon: "console_equipment",
    app: "device",
    id: 3,
  },
  {
    title: "餐厅管理",
    url: "/manageDininghall",
    icon: "console_canteen",
    app: "dininghall",
    id: 1,
  },
  {
    title: "统计查询",
    url: "/statistics",
    icon: "console_statistics",
    app: "datav",
    id: 22,
  },
  {
    title: "权限管理",
    url: "/authority",
    icon: "permission_group",
    app: "permission_group",
    id: 15,
  },
  {
    text: "访客设置",
    url: "/visitor",
    icon: "visitor_config",
    color: "rgba(0,186,70, 1.0)",
    app: "visitor_config",
    id: 39,
  },
  {
    id: "40",
    title: "审计日志",
    url: "/operationRecords",
    icon: "icon_record",
    app: "operate_log",
  },
  {
    title: "充值",
    url: "/accountTopup",
    icon: "console_recharge",
    app: "recharge",
    id: 16,
  },
  {
    title: "充值计划",
    url: "/plan_deposit",
    icon: "console_plan",
    app: "recharge_cron",
    id: 17,
  },
  {
    title: "充值记录",
    url: "/rechargeRecord",
    icon: "console_Record",
    app: "recharge_log",
    id: 27,
  },
  {
    title: "余额管理",
    url: "/app/balance",
    icon: "console_balance",
    app: "balance",
    id: 18,
  },
  {
    title: "账户扣款",
    url: "/accountdeduction",
    icon: "console_debit",
    app: "account_deduction",
    id: 19,
  },
  {
    title: "余额清空",
    url: "/plan_clean",
    icon: "recharge_cron",
    app: "recharge_cron",
    color: "rgba(255,113,61, 1.0)",
  },
  {
    title: "成本中心",
    url: "/costCenter",
    icon: "cost",
    app: "cost",
    id: 43,
  },
  // {
  //   title: "支付设置",
  //   url: "",
  //   icon: "",
  //   app: "pay_config",
  //   id: 24,
  // },
  {
    title: "语音设置",
    url: "/voiceSetting",
    icon: "console_customvoice",
    app: "voice",
    id: 23,
  },
  {
    title: "优惠券",
    url: "/coupon",
    icon: "console_juan",
    app: "coupon",
    id: 36,
  },
  {
    title: "推送通知",
    url: "/information",
    icon: "console_pushnotification",
    app: "message_config",
    id: 21,
  },
  {
    title: "系统设置",
    url: "/app/settings",
    icon: "console_systemsettings",
    app: "config",
    id: 20,
  },
  {
    title: "反馈管理",
    url: "/pages/work/sub_page/feedback_management/feedback_management",
    icon: "console_info",
    app: "feedback_manage",
    id: 26,
  },
  {
    title: "餐厅反馈",
    url: "/pages/work/sub_page/feedback_user/feedback_user",
    icon: "console_info",
    app: "feedback_dininghall",
    id: 25,
  },
  {
    title: "会议餐",
    url: "/meetMeal",
    icon: "meeting",
    app: "meeting",
    id: 41,
  },
  {
    title: "自动报餐",
    url: "/auto_report",
    icon: "apply_auto",
    app: "apply_auto",
    id: 42,
  },
  {
    title: "报表",
    url: "",
    app: "",
    id: 29,
  },
  {
    title: "帐单列表",
    url: "/appbill",
    icon: "console_info",
    app: "bill_ls",
    id: 46,
  },
  {
    title: "公告管理",
    url: "/manageNotice",
    icon: "announcement",
    class: "",
    app: "notice",
    id: 47,
  },
  {
    title: "包间管理",
    url: "/privateRoom",
    icon: "room_manage_nav",
    class: "",
    app: "entertain_room",
    id: 48,
  },
  {
    title: "包间预定",
    url: "/roomPrivate",
    icon: "room_private",
    class: "",
    app: "entertain_apply",
    id: 49,
  },
  {
    title: "付款码",
    url: "/payment",
    icon: "payment_code",
    class: "",
    app: "payment_code",
    id: 50,
  },
  {
    title: "食材单位",
    url: "/ingredientsUnit",
    icon: "erp_unit",
    id: 51,
    app: "psi_unit",
  },
  {
    text: "食材分类",
    url: "/ingredientsType",
    icon: "erp_category",
    id: 52,
    app: "psi_category",
  },
  {
    text: "食材管理",
    url: "/managementFoods",
    icon: "erp_goods",
    id: 53,
    app: "psi_food",
  },
  {
    text: "库存管理",
    url: "/managementInventory",
    icon: "erp_stock",
    id: 54,
    app: "psi_stock",
  },
  {
    text: "供应商",
    url: "/supplier",
    icon: "supplier",
    id: 55,
    app: "psi_supplier",
  },
  {
    text: "采购需求",
    url: "/procureDemand",
    icon: "Purchasing_demand",
    id: 56,
    app: "psi_demand",
  },
  {
    text: "采购订单",
    url: "/purchaseOrder",
    icon: "Purchase_order",
    id: 57,
    app: "psi_order",
  },
  {
    text: "食材溯源",
    url: "/tracea_food_ingredients",
    icon: "Food_source",
    id: 58,
    app: "psi_trace",
  },
  {
    text: "采购出库",
    url: "/purchOutbound",
    icon: "erp_stock_out",
    id: 59,
    app: "psi_stock_out",
  },
  {
    text: "访客管理",
    url: "/manageVisit",
    icon: "erp_stock_out",
    id: 60,
    app: "visitor",
  },
];
const curDininghall = ref(0);
let curDateIndex = ref(0);
let curMealtimeIndex = ref(0);
let nowDate = ref("");
let active = ref("");
let windowData = ref([]);
let reportOrOrder =
  app.homeCofig && app.homeCofig.reportOrOrder
    ? ref(app.homeCofig.reportOrOrder)
    : ref(0);
const showPopover = ref(false);
const dishShow = ref(false);
const dishDetail = ref({});
let option = [];
let config = {
  curl: {
    ls: "dininghall/get_all",
    window: "apply/index",
    dingdingChange: "dininghall/post_toggle",
  },
  postData: {},
};
const onNoticeClick = (item) => {
  router.push({
    path: "/noticeDetail",
    query: {
      id: item.id,
      hits: 1,
    },
  });
};
let list = ref([]);
let isShow = ref(false);
const getNotices = () => {
  proxy
    .$post("notice/get_all", {
      // page: 1,
      // per_page: props.data.count || 3,
      download: 0,
      status: 0,
    })
    .then((res) => {
      console.log(res);
      if (!res.errcode) {
        res = res.result.data || res.result;
        res.forEach((el) => {
          el.image = JSON.parse(el.image);
          list.value.push({
            image: Array.isArray(el.image) ? el.image[0] : el.image,
            id: el.id,
            text: el.title,
          });
          // if (props.data.notice_type == "banner") {
          // } else {
          //   list.value.push({
          //     text: el.title,
          //     id: el.id,
          //   });
          // }
        });
        // list.value = res;
      } else {
        // throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      // showToast(err);
    })
    .finally(() => {
      isShow.value = true;
    });
};
getNotices();
const onDateClick = (index) => {
  curDateIndex.value = index;
  nowDate.value = dateLs[index].dayjs.format("YYYY-MM-DD");
  getWindow();
};
const onDishClick = (item) => {
  app.orderDishSelectList = undefined;
  let dishess = item.dishess;
  let obj = {};
  dishess.forEach((dish) => {
    if (obj[dish.category_title]) {
      obj[dish.category_title].push(dish);
    } else {
      obj[dish.category_title] = [dish];
      !active.value && (active = dish.category_title);
    }
  });
  app.indexDishDetail = obj;
  router.push({
    path: "/orderDishDetail",
  });
};
const onOrderClick = (e) => {
  reportOrOrder.value = e;
  if (app.homeCofig) {
    app.homeCofig.reportOrOrder = e;
  } else {
    app.homeCofig = {
      reportOrOrder: e,
    };
  }
  getWindow();
};
const monthShow = (date) => {
  // console.log("monthShow--------------->", date,nowDate);
};
const formatter = (day) => {
  // console.log("day数据-------》",day)
  return day;
};
let report_data = null;
let postUrl = "";
let isShowInput = ref(false);
let reportNumber = ref(null);
let curItem = ref(null);
const onInpuConfirm = (e) => {
  report_data.count = reportNumber.value;
  if (report_data.advanced) {
    app.report_data = report_data;
    report_data.repast_title =
      windowData.value[curMealtimeIndex.value].repast_title;
    app.select_order_list = report_data.dishess;
    // showToast(`高级报餐未开放`);
    router.push({
      name: "reportSenior",
      // url: "/reportSenior",
      // params: report_data,
    });
  } else {
    post_reapst(postUrl, report_data);
  }
};
const onReportClick = (item) => {
  console.log("报餐点餐-------》", item);
  curItem.value = item;
  if (reportOrOrder.value) {
    //点餐
    item.date = nowDate.value;
    app.orderDishSelectList = undefined;
    app.item = item;
    router.push({
      path: "/orderMeal",
      query: item,
    });
  } else {
    //报餐
    report_data = {
      repast_id: item.repast_id,
      window_id: item.window_id,
      repast_title: item.repast_title,
      window_title: item.window_title,
      date: nowDate.value,
    };

    if (!item.apply_status) {
      // console.log("报餐------》", !item.apply_status);
      postUrl = "apply/post_apply_add";
      if (item.advanced || item.dept || item.multi) {
        report_data.dishess = item.dishess;
        report_data.advanced = item.advanced;
        item.date = nowDate.value;
        app.item = item;
        router.push({
          path: "/report/confirm",
        });
      } else {
        post_reapst(postUrl, report_data);
      }
      // if (item.multi) {
      //   proxy.$_dd.device.notification.actionSheet({
      //     title: "报餐", //标题
      //     cancelButton: "取消", //取消按钮文本
      //     otherButtons: ["单人报餐", "多人报餐"],
      //     onSuccess: (result) => {
      //       if (result.buttonIndex === -1) {
      //         return;
      //       }
      //       report_data.dishess = item.dishess;
      //       report_data.advanced = item.advanced;
      //       if (result.buttonIndex === 0) {
      //         report_data.count = 1;
      //         if (report_data.advanced) {
      //           report_data.price = item.price;
      //           report_data.repast_title =
      //             windowData.value[curMealtimeIndex.value].repast_title;
      //           app.report_data = report_data;
      //           app.select_order_list = report_data.dishess;
      //           router.push({
      //             name: "reportSenior",
      //             // url: "/reportSenior",
      //             // params: report_data,
      //           });
      //         } else {
      //           post_reapst(postUrl, report_data);
      //         }
      //       }
      //       if (result.buttonIndex === 1) {
      //         report_data.price = item.price;
      //         isShowInput.value = true;
      //       }
      //     },
      //     onFail(err) {},
      //   });
      //   return;
      // }
    } else {
      postUrl = "apply/post_apply_cancel";
      report_data = {
        bill_id: item.bill_id,
      };
      if (item.cancel_approval) {
        proxy.$_dd.device.notification.actionSheet({
          title: "取消报餐", //标题
          cancelButton: "取消", //取消按钮文本
          otherButtons: ["因公退费", "因私退费"],
          onSuccess: (result) => {
            if (result.buttonIndex === -1) {
              return;
            }
            report_data.cancel_type = result.buttonIndex;
            post_reapst(postUrl, report_data);
          },
          onFail(err) { },
        });
        return;
      }
      post_reapst(postUrl, report_data);
    }
  }
};
const post_reapst = (url, data) => {
  curItem.value.loading = true;
  proxy
    .$post(url, data)
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        showToast(
          `${url.includes("post_apply_cancel") ? "取消报餐" : "报餐"}成功`
        );
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    })
    .finally(() => {
      reportNumber.value = null;
      getWindow();
    });
};
const onDateSelect = (day) => {
  let dateStr = dayjs(day).format("YYYY-MM-DD");
  nowDate.value = dateStr;
  curDateIndex.value = dateLs.findIndex(
    (el) => el.dayjs.format("YYYY-MM-DD") === dateStr
  );
  getWindow();
};
const onRepastClick = (i) => {
  curMealtimeIndex.value = i;
};
var handleDate = () => {
  let dateLs = [];
  let week = ["日", "一", "二", "三", "四", "五", "六"];
  let dateMap = ["今天", "明天"];
  nowDate.value = dayjs().format("YYYY-MM-DD");
  for (let i = 0; i < 6; i++) {
    let date = dayjs().add(i, "day");
    dateLs.push({
      week: "周" + week[date.day()],
      date: dateMap[i] || date.format("MM-DD"),
      dayjs: date,
    });
  }
  return dateLs;
};
let dateLs = handleDate();
// emit 控制显示
const emit = defineEmits(["isStatus"]);
const dingdingChange = (e) => {
  proxy
    .$post(config.curl.dingdingChange, {
      id: e,
    })
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        let dininghall = option.find((el) => el.value == e);
        app.loginData.dininghall_title = dininghall.text;
        app.loginData.dininghall_id = e;
        app.loginData.token = res.token;
        app.loginData.permission = res.permission;
        // const routerListTow = set_permission(router_list);
        // window.sessionStorage.setItem(
        //   "routerList",
        //   JSON.stringify(routerListTow)
        // );
        emit("isStatus", false);
        // setTimeout(function () {
        //   // router.go(0);
        //   // location.reload();
        //   window.location.reload()
        //   // window.history.go(0)

        // }, 100);
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    })
    .finally(() => { });
};
const getDininghall = () => {
  proxy
    .$post(config.curl.ls, config.postData)
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        res.forEach((element) => {
          element.text = element.title;
          element.value = element.id;
        });
        option = res;

        curDininghall.value = app.loginData.dininghall_id;
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    })
    .finally(() => { });
};
let controller = null;

const getWindow = () => {
  windowData.value = [];
  if (controller) {
    controller.abort();
  }
  controller = new AbortController();
  proxy
    .$post(
      reportOrOrder.value ? "order/index" : config.curl.window,
      {
        date: nowDate.value,
      },
      {
        signal: controller.signal,
      }
    )
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        res.forEach((item) => {
          item.windows.forEach((el) => {
            ({
              repast_start_time: el.repast_start_time,
              repast_end_time: el.repast_end_time,
            } = item);
            el.repast_title = item.repast_title;
            if (el.dishess && typeof el.dishess == "string") {
              el.dishess = JSON.parse(el.dishess);
            }
          });
        });
        handleMealTime(nowDate.value, res);
        windowData.value = res;
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      // console.log(err);
      if (err.code != "ERR_CANCELED") {
        showToast(err);
      }
    })
    .finally(() => {
      curItem.value && (curItem.value.loading = false);
    });
};
const handleMealTime = (nowStrDate, res) => {
  let index = [];
  let timeoutIndex = [];
  let current_time = dayjs().format("HHmmss") * 1;
  let pre_start = null;
  let pre_end = null;
  res.findIndex((el, i) => {
    let start = el.repast_start_time.split(":").join("") * 1;
    let end = el.repast_end_time.split(":").join("") * 1;
    if (end < start) {
      timeoutIndex.push(i);
      end += 240000;
    }
    // console.log("计算餐时------------->", start, current_time, end);
    if (start < current_time && current_time < end) {
      index.push(i);
    } else if (pre_end < current_time && current_time < start) {
      index.push(i);
    } else if (!pre_end && current_time < start) {
      index.push(i);
    } else {
      pre_start = start;
      pre_end = end;
    }
  });
  let curMealIndex = index[0];
  if (!windowData.value.length) {
    !curMealIndex
      ? (curMealtimeIndex.value = 0)
      : (curMealtimeIndex.value = curMealIndex);
  }
  res.forEach((el, i) => {
    if (index.includes(i)) {
      el.timeOutText = `剩余${Math.abs(
        (timeoutIndex.includes(i)
          ? dayjs(`${nowStrDate} ${el.repast_end_time}`).add(1, "day")
          : dayjs(`${nowStrDate} ${el.repast_end_time}`)
        ).diff(dayjs(), "minute") / 60
      ).toFixed(1)}小时`;
    } else {
      el.timeOutText = "暂停就餐";
      el.notShowReport = true;
    }
    if (dayjs().isBefore(dayjs(nowStrDate))) {
      el.timeOutText = "暂停就餐";
      el.notShowReport = true;
    }
    el.repast_start_time = dayjs(
      `${nowStrDate} ${el.repast_start_time}`
    ).format("HH:mm");
    el.repast_end_time = dayjs(`${nowStrDate} ${el.repast_end_time}`).format(
      "HH:mm"
    );
    el.windows.forEach((window) => {
      window.repast_start_time = el.repast_start_time;
      window.repast_end_time = el.repast_end_time;
    });
  });
};
getDininghall();
getWindow();
</script>
<style lang="scss">
.wrapper-home {
  width: 100%;
  min-height: 100vh;

  overflow: hidden;

  .top-block {
    background: #007fff;
    color: #fff;
    overflow: hidden;
    position: relative;
    padding: 0;
    margin-bottom: 8px;

    .dininghall-date {
      display: flex;
      align-items: center;
      background: #007fff;
      padding-left: 8px;

      .van-dropdown-menu {
        flex: 1;

        .van-dropdown-menu__bar {
          background: #007fff;
          box-shadow: none !important;

          .van-dropdown-menu__title {
            color: #fff;
          }

          .van-dropdown-menu__title:after {
            border-color: transparent transparent #fff #fff;
          }

          .van-dropdown-menu__item {
            justify-content: flex-start;
          }
        }

        .van-popup--top {
          left: 16px;
          width: auto;
          right: 16px;
        }
      }

      .van-icon {
        width: 50px;
        text-align: center;
      }
    }

    .date-block {
      font-size: 12px;
      text-align: center;
      width: calc((100% - 5px * 14) / 7);
      height: 56px;
      border-radius: 10px;
      margin: 5px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .date {
      position: relative;
    }

    .date:after {
      position: absolute;
      left: 50%;
      bottom: -4px;
      margin-top: -5px;
      border: 3px solid;
      border-color: transparent transparent #fff #fff;
      transform: rotate(-45deg);
      opacity: 0.8;
      content: "";
    }

    .date:before {
      position: absolute;
      left: -2px;
      top: 50%;
      transform: translateY(-50%);
      height: 45%;
      border-left: 1px solid #fff;
      content: "";
    }

    .cur-date {
      background: #fff !important;
      color: #007fff !important;
    }

    .order,
    .report {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 10px;
      margin-right: 12px;
      background: #007fff;
    }
  }

  .window-info {
    position: relative;
    border-radius: 8px;
    margin-top: 8px;

    .left {
      position: absolute;
      left: 0;
      top: 0;
      width: 80px;
      // height: 60px;
      // width: 74px;
      overflow: scroll;
      height: calc(100vh - 100px);
      padding-bottom: 20px;

      .repast-time {
        height: 87px;
        width: 100%;
        display: flex;

        .select-line {
          width: 3px;
          background: #007fff;
        }

        .text-info {
          flex: 1;
          display: flex;
          align-items: center;
          flex-direction: column;
          justify-content: center;
          font-size: 14px;

          .repast-text {
            font-size: 14px;
            margin-top: 15px;
            text-align: center;
          }
        }
      }

      .active-repast {
        background: #fff;
        border-radius: 0 8px 8px 0;
      }
    }

    .right {
      margin-left: 74px;
      height: calc(100vh - 222px);
      overflow: scroll;
      padding-bottom: 20px;

      .window-dish {
        margin-left: 8px;
        background: #fff;
        border-radius: 8px;
        margin-bottom: 8px;
        margin-right: 8px;
      }

      .title-block {
        font-size: 12px;
        padding: 18px 8px 12px;
        // background: rgba(245, 247, 250, 0.88);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .dish-block {
        font-size: 10px;
        color: rgba(23, 26, 29, 0.4);
        margin: 0 8px;
        padding: 12px 0;
        border-top: 1px solid #f6f6f6;
        border-bottom: 1px solid #f6f6f6;
        overflow: scroll;
        white-space: nowrap;

        .dish-item {
          text-align: center;
          width: 50px;
          margin-right: 8px;
          display: inline-block;

          .title {
            margin-top: 2px;
            width: 100%;
            overflow: auto;
          }
        }
      }

      .bottom-block {
        color: rgba(23, 26, 29, 0.24);
        font-size: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 8px;
        padding: 12px 0;
      }
    }
  }

  .dish-detail {
    width: 80vw;
    border-radius: 4px;
    overflow: hidden;

    .title {
      padding: 16px 16px 0;
      font-size: 17px;
    }

    .dish-title {
      max-width: 100%;
      overflow: hidden;
      word-break: break-all;
    }
  }

  .van-divider {
    margin: 14px 0 0;
  }
}
</style>
