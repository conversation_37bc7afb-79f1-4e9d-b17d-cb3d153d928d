<template>
  <div class="wrapper">
    <van-field v-model="data.text" is-link :readonly="data.readonly" :name="config.key" :label="config.label"
      :placeholder="config.placeholder" input-align="right" :rules="config.rules" :disabled="config.disabled"
      @click="data.showCalendar = true" />
    <van-calendar v-model:show="data.showCalendar" :default-date="data['default-date']" :type="config.type"
      @confirm="onConfirm" />
  </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance } from "vue";
import { deepAssign } from "@/untils";
import { showToast } from "vant";
const { proxy } = getCurrentInstance();

let config = {
  // 基础配置
  label: "日历选择",       // 字段标签 (字符串) - 显示在输入框左侧的标签文字
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"start_date", "end_date"
  placeholder: "请选择",    // 占位符 (字符串) - 未选择日期时显示的提示文字
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用选择, false: 可正常选择
  rules: [],               // 验证规则 (数组) - 表单验证规则配置

  // 日历类型配置
  type: "multiple",        // 日历选择类型 (字符串) - "single": 单选, "multiple": 多选, "range": 范围选择

  // 样式配置 (暂未使用)
  size: 22,                // 尺寸 (数字) - 预留配置，暂未使用
  loading: false,          // 加载状态 (布尔值) - 预留配置，暂未使用
};
const props = defineProps({
  config: Object,
  form: Object,
});
props.config && deepAssign(config, props.config);
let data = reactive({
  showCalendar: false,
  value: "",
  text: "",
  readonly: false,
  "default-date": null
});
if (props.form[config.key]) {
  data.text = props.form[config.key].toString();
  data.value = props.form[config.key];
  if (config.type != 'single') {
    data["default-date"] = props.form[config.key].map(el => new Date(el))
  } else {
    data["default-date"] = new Date(props.form[config.key])
  }

}
const handleDate = (date) => {
  if (Array.isArray(date)) {
    data.value = date.map(
      (el) => `${el.getFullYear()}-${el.getMonth() + 1}-${el.getDate()}`
    );
  } else {
    data.value = `${date.getFullYear()}-${date.getMonth() + 1
      }-${date.getDate()}`;
  }
};
const onConfirm = (date) => {
  data.showCalendar = false;
  handleDate(date);
  props.form[config.key] = data.value;
  data.text = data.value.toString();

};
</script>
<style lang="scss" scoped>
.wrapper {}
</style>
