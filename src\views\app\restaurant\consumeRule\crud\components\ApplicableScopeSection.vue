<template>
  <div class="applicable-scope-section">
    <!-- 适用人员 -->
    <div class="form-group">
      <van-cell-group inset>
        <van-cell title="适用人员" />
        <van-radio-group v-model="localFormData.apply_personnel_type" :disabled="loading">
          <van-cell clickable @click="localFormData.apply_personnel_type = 0">
            <template #title>
              <van-radio :name="0">全部人员</van-radio>
            </template>
          </van-cell>
          <van-cell clickable @click="localFormData.apply_personnel_type = 1">
            <template #title>
              <van-radio :name="1">指定人员</van-radio>
            </template>
          </van-cell>
        </van-radio-group>
        
        <!-- 指定人员时显示 -->
        <template v-if="localFormData.apply_personnel_type === 1">
          <van-field
            v-model="localFormData.internal_staffs"
            label="内部员工"
            placeholder="请选择内部员工"
            readonly
            :disabled="loading"
            @click="handleSelectInternalStaffs"
          />
          <van-field
            v-model="localFormData.external_staffs"
            label="外部员工"
            placeholder="请选择外部员工"
            readonly
            :disabled="loading"
            @click="handleSelectExternalStaffs"
          />
        </template>
      </van-cell-group>
    </div>

    <!-- 适用餐时 -->
    <div class="form-group">
      <van-cell-group inset>
        <van-cell title="适用餐时" />
        <van-radio-group v-model="localFormData.apply_mealtime_type" :disabled="loading">
          <van-cell clickable @click="localFormData.apply_mealtime_type = 0">
            <template #title>
              <van-radio :name="0">全部餐时</van-radio>
            </template>
          </van-cell>
          <van-cell clickable @click="localFormData.apply_mealtime_type = 1">
            <template #title>
              <van-radio :name="1">指定餐时</van-radio>
            </template>
          </van-cell>
        </van-radio-group>
        
        <!-- 指定餐时时显示 -->
        <van-field
          v-if="localFormData.apply_mealtime_type === 1"
          v-model="mealtimeText"
          label="指定餐时"
          placeholder="请选择餐时"
          readonly
          required
          :disabled="loading"
          @click="showMealtimePicker = true"
        />
      </van-cell-group>
    </div>

    <!-- 适用档口 -->
    <div class="form-group">
      <van-cell-group inset>
        <van-cell title="适用档口" />
        <van-radio-group v-model="localFormData.apply_stall_type" :disabled="loading">
          <van-cell clickable @click="localFormData.apply_stall_type = 0">
            <template #title>
              <van-radio :name="0">全部档口</van-radio>
            </template>
          </van-cell>
          <van-cell clickable @click="localFormData.apply_stall_type = 1">
            <template #title>
              <van-radio :name="1">指定档口</van-radio>
            </template>
          </van-cell>
        </van-radio-group>
        
        <!-- 指定档口时显示 -->
        <van-field
          v-if="localFormData.apply_stall_type === 1"
          v-model="stallText"
          label="指定档口"
          placeholder="请选择档口"
          readonly
          required
          :disabled="loading"
          @click="showStallPicker = true"
        />
      </van-cell-group>
    </div>

    <!-- 餐时选择器 -->
    <van-popup v-model:show="showMealtimePicker" position="bottom" :style="{ height: '50vh' }">
      <van-picker
        :columns="mealtimeColumns"
        title="选择餐时"
        @confirm="handleMealtimeConfirm"
        @cancel="showMealtimePicker = false"
      />
    </van-popup>

    <!-- 档口选择器 -->
    <van-popup v-model:show="showStallPicker" position="bottom" :style="{ height: '50vh' }">
      <van-picker
        :columns="stallColumns"
        title="选择档口"
        @confirm="handleStallConfirm"
        @cancel="showStallPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getCurrentInstance } from 'vue'
import { showToast } from 'vant'

const { proxy } = getCurrentInstance()

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:formData'])

// 本地表单数据
const localFormData = computed({
  get: () => props.formData,
  set: (value) => emit('update:formData', value)
})

// 选择器状态
const showMealtimePicker = ref(false)
const showStallPicker = ref(false)

// 选择器数据
const mealtimeColumns = ref([])
const stallColumns = ref([])

// 显示文本
const mealtimeText = ref('')
const stallText = ref('')

// 获取餐时列表
const fetchMealtimes = async () => {
  try {
    const res = await proxy.$get('/mealtime/get_all', {})
    if (res.code === 200) {
      mealtimeColumns.value = res.data.map(item => ({
        text: `${item.title} (${item.start_time}-${item.end_time})`,
        value: item.id,
        ...item
      }))
    }
  } catch (error) {
    console.error('获取餐时列表失败:', error)
  }
}

// 获取档口列表
const fetchStalls = async () => {
  try {
    const res = await proxy.$get('/stall/get_all', {})
    if (res.code === 200) {
      stallColumns.value = res.data.map(item => ({
        text: item.title,
        value: item.id,
        ...item
      }))
    }
  } catch (error) {
    console.error('获取档口列表失败:', error)
  }
}

// 处理餐时确认
const handleMealtimeConfirm = ({ selectedOptions }) => {
  const selected = selectedOptions[0]
  localFormData.value.mealtimes = selected.value
  mealtimeText.value = selected.text
  showMealtimePicker.value = false
}

// 处理档口确认
const handleStallConfirm = ({ selectedOptions }) => {
  const selected = selectedOptions[0]
  localFormData.value.stalls = selected.value
  stallText.value = selected.text
  showStallPicker.value = false
}

// 处理选择内部员工
const handleSelectInternalStaffs = () => {
  showToast('选择内部员工功能待实现')
}

// 处理选择外部员工
const handleSelectExternalStaffs = () => {
  showToast('选择外部员工功能待实现')
}

onMounted(() => {
  fetchMealtimes()
  fetchStalls()
})
</script>

<style lang="scss" scoped>
.applicable-scope-section {
  .form-group {
    margin-bottom: 12px;
  }
  
  .van-cell-group {
    margin: 0 16px;
  }
}
</style>
