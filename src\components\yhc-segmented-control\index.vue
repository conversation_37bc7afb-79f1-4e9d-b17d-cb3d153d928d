<template>
  <div class="wrapper">
    <!-- 使用 van-cell 保持与其他表单组件一致的布局 -->
    <van-cell center :title="config.label" :title-style="`color:${config.disabled ? '#c8c9cc' : ''}`">
      <template #value>
        <!-- 分段器主体 -->
        <div class="segmented-wrapper" :class="{
          'segmented-disabled': config.disabled,
          'segmented-block': config.block
        }" :style="wrapperStyle">
          <div class="segmented-item" v-for="(item, index) in options" :key="getItemKey(item, index)" :class="{
            'segmented-item-active': isActive(item, index),
            'segmented-item-disabled': isItemDisabled(item)
          }" :style="getItemStyle(item, index)" @click="handleItemClick(item, index)">
            <!-- 图标显示 -->
            <van-icon v-if="item[config.iconKey]" :name="item[config.iconKey]" :size="config.iconSize"
              class="segmented-icon" />

            <!-- 文本显示 -->
            <span class="segmented-text">
              {{ item[config.textKey] || item }}
            </span>

            <!-- 徽标显示 -->
            <van-badge v-if="item[config.badgeKey]" :content="item[config.badgeKey]" class="segmented-badge" />
          </div>

          <!-- 滑动指示器 -->
          <div v-if="config.showIndicator && !config.disabled" class="segmented-indicator" :style="indicatorStyle">
          </div>
        </div>
      </template>
    </van-cell>

    <!-- 描述文本 -->
    <div class="segmented-description" v-if="config.description">
      {{ config.description }}
    </div>

    <!-- 子表单容器 -->
    <div class="child-container">
      <!-- 子表单项 - 使用 map 配置时显示（带有额外的安全检查） -->
      <template v-if="config.child && config.child.map">
        <div class="child-wrapper"
          v-if="config.child.form && Array.isArray(config.child.form) && config.child.form.length > 0">
          <div class="child-item" v-for="(childItem, i) in config.child.form" :key="childItem.key + i">
            <yhc-form-item v-if="childItem && childItem.key" :key="childItem.key" :config="childItem"
              :form="props.form" />
          </div>
        </div>
      </template>

      <!-- 子表单项 - 当选中值与 showMode 相等时显示 -->
      <template v-if="config.child && config.child.form && config.child.showMode !== undefined">
        <div class="child-wrapper" v-if="currentValue === config.child.showMode || config.child.showMode === 'show'">
          <div class="child-item" v-for="(childItem, i) in config.child.form" :key="childItem.key + i">
            <yhc-form-item :key="childItem.key" :config="childItem" :form="props.form" />
          </div>
        </div>
      </template>

      <!-- 子表单项 - 当选中值与 showMode 不相等时显示 -->
      <template v-if="config.child && config.child.formChild && config.child.showMode !== undefined">
        <div class="child-wrapper" v-if="currentValue !== config.child.showMode || config.child.showMode === 'hide'">
          <div class="child-item" v-for="(childItem, i) in config.child.formChild" :key="childItem.key + i">
            <yhc-form-item :key="childItem.key" :config="childItem" :form="props.form" />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, getCurrentInstance, nextTick, onMounted, shallowReactive } from "vue";
import { deepAssign } from "@/untils";

const { proxy } = getCurrentInstance();

// 默认配置
let config = {
  // 基础配置
  label: "",               // 标题文本 (字符串) - 显示在分段控制器上方的标签文字
  key: "",                 // 表单字段key (字符串) - 表单数据中的字段名，如"tab_type", "mode"
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用整个控制器, false: 可正常操作
  rules: [],               // 验证规则 (数组) - 表单验证规则配置
  description: "",         // 描述文本 (字符串) - 显示在控制器下方的说明文字

  // 布局配置
  block: false,            // 是否为块级元素 (布尔值) - true: 占满容器宽度, false: 内容宽度
  multiple: false,         // 是否支持多选 (布尔值) - true: 支持选择多个选项, false: 单选模式

  // 数据配置
  options: [],             // 选项数据 (数组) - 分段选项，格式: [{text: "选项1", value: "1", icon: "icon-name"}]
  textKey: "text",         // 文本字段key (字符串) - 选项显示文字对应的数据字段名
  valueKey: "value",       // 值字段key (字符串) - 选项值对应的数据字段名
  iconKey: "icon",         // 图标字段key (字符串) - 选项图标对应的数据字段名
  badgeKey: "badge",       // 徽标字段key (字符串) - 选项徽标对应的数据字段名
  disabledKey: "disabled", // 禁用字段key (字符串) - 选项禁用状态对应的数据字段名

  // 样式配置
  size: "small",          // 尺寸 (字符串) - "small": 小尺寸, "normal": 正常尺寸, "large": 大尺寸
  type: "default",         // 类型 (字符串) - "default": 默认, "primary": 主要, "success": 成功, "warning": 警告, "danger": 危险
  round: false,            // 是否圆角 (布尔值) - true: 圆角样式, false: 直角样式
  showIndicator: true,     // 是否显示滑动指示器 (布尔值) - true: 显示选中指示器, false: 不显示
  iconSize: "16px",        // 图标尺寸 (字符串) - 选项图标的大小，如"16px", "20px"

  // 颜色配置
  activeColor: "#171A1D",     // 激活颜色 (字符串) - 选中状态的文字颜色
  inactiveColor: "#171A1D", // 非激活颜色 (字符串) - 未选中状态的文字颜色
  backgroundColor: "#e5e6e8", // 背景颜色 (字符串) - 控制器的背景颜色

  // 事件配置
  onChange: null,          // 变化回调函数 (函数) - 选项改变时的回调函数

  // 子表单配置 (高级功能)
  child: {
    map: false,            // 是否使用映射模式 (布尔值) - true: 根据选中值动态显示子表单
    showMode: undefined,   // 显示模式值 (字符串/数字) - 当选中此值时显示子表单
    form: [],              // 子表单字段 (数组) - 当选中特定值时显示的表单字段
    formChild: [],         // 备用子表单字段 (数组) - 当选中非showMode值时显示的表单字段
  }
};

// Props定义
const props = defineProps({
  config: Object,
  form: Object,
});

// 合并配置
props.config && deepAssign(config, props.config);

// 初始化子表单配置 - 确保在任何操作之前都已正确初始化
if (config.child && config.child.map) {
  // 确保 child.form 存在且是响应式数组
  if (!config.child.form || !Array.isArray(config.child.form)) {
    config.child.form = shallowReactive([]);
  } else {
    config.child.form = shallowReactive(config.child.form);
  }
}

// 响应式数据
const activeIndex = ref(0);
const indicatorLeft = ref(0);
const indicatorWidth = ref(0);

// 计算属性
const options = computed(() => {
  return config.options || [];
});

const wrapperStyle = computed(() => {
  const styles = {
    backgroundColor: config.backgroundColor,
  };

  if (config.round) {
    styles.borderRadius = "20px";
  }

  return styles;
});

// 当前选中的值 - 用于子表单条件显示
const currentValue = computed(() => {
  return props.form[config.key];
});

const indicatorStyle = computed(() => {
  return {
    left: `${indicatorLeft.value}px`,
    width: `${indicatorWidth.value}px`,
    backgroundColor: "#ffffff", // 指示器使用白色背景
    borderRadius: config.round ? "16px" : "4px",
  };
});

// 方法定义
const getItemKey = (item, index) => {
  if (typeof item === "object" && item[config.valueKey] !== undefined) {
    return item[config.valueKey];
  }
  return index;
};

const isActive = (item, index) => {
  if (config.multiple) {
    const currentValue = props.form[config.key] || [];
    const itemValue = getItemValue(item, index);
    return currentValue.includes(itemValue);
  } else {
    const currentValue = props.form[config.key];
    const itemValue = getItemValue(item, index);
    return currentValue === itemValue;
  }
};

const isItemDisabled = (item) => {
  if (config.disabled) return true;
  if (typeof item === "object" && item[config.disabledKey]) {
    return item[config.disabledKey];
  }
  return false;
};

const getItemValue = (item, index) => {
  if (typeof item === "object" && item[config.valueKey] !== undefined) {
    return item[config.valueKey];
  }
  return typeof item === "string" ? item : index;
};

const getItemStyle = (item, index) => {
  const styles = {
    color: isActive(item, index) ? config.activeColor : config.inactiveColor,
  };

  // 尺寸样式
  if (config.size === "small") {
    styles.padding = "4px 8px";
    styles.fontSize = "12px";
  } else if (config.size === "large") {
    styles.padding = "12px 16px";
    styles.fontSize = "16px";
  } else {
    styles.padding = "8px 12px";
    styles.fontSize = "14px";
  }

  return styles;
};

const handleItemClick = (item, index) => {
  if (config.disabled || isItemDisabled(item)) {
    return;
  }

  const itemValue = getItemValue(item, index);

  if (config.multiple) {
    // 多选模式
    const currentValue = props.form[config.key] || [];
    const newValue = [...currentValue];
    const valueIndex = newValue.indexOf(itemValue);

    if (valueIndex > -1) {
      newValue.splice(valueIndex, 1);
    } else {
      newValue.push(itemValue);
    }

    props.form[config.key] = newValue;
  } else {
    // 单选模式
    props.form[config.key] = itemValue;
    activeIndex.value = index;
    updateIndicator();
  }

  // 触发变化事件
  if (typeof config.onChange === "function") {
    config.onChange(props.form[config.key], item, index);
  }

  // 触发自定义事件
  emits("change", props.form[config.key], item, index);
};

const updateIndicator = () => {
  if (!config.showIndicator || config.multiple) return;

  nextTick(() => {
    const wrapper = proxy.$el?.querySelector(".segmented-wrapper");
    if (!wrapper) return;

    const activeItem = wrapper.querySelector(".segmented-item-active");

    if (activeItem) {
      const wrapperRect = wrapper.getBoundingClientRect();
      const activeRect = activeItem.getBoundingClientRect();

      indicatorLeft.value = activeRect.left - wrapperRect.left;
      indicatorWidth.value = activeRect.width;
    } else {
      // 如果没有找到激活项，根据当前选中值计算位置
      const items = wrapper.querySelectorAll(".segmented-item");
      const currentValue = props.form[config.key];

      for (let i = 0; i < items.length; i++) {
        const itemValue = getItemValue(options.value[i], i);
        if (itemValue === currentValue) {
          const wrapperRect = wrapper.getBoundingClientRect();
          const itemRect = items[i].getBoundingClientRect();

          indicatorLeft.value = itemRect.left - wrapperRect.left;
          indicatorWidth.value = itemRect.width;
          break;
        }
      }
    }
  });
};



// 安全的子表单初始化函数
const initChildForm = () => {
  if (config.child && config.child.map) {
    // 确保 child.form 存在且是响应式数组
    if (!config.child.form || !Array.isArray(config.child.form)) {
      config.child.form = shallowReactive([]);
    }
  }
};

// 安全的子表单更新函数
const updateChildForm = (currentValue) => {
  if (!config.child || !config.child.map || currentValue === undefined) {
    return;
  }

  // 确保子表单已初始化
  initChildForm();

  // 安全地清空当前子表单
  if (config.child.form && Array.isArray(config.child.form)) {
    config.child.form.splice(0);

    // 如果当前值在 map 中有对应的子表单配置，则添加到 form 中
    if (config.child.map[currentValue]) {
      config.child.form.push(...config.child.map[currentValue]);
    }
  }
};

// 初始化
const init = () => {
  if (!config.multiple && props.form[config.key] !== undefined) {
    const currentValue = props.form[config.key];
    const index = options.value.findIndex((item, idx) => {
      return getItemValue(item, idx) === currentValue;
    });
    if (index > -1) {
      activeIndex.value = index;
    }
  }

  // 设置默认值
  if (props.form[config.key] === undefined) {
    if (config.multiple) {
      props.form[config.key] = [];
    } else if (options.value.length > 0) {
      props.form[config.key] = getItemValue(options.value[0], 0);
      activeIndex.value = 0;
    }
  }

  // 安全地初始化和更新子表单
  if (props.form[config.key] !== undefined) {
    updateChildForm(props.form[config.key]);
  }

  // 延迟更新指示器，确保DOM已渲染
  setTimeout(() => {
    updateIndicator();
  }, 50);
};

// 事件定义
const emits = defineEmits(["change"]);

// 监听器
watch(
  () => props.form[config.key],
  (newValue) => {
    if (!config.multiple) {
      // 更新activeIndex
      const index = options.value.findIndex((item, idx) => {
        return getItemValue(item, idx) === newValue;
      });
      if (index > -1) {
        activeIndex.value = index;
      }
      // 延迟更新指示器
      nextTick(() => {
        setTimeout(() => {
          updateIndicator();
        }, 10);
      });
    }

    // 处理子表单的动态显示 - 使用安全的更新函数
    updateChildForm(newValue);

    // 强制更新视图
    if (config.child && config.child.map && newValue !== undefined) {
      nextTick(() => {
        proxy.$forceUpdate();
      });
    }
  }
);

watch(
  () => options.value,
  () => {
    init();
  },
  { immediate: true }
);

// 组件挂载后确保指示器正确显示和子表单正确初始化
onMounted(() => {
  // 确保子表单在组件挂载后也正确初始化
  if (config.child && config.child.map && props.form[config.key] !== undefined) {
    updateChildForm(props.form[config.key]);
  }

  // 多次尝试更新指示器，确保DOM完全渲染
  setTimeout(() => updateIndicator(), 50);
  setTimeout(() => updateIndicator(), 100);
  setTimeout(() => updateIndicator(), 200);
});
</script>

<style lang="scss" scoped>
.wrapper {
  // 与其他表单组件保持一致的样式

  // 主容器：尺寸 117px × 28px，背景 #E5E6E8，圆角 4px
  .segmented-wrapper {
    position: relative;
    display: flex;
    width: 117px;
    height: 28px;
    background-color: #e5e6e8;
    border-radius: 4px;
    padding: 2px;
    overflow: hidden;
    margin-left: auto;

    &.segmented-disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &.segmented-block {
      width: 100%;
      max-width: none;
    }
  }

  // 选项卡样式：每个 56px × 24px，圆角 2px，内边距 2px 4px
  .segmented-item {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 12px;
    color: #171A1D;
    line-height: 20px;
    padding: 2px 4px;
    box-sizing: border-box;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 2px;
    z-index: 1;
    white-space: nowrap;

    &:hover:not(.segmented-item-disabled) {
      color: #171A1D;
    }

    &.segmented-item-active {
      color: #171A1D !important;
      font-weight: 500;
    }

    &.segmented-item-disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  // 分隔线：位置 top:7.03px, left:58px，尺寸 1px × 12px，颜色 rgba(126, 134, 142, 0.16)
  .segmented-icon {
    margin-right: 4px;
    font-size: 16px;
  }

  .segmented-text {
    white-space: nowrap;
  }

  .segmented-badge {
    margin-left: 4px;
  }

  // 指示器样式：白色背景，圆角 2px
  .segmented-indicator {
    position: absolute;
    top: 2px;
    bottom: 2px;
    background-color: #ffffff;
    border-radius: 2px;
    transition: all 0.3s ease;
    z-index: 0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transform: translateZ(0);
  }

  .segmented-description {
    font-size: 12px;
    color: #969799;
    margin-top: 4px;
    line-height: 1.4;
    text-align: right;
  }

  // 子表单容器样式
  .child-container {
    margin-top: 0;
  }

  // 子表单样式
  .child-wrapper {
    animation: fadeIn 0.3s ease-in-out;

    .child-item {
      background: transparent;
    }
  }

  // 添加淡入动画
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(5px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
</style>
